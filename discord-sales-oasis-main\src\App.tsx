import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Login from "./pages/Login";
import Success from "./pages/Success";
import Configure from "./pages/Configure";
import BotActive from "./pages/BotActive";
import Dashboard from "./pages/Dashboard";
import ExtractNodexPay from "./pages/ExtractNodexPay";
import NotFound from "./pages/NotFound";
import AuthCallback from "./pages/AuthCallback";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/login" element={<Login />} />
          <Route path="/success" element={<Success />} />
          <Route path="/auth/callback" element={<AuthCallback />} />
          <Route path="/callback" element={<AuthCallback />} />
          <Route path="/configure" element={<Configure />} />
          <Route path="/bot-active" element={<BotActive />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/dashboard/:urlToken" element={<Dashboard />} />
          <Route path="/extrato" element={<ExtractNodexPay />} />
          <Route path="/extrato/:urlToken" element={<ExtractNodexPay />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
