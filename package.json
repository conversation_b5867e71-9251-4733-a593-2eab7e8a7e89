{"name": "discord-sales-system", "version": "2.0.0", "description": "Sistema completo de vendas Discord com bots personalizados", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "build-frontend": "cd discord-sales-oasis-main && npm run build", "install-frontend": "cd discord-sales-oasis-main && npm install", "setup": "node setup.js", "quick-setup": "npm install && npm run install-frontend && npm run build-frontend", "test": "node test-system.js"}, "dependencies": {"@discordjs/rest": "^1.1.0", "@supabase/supabase-js": "^2.50.1", "axios": "^1.6.5", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "discord.js": "^14.14.1", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "nodemailer": "^7.0.3", "nodemon": "^3.1.10", "stripe": "^18.2.1"}, "keywords": ["discord", "bot", "sales", "vendas", "automation"], "author": "Discord Sales System", "license": "MIT"}