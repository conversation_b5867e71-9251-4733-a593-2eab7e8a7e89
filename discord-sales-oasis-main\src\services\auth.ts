// Configurações do Discord OAuth
const DISCORD_CLIENT_ID = '1320506893893734400'; // Substitua pelo seu Client ID
const DISCORD_REDIRECT_URI = window.location.origin + '/auth/callback';
const DISCORD_SCOPE = 'identify email';

export interface DiscordUser {
  id: string;
  username: string;
  discriminator: string;
  avatar: string | null;
  email: string;
  verified: boolean;
}

export class AuthService {
  private static instance: AuthService;
  private user: DiscordUser | null = null;

  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  constructor() {
    // Verificar se há token salvo no localStorage
    this.loadUserFromStorage();
  }

  // Iniciar login com Discord
  loginWithDiscord(): void {
    const authUrl = new URL('https://discord.com/api/oauth2/authorize');
    authUrl.searchParams.set('client_id', DISCORD_CLIENT_ID);
    authUrl.searchParams.set('redirect_uri', DISCORD_REDIRECT_URI);
    authUrl.searchParams.set('response_type', 'code');
    authUrl.searchParams.set('scope', DISCORD_SCOPE);
    
    window.location.href = authUrl.toString();
  }

  // Processar callback do Discord
  async handleDiscordCallback(code: string): Promise<DiscordUser> {
    try {
      // Trocar código por token
      const tokenResponse = await fetch('/api/auth/discord/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code, redirect_uri: DISCORD_REDIRECT_URI }),
      });

      if (!tokenResponse.ok) {
        throw new Error('Erro ao obter token do Discord');
      }

      const { access_token } = await tokenResponse.json();

      // Obter dados do usuário
      const userResponse = await fetch('https://discord.com/api/users/@me', {
        headers: {
          Authorization: `Bearer ${access_token}`,
        },
      });

      if (!userResponse.ok) {
        throw new Error('Erro ao obter dados do usuário');
      }

      const userData = await userResponse.json();
      
      this.user = {
        id: userData.id,
        username: userData.username,
        discriminator: userData.discriminator,
        avatar: userData.avatar,
        email: userData.email,
        verified: userData.verified,
      };

      // Salvar no localStorage
      localStorage.setItem('discord_user', JSON.stringify(this.user));
      localStorage.setItem('discord_token', access_token);

      return this.user;
    } catch (error) {
      console.error('Erro no callback do Discord:', error);
      throw error;
    }
  }

  // Carregar usuário do localStorage
  private loadUserFromStorage(): void {
    const savedUser = localStorage.getItem('discord_user');
    if (savedUser) {
      try {
        this.user = JSON.parse(savedUser);
      } catch (error) {
        console.error('Erro ao carregar usuário do storage:', error);
        this.logout();
      }
    }
  }

  // Obter usuário atual
  getCurrentUser(): DiscordUser | null {
    return this.user;
  }

  // Verificar se está logado
  isAuthenticated(): boolean {
    return this.user !== null && localStorage.getItem('discord_token') !== null;
  }

  // Logout
  logout(): void {
    this.user = null;
    localStorage.removeItem('discord_user');
    localStorage.removeItem('discord_token');
  }

  // Obter avatar URL
  getAvatarUrl(size: number = 128): string | null {
    if (!this.user) return null;
    
    if (this.user.avatar) {
      return `https://cdn.discordapp.com/avatars/${this.user.id}/${this.user.avatar}.png?size=${size}`;
    }
    
    // Avatar padrão baseado no discriminator
    const defaultAvatarNumber = parseInt(this.user.discriminator) % 5;
    return `https://cdn.discordapp.com/embed/avatars/${defaultAvatarNumber}.png`;
  }

  // Obter nome de exibição
  getDisplayName(): string {
    if (!this.user) return '';
    return `${this.user.username}#${this.user.discriminator}`;
  }
}
