const { createClient } = require('@supabase/supabase-js');
const crypto = require('crypto');

class SupabaseLicenseManager {
    constructor() {
        // Configuração do Supabase
        this.supabaseUrl = process.env.SUPABASE_URL;
        this.supabaseKey = process.env.SUPABASE_SERVICE_KEY; // Use service key para bypass RLS
        
        if (!this.supabaseUrl || !this.supabaseKey) {
            throw new Error('Configurações do Supabase não encontradas no .env');
        }
        
        this.supabase = createClient(this.supabaseUrl, this.supabaseKey);
        console.log('✅ Supabase License Manager inicializado');
    }

    /**
     * Gerar chave de licença única
     */
    generateLicenseKey() {
        const part1 = crypto.randomBytes(2).toString('hex').toUpperCase();
        const part2 = crypto.randomBytes(2).toString('hex').toUpperCase();
        const part3 = crypto.randomBytes(2).toString('hex').toUpperCase();
        const part4 = crypto.randomBytes(2).toString('hex').toUpperCase();
        
        return `NODEX-${part1}-${part2}-${part3}-${part4}`;
    }

    /**
     * Criar nova licença
     */
    async createLicense(customerEmail, plan, stripeSessionId = null, stripeCustomerId = null) {
        try {
            const licenseKey = this.generateLicenseKey();
            const planName = this.getPlanName(plan);
            const expiresAt = this.calculateExpiry(plan);

            const licenseData = {
                key: licenseKey,
                customer_email: customerEmail,
                plan: plan,
                plan_name: planName,
                stripe_session_id: stripeSessionId,
                stripe_customer_id: stripeCustomerId,
                expires_at: expiresAt
            };

            const { data, error } = await this.supabase
                .from('licenses')
                .insert([licenseData])
                .select()
                .single();

            if (error) {
                console.error('❌ Erro ao criar licença:', error);
                throw error;
            }

            console.log(`🔑 Nova licença criada: ${licenseKey} para ${customerEmail}`);
            return data;

        } catch (error) {
            console.error('❌ Erro ao criar licença:', error);
            throw error;
        }
    }

    /**
     * Validar licença
     */
    async validateLicense(licenseKey) {
        try {
            const { data, error } = await this.supabase
                .rpc('validate_license', { license_key: licenseKey });

            if (error) {
                console.error('❌ Erro ao validar licença:', error);
                return {
                    valid: false,
                    error: 'Erro interno do servidor'
                };
            }

            if (data && data.length > 0) {
                const result = data[0];
                return {
                    valid: result.valid,
                    license: result.license_data,
                    error: result.error_message
                };
            }

            return {
                valid: false,
                error: 'Licença não encontrada'
            };

        } catch (error) {
            console.error('❌ Erro ao validar licença:', error);
            return {
                valid: false,
                error: 'Erro interno do servidor'
            };
        }
    }

    /**
     * Ativar licença para um usuário Discord
     */
    async activateLicense(licenseKey, discordId) {
        try {
            // Verificar se a licença existe e é válida
            const validation = await this.validateLicense(licenseKey);
            if (!validation.valid) {
                return {
                    success: false,
                    error: validation.error
                };
            }

            // Verificar se o usuário já tem uma licença ativa
            const { data: existingUser } = await this.supabase
                .from('users')
                .select('*')
                .eq('discord_id', discordId)
                .single();

            if (existingUser && existingUser.license_key) {
                return {
                    success: false,
                    error: 'Usuário já possui uma licença ativa'
                };
            }

            // Verificar se a licença já foi ativada por outro usuário
            const { data: existingActivation } = await this.supabase
                .from('users')
                .select('*')
                .eq('license_key', licenseKey)
                .single();

            if (existingActivation) {
                return {
                    success: false,
                    error: 'Esta licença já foi ativada por outro usuário'
                };
            }

            // Ativar a licença
            const now = new Date().toISOString();

            // Atualizar tabela de licenças
            const { error: licenseError } = await this.supabase
                .from('licenses')
                .update({
                    activated_at: now,
                    activated_by: discordId
                })
                .eq('key', licenseKey);

            if (licenseError) {
                console.error('❌ Erro ao atualizar licença:', licenseError);
                throw licenseError;
            }

            // Inserir/atualizar usuário
            const { data: userData, error: userError } = await this.supabase
                .from('users')
                .upsert({
                    discord_id: discordId,
                    license_key: licenseKey,
                    activated_at: now
                })
                .select()
                .single();

            if (userError) {
                console.error('❌ Erro ao criar/atualizar usuário:', userError);
                throw userError;
            }

            console.log(`✅ Licença ${licenseKey} ativada para usuário ${discordId}`);
            
            return {
                success: true,
                license: validation.license,
                user: userData
            };

        } catch (error) {
            console.error('❌ Erro ao ativar licença:', error);
            return {
                success: false,
                error: 'Erro interno do servidor'
            };
        }
    }

    /**
     * Buscar usuário por Discord ID
     */
    async getUserByDiscordId(discordId) {
        try {
            const { data, error } = await this.supabase
                .from('users')
                .select(`
                    *,
                    licenses (*)
                `)
                .eq('discord_id', discordId)
                .single();

            if (error && error.code !== 'PGRST116') { // PGRST116 = not found
                console.error('❌ Erro ao buscar usuário:', error);
                throw error;
            }

            return data;

        } catch (error) {
            console.error('❌ Erro ao buscar usuário:', error);
            return null;
        }
    }

    /**
     * Obter estatísticas
     */
    async getStats() {
        try {
            const { data, error } = await this.supabase
                .from('license_stats')
                .select('*')
                .single();

            if (error) {
                console.error('❌ Erro ao obter estatísticas:', error);
                throw error;
            }

            return {
                total: data.total_licenses || 0,
                activated: data.activated_licenses || 0,
                pending: data.pending_licenses || 0,
                active: data.active_licenses || 0,
                expired: data.expired_licenses || 0,
                byPlan: {
                    basic: data.basic_licenses || 0,
                    pro: data.pro_licenses || 0
                }
            };

        } catch (error) {
            console.error('❌ Erro ao obter estatísticas:', error);
            return {
                total: 0,
                activated: 0,
                pending: 0,
                active: 0,
                expired: 0,
                byPlan: { basic: 0, pro: 0 }
            };
        }
    }

    /**
     * Listar todas as licenças (para dashboard)
     */
    async getAllLicenses() {
        try {
            const { data, error } = await this.supabase
                .from('licenses_with_users')
                .select('*')
                .order('created_at', { ascending: false });

            if (error) {
                console.error('❌ Erro ao listar licenças:', error);
                throw error;
            }

            return data || [];

        } catch (error) {
            console.error('❌ Erro ao listar licenças:', error);
            return [];
        }
    }

    /**
     * Salvar configuração do bot
     */
    async saveBotConfig(discordId, configType, configData) {
        try {
            const { data, error } = await this.supabase
                .from('bot_configs')
                .upsert({
                    discord_id: discordId,
                    config_type: configType,
                    config_data: configData
                })
                .select()
                .single();

            if (error) {
                console.error('❌ Erro ao salvar configuração:', error);
                throw error;
            }

            return data;

        } catch (error) {
            console.error('❌ Erro ao salvar configuração:', error);
            throw error;
        }
    }

    /**
     * Carregar configuração do bot
     */
    async loadBotConfig(discordId, configType) {
        try {
            const { data, error } = await this.supabase
                .from('bot_configs')
                .select('config_data')
                .eq('discord_id', discordId)
                .eq('config_type', configType)
                .single();

            if (error && error.code !== 'PGRST116') {
                console.error('❌ Erro ao carregar configuração:', error);
                throw error;
            }

            return data ? data.config_data : null;

        } catch (error) {
            console.error('❌ Erro ao carregar configuração:', error);
            return null;
        }
    }

    // Métodos auxiliares
    getPlanName(plan) {
        const plans = {
            'basic': 'Plano Basic',
            'pro': 'Plano Pro'
        };
        return plans[plan] || 'Plano Desconhecido';
    }

    calculateExpiry(plan) {
        const now = new Date();
        
        switch (plan) {
            case 'basic':
            case 'pro':
                return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString(); // 30 dias
            default:
                return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString(); // 30 dias padrão
        }
    }
}

module.exports = SupabaseLicenseManager;
