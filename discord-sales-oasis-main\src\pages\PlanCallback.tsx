import { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';

const PlanCallback = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [message, setMessage] = useState('Processando pagamento...');

  useEffect(() => {
    const processPayment = async () => {
      try {
        const error = searchParams.get('error');
        const token = searchParams.get('token');
        const urlToken = searchParams.get('urlToken');

        if (error) {
          throw new Error(`Erro de autorização: ${error}`);
        }

        if (!token || !urlToken) {
          throw new Error('Tokens de autenticação não encontrados');
        }

        // Salvar tokens no localStorage
        localStorage.setItem('sessionToken', token);
        localStorage.setItem('urlToken', urlToken);

        // Pegar o plano selecionado
        const selectedPlan = localStorage.getItem('selectedPlan');
        if (!selectedPlan) {
          throw new Error('Plano não encontrado');
        }

        setMessage(`Processando plano ${selectedPlan.toUpperCase()}...`);

        // Determinar o priceId baseado no plano
        const priceId = selectedPlan === 'basic'
          ? 'price_1RdYzeRIzvHRKrKIhNsADY81'  // Basic
          : 'price_1RdZ0ORIzvHRKrKIt3O227mH'; // Pro

        console.log(`🛒 Criando checkout para plano: ${selectedPlan}, priceId: ${priceId}`);

        // Criar sessão do Stripe
        const response = await fetch('/api/stripe/create-checkout-session', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-session-token': token,
          },
          body: JSON.stringify({
            priceId: priceId,
            plan: selectedPlan
          }),
        });

        if (!response.ok) {
          const errorData = await response.text();
          throw new Error(`Erro ao criar sessão de pagamento: ${errorData}`);
        }

        const { url } = await response.json();
        
        if (!url) {
          throw new Error('URL de pagamento não recebida');
        }

        console.log(`✅ Redirecionando para Stripe: ${url}`);
        
        // Limpar o plano do localStorage
        localStorage.removeItem('selectedPlan');
        
        // Redirecionar para o Stripe
        window.location.href = url;

      } catch (error) {
        console.error('❌ Erro no processamento:', error);
        setMessage(`Erro: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
        
        // Redirecionar para home após 3 segundos
        setTimeout(() => {
          navigate('/');
        }, 3000);
      }
    };

    processPayment();
  }, [searchParams, navigate]);

  return (
    <div className="min-h-screen bg-[#0A0A0A] flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-400 mx-auto mb-4"></div>
        <p className="text-white text-lg">{message}</p>
      </div>
    </div>
  );
};

export default PlanCallback;
