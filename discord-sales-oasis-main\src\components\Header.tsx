import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON>u, User, LogIn, LogOut } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";

const Header = () => {
  const navigate = useNavigate();
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  useEffect(() => {
    // Verificar se está logado usando o sistema existente
    const sessionToken = localStorage.getItem('sessionToken');
    setIsLoggedIn(!!sessionToken);
  }, []);

  const handleLogin = () => {
    navigate('/login');
  };

  const handleLogout = () => {
    localStorage.removeItem('sessionToken');
    localStorage.removeItem('urlToken');
    setIsLoggedIn(false);
    navigate('/');
  };

  return <header className="fixed top-0 w-full bg-background/80 backdrop-blur-md border-b border-border z-50">
      <div className="container mx-auto px-4 h-16 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Bot className="h-8 w-8 text-primary" />
          <span className="text-xl font-bold text-foreground">SalesBot</span>
        </div>
        
        <nav className="hidden md:flex items-center space-x-8">
          <a href="#features" className="text-muted-foreground hover:text-foreground transition-colors">
            Recursos
          </a>
          <a href="#how-it-works" className="text-muted-foreground hover:text-foreground transition-colors">
            Como Funciona
          </a>
          <a href="#pricing" className="text-muted-foreground hover:text-foreground transition-colors">
            Preços
          </a>
        </nav>

        <div className="flex items-center space-x-4">
          {isLoggedIn ? (
            <>
              <Button
                variant="outline"
                onClick={() => {
                  const urlToken = localStorage.getItem('urlToken');
                  if (urlToken) {
                    navigate(`/dashboard/${urlToken}`);
                  } else {
                    navigate("/dashboard");
                  }
                }}
                className="border-[#5865F2] text-[#5865F2] hover:bg-[#5865F2] hover:text-white transition-all duration-200"
              >
                <User className="mr-2 h-4 w-4" />
                <span>Dashboard</span>
              </Button>
              <Button
                variant="ghost"
                onClick={handleLogout}
                className="text-muted-foreground hover:text-foreground"
              >
                <LogOut className="mr-2 h-4 w-4" />
                <span>Sair</span>
              </Button>
            </>
          ) : (
            <Button
              onClick={handleLogin}
              className="bg-[#5865F2] hover:bg-[#4752C4] transition-all duration-200"
            >
              <LogIn className="mr-2 h-4 w-4" />
              <span>Login Discord</span>
            </Button>
          )}
          <Button variant="ghost" size="icon" className="md:hidden">
            <Menu className="h-5 w-5" />
          </Button>
        </div>
      </div>
    </header>;
};
export default Header;