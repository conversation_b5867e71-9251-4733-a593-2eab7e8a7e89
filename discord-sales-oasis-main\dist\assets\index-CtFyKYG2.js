var S="basil",h=function(r){return r===3?"v3":r},m="https://js.stripe.com",j="".concat(m,"/").concat(S,"/stripe.js"),L=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,g=/^https:\/\/js\.stripe\.com\/(v3|[a-z]+)\/stripe\.js(\?.*)?$/,p="loadStripe.setLoadParameters was called but an existing Stripe.js script already exists in the document; existing script parameters will be used",R=function(r){return L.test(r)||g.test(r)},P=function(){for(var r=document.querySelectorAll('script[src^="'.concat(m,'"]')),e=0;e<r.length;e++){var t=r[e];if(R(t.src))return t}return null},f=function(r){var e="",t=document.createElement("script");t.src="".concat(j).concat(e);var n=document.head||document.body;if(!n)throw new Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return n.appendChild(t),t},T=function(r,e){!r||!r._registerWrapper||r._registerWrapper({name:"stripe-js",version:"7.4.0",startTime:e})},a=null,c=null,l=null,y=function(r){return function(e){r(new Error("Failed to load Stripe.js",{cause:e}))}},b=function(r,e){return function(){window.Stripe?r(window.Stripe):e(new Error("Stripe.js not available"))}},x=function(r){return a!==null?a:(a=new Promise(function(e,t){if(typeof window>"u"||typeof document>"u"){e(null);return}if(window.Stripe&&r&&console.warn(p),window.Stripe){e(window.Stripe);return}try{var n=P();if(n&&r)console.warn(p);else if(!n)n=f(r);else if(n&&l!==null&&c!==null){var o;n.removeEventListener("load",l),n.removeEventListener("error",c),(o=n.parentNode)===null||o===void 0||o.removeChild(n),n=f(r)}l=b(e,t),c=y(t),n.addEventListener("load",l),n.addEventListener("error",c)}catch(u){t(u);return}}),a.catch(function(e){return a=null,Promise.reject(e)}))},I=function(r,e,t){if(r===null)return null;var n=e[0],o=n.match(/^pk_test/),u=h(r.version),d=S;o&&u!==d&&console.warn("Stripe.js@".concat(u," was loaded on the page, but @stripe/stripe-js@").concat("7.4.0"," expected Stripe.js@").concat(d,". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning"));var v=r.apply(void 0,e);return T(v,t),v},s,w=!1,E=function(){return s||(s=x(null).catch(function(r){return s=null,Promise.reject(r)}),s)};Promise.resolve().then(function(){return E()}).catch(function(i){w||console.warn(i)});var U=function(){for(var r=arguments.length,e=new Array(r),t=0;t<r;t++)e[t]=arguments[t];w=!0;var n=Date.now();return E().then(function(o){return I(o,e,n)})};export{U as loadStripe};
