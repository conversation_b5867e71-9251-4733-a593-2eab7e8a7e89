var kd=e=>{throw TypeError(e)};var Xa=(e,t,n)=>t.has(e)||kd("Cannot "+n);var T=(e,t,n)=>(Xa(e,t,"read from private field"),n?n.call(e):t.get(e)),q=(e,t,n)=>t.has(e)?kd("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),W=(e,t,n,r)=>(Xa(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),Ae=(e,t,n)=>(Xa(e,t,"access private method"),n);var $s=(e,t,n,r)=>({set _(o){W(e,t,o,n)},get _(){return T(e,t,r)}});function q0(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const s=Object.getOwnPropertyDescriptor(r,o);s&&Object.defineProperty(e,o,s.get?s:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const s of o)if(s.type==="childList")for(const i of s.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(o){const s={};return o.integrity&&(s.integrity=o.integrity),o.referrerPolicy&&(s.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?s.credentials="include":o.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(o){if(o.ep)return;o.ep=!0;const s=n(o);fetch(o.href,s)}})();function Up(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Bp={exports:{}},da={},Vp={exports:{}},G={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ts=Symbol.for("react.element"),Z0=Symbol.for("react.portal"),J0=Symbol.for("react.fragment"),ey=Symbol.for("react.strict_mode"),ty=Symbol.for("react.profiler"),ny=Symbol.for("react.provider"),ry=Symbol.for("react.context"),oy=Symbol.for("react.forward_ref"),sy=Symbol.for("react.suspense"),iy=Symbol.for("react.memo"),ay=Symbol.for("react.lazy"),Cd=Symbol.iterator;function ly(e){return e===null||typeof e!="object"?null:(e=Cd&&e[Cd]||e["@@iterator"],typeof e=="function"?e:null)}var Wp={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Hp=Object.assign,Kp={};function So(e,t,n){this.props=e,this.context=t,this.refs=Kp,this.updater=n||Wp}So.prototype.isReactComponent={};So.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};So.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Qp(){}Qp.prototype=So.prototype;function eu(e,t,n){this.props=e,this.context=t,this.refs=Kp,this.updater=n||Wp}var tu=eu.prototype=new Qp;tu.constructor=eu;Hp(tu,So.prototype);tu.isPureReactComponent=!0;var Nd=Array.isArray,Gp=Object.prototype.hasOwnProperty,nu={current:null},Yp={key:!0,ref:!0,__self:!0,__source:!0};function Xp(e,t,n){var r,o={},s=null,i=null;if(t!=null)for(r in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(s=""+t.key),t)Gp.call(t,r)&&!Yp.hasOwnProperty(r)&&(o[r]=t[r]);var a=arguments.length-2;if(a===1)o.children=n;else if(1<a){for(var c=Array(a),u=0;u<a;u++)c[u]=arguments[u+2];o.children=c}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)o[r]===void 0&&(o[r]=a[r]);return{$$typeof:Ts,type:e,key:s,ref:i,props:o,_owner:nu.current}}function cy(e,t){return{$$typeof:Ts,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function ru(e){return typeof e=="object"&&e!==null&&e.$$typeof===Ts}function uy(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var jd=/\/+/g;function qa(e,t){return typeof e=="object"&&e!==null&&e.key!=null?uy(""+e.key):t.toString(36)}function mi(e,t,n,r,o){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(s){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case Ts:case Z0:i=!0}}if(i)return i=e,o=o(i),e=r===""?"."+qa(i,0):r,Nd(o)?(n="",e!=null&&(n=e.replace(jd,"$&/")+"/"),mi(o,t,n,"",function(u){return u})):o!=null&&(ru(o)&&(o=cy(o,n+(!o.key||i&&i.key===o.key?"":(""+o.key).replace(jd,"$&/")+"/")+e)),t.push(o)),1;if(i=0,r=r===""?".":r+":",Nd(e))for(var a=0;a<e.length;a++){s=e[a];var c=r+qa(s,a);i+=mi(s,t,n,c,o)}else if(c=ly(e),typeof c=="function")for(e=c.call(e),a=0;!(s=e.next()).done;)s=s.value,c=r+qa(s,a++),i+=mi(s,t,n,c,o);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function Us(e,t,n){if(e==null)return e;var r=[],o=0;return mi(e,r,"","",function(s){return t.call(n,s,o++)}),r}function dy(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ve={current:null},gi={transition:null},fy={ReactCurrentDispatcher:Ve,ReactCurrentBatchConfig:gi,ReactCurrentOwner:nu};function qp(){throw Error("act(...) is not supported in production builds of React.")}G.Children={map:Us,forEach:function(e,t,n){Us(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Us(e,function(){t++}),t},toArray:function(e){return Us(e,function(t){return t})||[]},only:function(e){if(!ru(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};G.Component=So;G.Fragment=J0;G.Profiler=ty;G.PureComponent=eu;G.StrictMode=ey;G.Suspense=sy;G.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=fy;G.act=qp;G.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Hp({},e.props),o=e.key,s=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,i=nu.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(c in t)Gp.call(t,c)&&!Yp.hasOwnProperty(c)&&(r[c]=t[c]===void 0&&a!==void 0?a[c]:t[c])}var c=arguments.length-2;if(c===1)r.children=n;else if(1<c){a=Array(c);for(var u=0;u<c;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:Ts,type:e.type,key:o,ref:s,props:r,_owner:i}};G.createContext=function(e){return e={$$typeof:ry,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:ny,_context:e},e.Consumer=e};G.createElement=Xp;G.createFactory=function(e){var t=Xp.bind(null,e);return t.type=e,t};G.createRef=function(){return{current:null}};G.forwardRef=function(e){return{$$typeof:oy,render:e}};G.isValidElement=ru;G.lazy=function(e){return{$$typeof:ay,_payload:{_status:-1,_result:e},_init:dy}};G.memo=function(e,t){return{$$typeof:iy,type:e,compare:t===void 0?null:t}};G.startTransition=function(e){var t=gi.transition;gi.transition={};try{e()}finally{gi.transition=t}};G.unstable_act=qp;G.useCallback=function(e,t){return Ve.current.useCallback(e,t)};G.useContext=function(e){return Ve.current.useContext(e)};G.useDebugValue=function(){};G.useDeferredValue=function(e){return Ve.current.useDeferredValue(e)};G.useEffect=function(e,t){return Ve.current.useEffect(e,t)};G.useId=function(){return Ve.current.useId()};G.useImperativeHandle=function(e,t,n){return Ve.current.useImperativeHandle(e,t,n)};G.useInsertionEffect=function(e,t){return Ve.current.useInsertionEffect(e,t)};G.useLayoutEffect=function(e,t){return Ve.current.useLayoutEffect(e,t)};G.useMemo=function(e,t){return Ve.current.useMemo(e,t)};G.useReducer=function(e,t,n){return Ve.current.useReducer(e,t,n)};G.useRef=function(e){return Ve.current.useRef(e)};G.useState=function(e){return Ve.current.useState(e)};G.useSyncExternalStore=function(e,t,n){return Ve.current.useSyncExternalStore(e,t,n)};G.useTransition=function(){return Ve.current.useTransition()};G.version="18.3.1";Vp.exports=G;var h=Vp.exports;const A=Up(h),Zp=q0({__proto__:null,default:A},[h]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var py=h,hy=Symbol.for("react.element"),my=Symbol.for("react.fragment"),gy=Object.prototype.hasOwnProperty,vy=py.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,yy={key:!0,ref:!0,__self:!0,__source:!0};function Jp(e,t,n){var r,o={},s=null,i=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(i=t.ref);for(r in t)gy.call(t,r)&&!yy.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:hy,type:e,key:s,ref:i,props:o,_owner:vy.current}}da.Fragment=my;da.jsx=Jp;da.jsxs=Jp;Bp.exports=da;var l=Bp.exports,eh={exports:{}},at={},th={exports:{}},nh={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(N,R){var z=N.length;N.push(R);e:for(;0<z;){var L=z-1>>>1,$=N[L];if(0<o($,R))N[L]=R,N[z]=$,z=L;else break e}}function n(N){return N.length===0?null:N[0]}function r(N){if(N.length===0)return null;var R=N[0],z=N.pop();if(z!==R){N[0]=z;e:for(var L=0,$=N.length,Y=$>>>1;L<Y;){var de=2*(L+1)-1,qe=N[de],J=de+1,yt=N[J];if(0>o(qe,z))J<$&&0>o(yt,qe)?(N[L]=yt,N[J]=z,L=J):(N[L]=qe,N[de]=z,L=de);else if(J<$&&0>o(yt,z))N[L]=yt,N[J]=z,L=J;else break e}}return R}function o(N,R){var z=N.sortIndex-R.sortIndex;return z!==0?z:N.id-R.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var i=Date,a=i.now();e.unstable_now=function(){return i.now()-a}}var c=[],u=[],f=1,p=null,d=3,x=!1,b=!1,g=!1,S=typeof setTimeout=="function"?setTimeout:null,v=typeof clearTimeout=="function"?clearTimeout:null,m=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function y(N){for(var R=n(u);R!==null;){if(R.callback===null)r(u);else if(R.startTime<=N)r(u),R.sortIndex=R.expirationTime,t(c,R);else break;R=n(u)}}function w(N){if(g=!1,y(N),!b)if(n(c)!==null)b=!0,U(E);else{var R=n(u);R!==null&&Q(w,R.startTime-N)}}function E(N,R){b=!1,g&&(g=!1,v(j),j=-1),x=!0;var z=d;try{for(y(R),p=n(c);p!==null&&(!(p.expirationTime>R)||N&&!F());){var L=p.callback;if(typeof L=="function"){p.callback=null,d=p.priorityLevel;var $=L(p.expirationTime<=R);R=e.unstable_now(),typeof $=="function"?p.callback=$:p===n(c)&&r(c),y(R)}else r(c);p=n(c)}if(p!==null)var Y=!0;else{var de=n(u);de!==null&&Q(w,de.startTime-R),Y=!1}return Y}finally{p=null,d=z,x=!1}}var C=!1,k=null,j=-1,O=5,_=-1;function F(){return!(e.unstable_now()-_<O)}function M(){if(k!==null){var N=e.unstable_now();_=N;var R=!0;try{R=k(!0,N)}finally{R?H():(C=!1,k=null)}}else C=!1}var H;if(typeof m=="function")H=function(){m(M)};else if(typeof MessageChannel<"u"){var D=new MessageChannel,K=D.port2;D.port1.onmessage=M,H=function(){K.postMessage(null)}}else H=function(){S(M,0)};function U(N){k=N,C||(C=!0,H())}function Q(N,R){j=S(function(){N(e.unstable_now())},R)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(N){N.callback=null},e.unstable_continueExecution=function(){b||x||(b=!0,U(E))},e.unstable_forceFrameRate=function(N){0>N||125<N?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):O=0<N?Math.floor(1e3/N):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return n(c)},e.unstable_next=function(N){switch(d){case 1:case 2:case 3:var R=3;break;default:R=d}var z=d;d=R;try{return N()}finally{d=z}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(N,R){switch(N){case 1:case 2:case 3:case 4:case 5:break;default:N=3}var z=d;d=N;try{return R()}finally{d=z}},e.unstable_scheduleCallback=function(N,R,z){var L=e.unstable_now();switch(typeof z=="object"&&z!==null?(z=z.delay,z=typeof z=="number"&&0<z?L+z:L):z=L,N){case 1:var $=-1;break;case 2:$=250;break;case 5:$=**********;break;case 4:$=1e4;break;default:$=5e3}return $=z+$,N={id:f++,callback:R,priorityLevel:N,startTime:z,expirationTime:$,sortIndex:-1},z>L?(N.sortIndex=z,t(u,N),n(c)===null&&N===n(u)&&(g?(v(j),j=-1):g=!0,Q(w,z-L))):(N.sortIndex=$,t(c,N),b||x||(b=!0,U(E))),N},e.unstable_shouldYield=F,e.unstable_wrapCallback=function(N){var R=d;return function(){var z=d;d=R;try{return N.apply(this,arguments)}finally{d=z}}}})(nh);th.exports=nh;var xy=th.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wy=h,it=xy;function P(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var rh=new Set,rs={};function vr(e,t){co(e,t),co(e+"Capture",t)}function co(e,t){for(rs[e]=t,e=0;e<t.length;e++)rh.add(t[e])}var Zt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Fl=Object.prototype.hasOwnProperty,Sy=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Td={},Pd={};function by(e){return Fl.call(Pd,e)?!0:Fl.call(Td,e)?!1:Sy.test(e)?Pd[e]=!0:(Td[e]=!0,!1)}function Ey(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function ky(e,t,n,r){if(t===null||typeof t>"u"||Ey(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function We(e,t,n,r,o,s,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=i}var Pe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Pe[e]=new We(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Pe[t]=new We(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Pe[e]=new We(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Pe[e]=new We(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Pe[e]=new We(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Pe[e]=new We(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Pe[e]=new We(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Pe[e]=new We(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Pe[e]=new We(e,5,!1,e.toLowerCase(),null,!1,!1)});var ou=/[\-:]([a-z])/g;function su(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(ou,su);Pe[t]=new We(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(ou,su);Pe[t]=new We(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(ou,su);Pe[t]=new We(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Pe[e]=new We(e,1,!1,e.toLowerCase(),null,!1,!1)});Pe.xlinkHref=new We("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Pe[e]=new We(e,1,!1,e.toLowerCase(),null,!0,!0)});function iu(e,t,n,r){var o=Pe.hasOwnProperty(t)?Pe[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(ky(t,n,o,r)&&(n=null),r||o===null?by(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var on=wy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Bs=Symbol.for("react.element"),Pr=Symbol.for("react.portal"),Rr=Symbol.for("react.fragment"),au=Symbol.for("react.strict_mode"),$l=Symbol.for("react.profiler"),oh=Symbol.for("react.provider"),sh=Symbol.for("react.context"),lu=Symbol.for("react.forward_ref"),Ul=Symbol.for("react.suspense"),Bl=Symbol.for("react.suspense_list"),cu=Symbol.for("react.memo"),vn=Symbol.for("react.lazy"),ih=Symbol.for("react.offscreen"),Rd=Symbol.iterator;function Ao(e){return e===null||typeof e!="object"?null:(e=Rd&&e[Rd]||e["@@iterator"],typeof e=="function"?e:null)}var he=Object.assign,Za;function Uo(e){if(Za===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Za=t&&t[1]||""}return`
`+Za+e}var Ja=!1;function el(e,t){if(!e||Ja)return"";Ja=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),s=r.stack.split(`
`),i=o.length-1,a=s.length-1;1<=i&&0<=a&&o[i]!==s[a];)a--;for(;1<=i&&0<=a;i--,a--)if(o[i]!==s[a]){if(i!==1||a!==1)do if(i--,a--,0>a||o[i]!==s[a]){var c=`
`+o[i].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}while(1<=i&&0<=a);break}}}finally{Ja=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Uo(e):""}function Cy(e){switch(e.tag){case 5:return Uo(e.type);case 16:return Uo("Lazy");case 13:return Uo("Suspense");case 19:return Uo("SuspenseList");case 0:case 2:case 15:return e=el(e.type,!1),e;case 11:return e=el(e.type.render,!1),e;case 1:return e=el(e.type,!0),e;default:return""}}function Vl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Rr:return"Fragment";case Pr:return"Portal";case $l:return"Profiler";case au:return"StrictMode";case Ul:return"Suspense";case Bl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case sh:return(e.displayName||"Context")+".Consumer";case oh:return(e._context.displayName||"Context")+".Provider";case lu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case cu:return t=e.displayName||null,t!==null?t:Vl(e.type)||"Memo";case vn:t=e._payload,e=e._init;try{return Vl(e(t))}catch{}}return null}function Ny(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Vl(t);case 8:return t===au?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function zn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function ah(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function jy(e){var t=ah(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(i){r=""+i,s.call(this,i)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(i){r=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Vs(e){e._valueTracker||(e._valueTracker=jy(e))}function lh(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ah(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Oi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Wl(e,t){var n=t.checked;return he({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Ad(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=zn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function ch(e,t){t=t.checked,t!=null&&iu(e,"checked",t,!1)}function Hl(e,t){ch(e,t);var n=zn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Kl(e,t.type,n):t.hasOwnProperty("defaultValue")&&Kl(e,t.type,zn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function _d(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Kl(e,t,n){(t!=="number"||Oi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Bo=Array.isArray;function Br(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+zn(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Ql(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(P(91));return he({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Od(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(P(92));if(Bo(n)){if(1<n.length)throw Error(P(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:zn(n)}}function uh(e,t){var n=zn(t.value),r=zn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Dd(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function dh(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Gl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?dh(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Ws,fh=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Ws=Ws||document.createElement("div"),Ws.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Ws.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function os(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Ko={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Ty=["Webkit","ms","Moz","O"];Object.keys(Ko).forEach(function(e){Ty.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Ko[t]=Ko[e]})});function ph(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Ko.hasOwnProperty(e)&&Ko[e]?(""+t).trim():t+"px"}function hh(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=ph(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var Py=he({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Yl(e,t){if(t){if(Py[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(P(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(P(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(P(61))}if(t.style!=null&&typeof t.style!="object")throw Error(P(62))}}function Xl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ql=null;function uu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Zl=null,Vr=null,Wr=null;function Id(e){if(e=As(e)){if(typeof Zl!="function")throw Error(P(280));var t=e.stateNode;t&&(t=ga(t),Zl(e.stateNode,e.type,t))}}function mh(e){Vr?Wr?Wr.push(e):Wr=[e]:Vr=e}function gh(){if(Vr){var e=Vr,t=Wr;if(Wr=Vr=null,Id(e),t)for(e=0;e<t.length;e++)Id(t[e])}}function vh(e,t){return e(t)}function yh(){}var tl=!1;function xh(e,t,n){if(tl)return e(t,n);tl=!0;try{return vh(e,t,n)}finally{tl=!1,(Vr!==null||Wr!==null)&&(yh(),gh())}}function ss(e,t){var n=e.stateNode;if(n===null)return null;var r=ga(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(P(231,t,typeof n));return n}var Jl=!1;if(Zt)try{var _o={};Object.defineProperty(_o,"passive",{get:function(){Jl=!0}}),window.addEventListener("test",_o,_o),window.removeEventListener("test",_o,_o)}catch{Jl=!1}function Ry(e,t,n,r,o,s,i,a,c){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(f){this.onError(f)}}var Qo=!1,Di=null,Ii=!1,ec=null,Ay={onError:function(e){Qo=!0,Di=e}};function _y(e,t,n,r,o,s,i,a,c){Qo=!1,Di=null,Ry.apply(Ay,arguments)}function Oy(e,t,n,r,o,s,i,a,c){if(_y.apply(this,arguments),Qo){if(Qo){var u=Di;Qo=!1,Di=null}else throw Error(P(198));Ii||(Ii=!0,ec=u)}}function yr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function wh(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Md(e){if(yr(e)!==e)throw Error(P(188))}function Dy(e){var t=e.alternate;if(!t){if(t=yr(e),t===null)throw Error(P(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var s=o.alternate;if(s===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===s.child){for(s=o.child;s;){if(s===n)return Md(o),e;if(s===r)return Md(o),t;s=s.sibling}throw Error(P(188))}if(n.return!==r.return)n=o,r=s;else{for(var i=!1,a=o.child;a;){if(a===n){i=!0,n=o,r=s;break}if(a===r){i=!0,r=o,n=s;break}a=a.sibling}if(!i){for(a=s.child;a;){if(a===n){i=!0,n=s,r=o;break}if(a===r){i=!0,r=s,n=o;break}a=a.sibling}if(!i)throw Error(P(189))}}if(n.alternate!==r)throw Error(P(190))}if(n.tag!==3)throw Error(P(188));return n.stateNode.current===n?e:t}function Sh(e){return e=Dy(e),e!==null?bh(e):null}function bh(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=bh(e);if(t!==null)return t;e=e.sibling}return null}var Eh=it.unstable_scheduleCallback,Ld=it.unstable_cancelCallback,Iy=it.unstable_shouldYield,My=it.unstable_requestPaint,ve=it.unstable_now,Ly=it.unstable_getCurrentPriorityLevel,du=it.unstable_ImmediatePriority,kh=it.unstable_UserBlockingPriority,Mi=it.unstable_NormalPriority,zy=it.unstable_LowPriority,Ch=it.unstable_IdlePriority,fa=null,Ut=null;function Fy(e){if(Ut&&typeof Ut.onCommitFiberRoot=="function")try{Ut.onCommitFiberRoot(fa,e,void 0,(e.current.flags&128)===128)}catch{}}var Ct=Math.clz32?Math.clz32:By,$y=Math.log,Uy=Math.LN2;function By(e){return e>>>=0,e===0?32:31-($y(e)/Uy|0)|0}var Hs=64,Ks=4194304;function Vo(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Li(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,s=e.pingedLanes,i=n&268435455;if(i!==0){var a=i&~o;a!==0?r=Vo(a):(s&=i,s!==0&&(r=Vo(s)))}else i=n&~o,i!==0?r=Vo(i):s!==0&&(r=Vo(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,s=t&-t,o>=s||o===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ct(t),o=1<<n,r|=e[n],t&=~o;return r}function Vy(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Wy(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,s=e.pendingLanes;0<s;){var i=31-Ct(s),a=1<<i,c=o[i];c===-1?(!(a&n)||a&r)&&(o[i]=Vy(a,t)):c<=t&&(e.expiredLanes|=a),s&=~a}}function tc(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Nh(){var e=Hs;return Hs<<=1,!(Hs&4194240)&&(Hs=64),e}function nl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ps(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ct(t),e[t]=n}function Hy(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-Ct(n),s=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~s}}function fu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ct(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var ee=0;function jh(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Th,pu,Ph,Rh,Ah,nc=!1,Qs=[],Pn=null,Rn=null,An=null,is=new Map,as=new Map,xn=[],Ky="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function zd(e,t){switch(e){case"focusin":case"focusout":Pn=null;break;case"dragenter":case"dragleave":Rn=null;break;case"mouseover":case"mouseout":An=null;break;case"pointerover":case"pointerout":is.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":as.delete(t.pointerId)}}function Oo(e,t,n,r,o,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[o]},t!==null&&(t=As(t),t!==null&&pu(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function Qy(e,t,n,r,o){switch(t){case"focusin":return Pn=Oo(Pn,e,t,n,r,o),!0;case"dragenter":return Rn=Oo(Rn,e,t,n,r,o),!0;case"mouseover":return An=Oo(An,e,t,n,r,o),!0;case"pointerover":var s=o.pointerId;return is.set(s,Oo(is.get(s)||null,e,t,n,r,o)),!0;case"gotpointercapture":return s=o.pointerId,as.set(s,Oo(as.get(s)||null,e,t,n,r,o)),!0}return!1}function _h(e){var t=Zn(e.target);if(t!==null){var n=yr(t);if(n!==null){if(t=n.tag,t===13){if(t=wh(n),t!==null){e.blockedOn=t,Ah(e.priority,function(){Ph(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function vi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=rc(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);ql=r,n.target.dispatchEvent(r),ql=null}else return t=As(n),t!==null&&pu(t),e.blockedOn=n,!1;t.shift()}return!0}function Fd(e,t,n){vi(e)&&n.delete(t)}function Gy(){nc=!1,Pn!==null&&vi(Pn)&&(Pn=null),Rn!==null&&vi(Rn)&&(Rn=null),An!==null&&vi(An)&&(An=null),is.forEach(Fd),as.forEach(Fd)}function Do(e,t){e.blockedOn===t&&(e.blockedOn=null,nc||(nc=!0,it.unstable_scheduleCallback(it.unstable_NormalPriority,Gy)))}function ls(e){function t(o){return Do(o,e)}if(0<Qs.length){Do(Qs[0],e);for(var n=1;n<Qs.length;n++){var r=Qs[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Pn!==null&&Do(Pn,e),Rn!==null&&Do(Rn,e),An!==null&&Do(An,e),is.forEach(t),as.forEach(t),n=0;n<xn.length;n++)r=xn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<xn.length&&(n=xn[0],n.blockedOn===null);)_h(n),n.blockedOn===null&&xn.shift()}var Hr=on.ReactCurrentBatchConfig,zi=!0;function Yy(e,t,n,r){var o=ee,s=Hr.transition;Hr.transition=null;try{ee=1,hu(e,t,n,r)}finally{ee=o,Hr.transition=s}}function Xy(e,t,n,r){var o=ee,s=Hr.transition;Hr.transition=null;try{ee=4,hu(e,t,n,r)}finally{ee=o,Hr.transition=s}}function hu(e,t,n,r){if(zi){var o=rc(e,t,n,r);if(o===null)fl(e,t,r,Fi,n),zd(e,r);else if(Qy(o,e,t,n,r))r.stopPropagation();else if(zd(e,r),t&4&&-1<Ky.indexOf(e)){for(;o!==null;){var s=As(o);if(s!==null&&Th(s),s=rc(e,t,n,r),s===null&&fl(e,t,r,Fi,n),s===o)break;o=s}o!==null&&r.stopPropagation()}else fl(e,t,r,null,n)}}var Fi=null;function rc(e,t,n,r){if(Fi=null,e=uu(r),e=Zn(e),e!==null)if(t=yr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=wh(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Fi=e,null}function Oh(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ly()){case du:return 1;case kh:return 4;case Mi:case zy:return 16;case Ch:return 536870912;default:return 16}default:return 16}}var Nn=null,mu=null,yi=null;function Dh(){if(yi)return yi;var e,t=mu,n=t.length,r,o="value"in Nn?Nn.value:Nn.textContent,s=o.length;for(e=0;e<n&&t[e]===o[e];e++);var i=n-e;for(r=1;r<=i&&t[n-r]===o[s-r];r++);return yi=o.slice(e,1<r?1-r:void 0)}function xi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Gs(){return!0}function $d(){return!1}function lt(e){function t(n,r,o,s,i){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=s,this.target=i,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(s):s[a]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?Gs:$d,this.isPropagationStopped=$d,this}return he(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Gs)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Gs)},persist:function(){},isPersistent:Gs}),t}var bo={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},gu=lt(bo),Rs=he({},bo,{view:0,detail:0}),qy=lt(Rs),rl,ol,Io,pa=he({},Rs,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:vu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Io&&(Io&&e.type==="mousemove"?(rl=e.screenX-Io.screenX,ol=e.screenY-Io.screenY):ol=rl=0,Io=e),rl)},movementY:function(e){return"movementY"in e?e.movementY:ol}}),Ud=lt(pa),Zy=he({},pa,{dataTransfer:0}),Jy=lt(Zy),ex=he({},Rs,{relatedTarget:0}),sl=lt(ex),tx=he({},bo,{animationName:0,elapsedTime:0,pseudoElement:0}),nx=lt(tx),rx=he({},bo,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),ox=lt(rx),sx=he({},bo,{data:0}),Bd=lt(sx),ix={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},ax={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},lx={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function cx(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=lx[e])?!!t[e]:!1}function vu(){return cx}var ux=he({},Rs,{key:function(e){if(e.key){var t=ix[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=xi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?ax[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:vu,charCode:function(e){return e.type==="keypress"?xi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?xi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),dx=lt(ux),fx=he({},pa,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Vd=lt(fx),px=he({},Rs,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:vu}),hx=lt(px),mx=he({},bo,{propertyName:0,elapsedTime:0,pseudoElement:0}),gx=lt(mx),vx=he({},pa,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),yx=lt(vx),xx=[9,13,27,32],yu=Zt&&"CompositionEvent"in window,Go=null;Zt&&"documentMode"in document&&(Go=document.documentMode);var wx=Zt&&"TextEvent"in window&&!Go,Ih=Zt&&(!yu||Go&&8<Go&&11>=Go),Wd=" ",Hd=!1;function Mh(e,t){switch(e){case"keyup":return xx.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Lh(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Ar=!1;function Sx(e,t){switch(e){case"compositionend":return Lh(t);case"keypress":return t.which!==32?null:(Hd=!0,Wd);case"textInput":return e=t.data,e===Wd&&Hd?null:e;default:return null}}function bx(e,t){if(Ar)return e==="compositionend"||!yu&&Mh(e,t)?(e=Dh(),yi=mu=Nn=null,Ar=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Ih&&t.locale!=="ko"?null:t.data;default:return null}}var Ex={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Kd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Ex[e.type]:t==="textarea"}function zh(e,t,n,r){mh(r),t=$i(t,"onChange"),0<t.length&&(n=new gu("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Yo=null,cs=null;function kx(e){Yh(e,0)}function ha(e){var t=Dr(e);if(lh(t))return e}function Cx(e,t){if(e==="change")return t}var Fh=!1;if(Zt){var il;if(Zt){var al="oninput"in document;if(!al){var Qd=document.createElement("div");Qd.setAttribute("oninput","return;"),al=typeof Qd.oninput=="function"}il=al}else il=!1;Fh=il&&(!document.documentMode||9<document.documentMode)}function Gd(){Yo&&(Yo.detachEvent("onpropertychange",$h),cs=Yo=null)}function $h(e){if(e.propertyName==="value"&&ha(cs)){var t=[];zh(t,cs,e,uu(e)),xh(kx,t)}}function Nx(e,t,n){e==="focusin"?(Gd(),Yo=t,cs=n,Yo.attachEvent("onpropertychange",$h)):e==="focusout"&&Gd()}function jx(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return ha(cs)}function Tx(e,t){if(e==="click")return ha(t)}function Px(e,t){if(e==="input"||e==="change")return ha(t)}function Rx(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var jt=typeof Object.is=="function"?Object.is:Rx;function us(e,t){if(jt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!Fl.call(t,o)||!jt(e[o],t[o]))return!1}return!0}function Yd(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Xd(e,t){var n=Yd(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Yd(n)}}function Uh(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Uh(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Bh(){for(var e=window,t=Oi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Oi(e.document)}return t}function xu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Ax(e){var t=Bh(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Uh(n.ownerDocument.documentElement,n)){if(r!==null&&xu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,s=Math.min(r.start,o);r=r.end===void 0?s:Math.min(r.end,o),!e.extend&&s>r&&(o=r,r=s,s=o),o=Xd(n,s);var i=Xd(n,r);o&&i&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var _x=Zt&&"documentMode"in document&&11>=document.documentMode,_r=null,oc=null,Xo=null,sc=!1;function qd(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;sc||_r==null||_r!==Oi(r)||(r=_r,"selectionStart"in r&&xu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Xo&&us(Xo,r)||(Xo=r,r=$i(oc,"onSelect"),0<r.length&&(t=new gu("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=_r)))}function Ys(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Or={animationend:Ys("Animation","AnimationEnd"),animationiteration:Ys("Animation","AnimationIteration"),animationstart:Ys("Animation","AnimationStart"),transitionend:Ys("Transition","TransitionEnd")},ll={},Vh={};Zt&&(Vh=document.createElement("div").style,"AnimationEvent"in window||(delete Or.animationend.animation,delete Or.animationiteration.animation,delete Or.animationstart.animation),"TransitionEvent"in window||delete Or.transitionend.transition);function ma(e){if(ll[e])return ll[e];if(!Or[e])return e;var t=Or[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Vh)return ll[e]=t[n];return e}var Wh=ma("animationend"),Hh=ma("animationiteration"),Kh=ma("animationstart"),Qh=ma("transitionend"),Gh=new Map,Zd="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Hn(e,t){Gh.set(e,t),vr(t,[e])}for(var cl=0;cl<Zd.length;cl++){var ul=Zd[cl],Ox=ul.toLowerCase(),Dx=ul[0].toUpperCase()+ul.slice(1);Hn(Ox,"on"+Dx)}Hn(Wh,"onAnimationEnd");Hn(Hh,"onAnimationIteration");Hn(Kh,"onAnimationStart");Hn("dblclick","onDoubleClick");Hn("focusin","onFocus");Hn("focusout","onBlur");Hn(Qh,"onTransitionEnd");co("onMouseEnter",["mouseout","mouseover"]);co("onMouseLeave",["mouseout","mouseover"]);co("onPointerEnter",["pointerout","pointerover"]);co("onPointerLeave",["pointerout","pointerover"]);vr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));vr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));vr("onBeforeInput",["compositionend","keypress","textInput","paste"]);vr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));vr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));vr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Wo="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ix=new Set("cancel close invalid load scroll toggle".split(" ").concat(Wo));function Jd(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Oy(r,t,void 0,e),e.currentTarget=null}function Yh(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var i=r.length-1;0<=i;i--){var a=r[i],c=a.instance,u=a.currentTarget;if(a=a.listener,c!==s&&o.isPropagationStopped())break e;Jd(o,a,u),s=c}else for(i=0;i<r.length;i++){if(a=r[i],c=a.instance,u=a.currentTarget,a=a.listener,c!==s&&o.isPropagationStopped())break e;Jd(o,a,u),s=c}}}if(Ii)throw e=ec,Ii=!1,ec=null,e}function ie(e,t){var n=t[uc];n===void 0&&(n=t[uc]=new Set);var r=e+"__bubble";n.has(r)||(Xh(t,e,2,!1),n.add(r))}function dl(e,t,n){var r=0;t&&(r|=4),Xh(n,e,r,t)}var Xs="_reactListening"+Math.random().toString(36).slice(2);function ds(e){if(!e[Xs]){e[Xs]=!0,rh.forEach(function(n){n!=="selectionchange"&&(Ix.has(n)||dl(n,!1,e),dl(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Xs]||(t[Xs]=!0,dl("selectionchange",!1,t))}}function Xh(e,t,n,r){switch(Oh(t)){case 1:var o=Yy;break;case 4:o=Xy;break;default:o=hu}n=o.bind(null,t,n,e),o=void 0,!Jl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function fl(e,t,n,r,o){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var i=r.tag;if(i===3||i===4){var a=r.stateNode.containerInfo;if(a===o||a.nodeType===8&&a.parentNode===o)break;if(i===4)for(i=r.return;i!==null;){var c=i.tag;if((c===3||c===4)&&(c=i.stateNode.containerInfo,c===o||c.nodeType===8&&c.parentNode===o))return;i=i.return}for(;a!==null;){if(i=Zn(a),i===null)return;if(c=i.tag,c===5||c===6){r=s=i;continue e}a=a.parentNode}}r=r.return}xh(function(){var u=s,f=uu(n),p=[];e:{var d=Gh.get(e);if(d!==void 0){var x=gu,b=e;switch(e){case"keypress":if(xi(n)===0)break e;case"keydown":case"keyup":x=dx;break;case"focusin":b="focus",x=sl;break;case"focusout":b="blur",x=sl;break;case"beforeblur":case"afterblur":x=sl;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":x=Ud;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":x=Jy;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":x=hx;break;case Wh:case Hh:case Kh:x=nx;break;case Qh:x=gx;break;case"scroll":x=qy;break;case"wheel":x=yx;break;case"copy":case"cut":case"paste":x=ox;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":x=Vd}var g=(t&4)!==0,S=!g&&e==="scroll",v=g?d!==null?d+"Capture":null:d;g=[];for(var m=u,y;m!==null;){y=m;var w=y.stateNode;if(y.tag===5&&w!==null&&(y=w,v!==null&&(w=ss(m,v),w!=null&&g.push(fs(m,w,y)))),S)break;m=m.return}0<g.length&&(d=new x(d,b,null,n,f),p.push({event:d,listeners:g}))}}if(!(t&7)){e:{if(d=e==="mouseover"||e==="pointerover",x=e==="mouseout"||e==="pointerout",d&&n!==ql&&(b=n.relatedTarget||n.fromElement)&&(Zn(b)||b[Jt]))break e;if((x||d)&&(d=f.window===f?f:(d=f.ownerDocument)?d.defaultView||d.parentWindow:window,x?(b=n.relatedTarget||n.toElement,x=u,b=b?Zn(b):null,b!==null&&(S=yr(b),b!==S||b.tag!==5&&b.tag!==6)&&(b=null)):(x=null,b=u),x!==b)){if(g=Ud,w="onMouseLeave",v="onMouseEnter",m="mouse",(e==="pointerout"||e==="pointerover")&&(g=Vd,w="onPointerLeave",v="onPointerEnter",m="pointer"),S=x==null?d:Dr(x),y=b==null?d:Dr(b),d=new g(w,m+"leave",x,n,f),d.target=S,d.relatedTarget=y,w=null,Zn(f)===u&&(g=new g(v,m+"enter",b,n,f),g.target=y,g.relatedTarget=S,w=g),S=w,x&&b)t:{for(g=x,v=b,m=0,y=g;y;y=Cr(y))m++;for(y=0,w=v;w;w=Cr(w))y++;for(;0<m-y;)g=Cr(g),m--;for(;0<y-m;)v=Cr(v),y--;for(;m--;){if(g===v||v!==null&&g===v.alternate)break t;g=Cr(g),v=Cr(v)}g=null}else g=null;x!==null&&ef(p,d,x,g,!1),b!==null&&S!==null&&ef(p,S,b,g,!0)}}e:{if(d=u?Dr(u):window,x=d.nodeName&&d.nodeName.toLowerCase(),x==="select"||x==="input"&&d.type==="file")var E=Cx;else if(Kd(d))if(Fh)E=Px;else{E=jx;var C=Nx}else(x=d.nodeName)&&x.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(E=Tx);if(E&&(E=E(e,u))){zh(p,E,n,f);break e}C&&C(e,d,u),e==="focusout"&&(C=d._wrapperState)&&C.controlled&&d.type==="number"&&Kl(d,"number",d.value)}switch(C=u?Dr(u):window,e){case"focusin":(Kd(C)||C.contentEditable==="true")&&(_r=C,oc=u,Xo=null);break;case"focusout":Xo=oc=_r=null;break;case"mousedown":sc=!0;break;case"contextmenu":case"mouseup":case"dragend":sc=!1,qd(p,n,f);break;case"selectionchange":if(_x)break;case"keydown":case"keyup":qd(p,n,f)}var k;if(yu)e:{switch(e){case"compositionstart":var j="onCompositionStart";break e;case"compositionend":j="onCompositionEnd";break e;case"compositionupdate":j="onCompositionUpdate";break e}j=void 0}else Ar?Mh(e,n)&&(j="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(j="onCompositionStart");j&&(Ih&&n.locale!=="ko"&&(Ar||j!=="onCompositionStart"?j==="onCompositionEnd"&&Ar&&(k=Dh()):(Nn=f,mu="value"in Nn?Nn.value:Nn.textContent,Ar=!0)),C=$i(u,j),0<C.length&&(j=new Bd(j,e,null,n,f),p.push({event:j,listeners:C}),k?j.data=k:(k=Lh(n),k!==null&&(j.data=k)))),(k=wx?Sx(e,n):bx(e,n))&&(u=$i(u,"onBeforeInput"),0<u.length&&(f=new Bd("onBeforeInput","beforeinput",null,n,f),p.push({event:f,listeners:u}),f.data=k))}Yh(p,t)})}function fs(e,t,n){return{instance:e,listener:t,currentTarget:n}}function $i(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,s=o.stateNode;o.tag===5&&s!==null&&(o=s,s=ss(e,n),s!=null&&r.unshift(fs(e,s,o)),s=ss(e,t),s!=null&&r.push(fs(e,s,o))),e=e.return}return r}function Cr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function ef(e,t,n,r,o){for(var s=t._reactName,i=[];n!==null&&n!==r;){var a=n,c=a.alternate,u=a.stateNode;if(c!==null&&c===r)break;a.tag===5&&u!==null&&(a=u,o?(c=ss(n,s),c!=null&&i.unshift(fs(n,c,a))):o||(c=ss(n,s),c!=null&&i.push(fs(n,c,a)))),n=n.return}i.length!==0&&e.push({event:t,listeners:i})}var Mx=/\r\n?/g,Lx=/\u0000|\uFFFD/g;function tf(e){return(typeof e=="string"?e:""+e).replace(Mx,`
`).replace(Lx,"")}function qs(e,t,n){if(t=tf(t),tf(e)!==t&&n)throw Error(P(425))}function Ui(){}var ic=null,ac=null;function lc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var cc=typeof setTimeout=="function"?setTimeout:void 0,zx=typeof clearTimeout=="function"?clearTimeout:void 0,nf=typeof Promise=="function"?Promise:void 0,Fx=typeof queueMicrotask=="function"?queueMicrotask:typeof nf<"u"?function(e){return nf.resolve(null).then(e).catch($x)}:cc;function $x(e){setTimeout(function(){throw e})}function pl(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),ls(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);ls(t)}function _n(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function rf(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Eo=Math.random().toString(36).slice(2),Ft="__reactFiber$"+Eo,ps="__reactProps$"+Eo,Jt="__reactContainer$"+Eo,uc="__reactEvents$"+Eo,Ux="__reactListeners$"+Eo,Bx="__reactHandles$"+Eo;function Zn(e){var t=e[Ft];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Jt]||n[Ft]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=rf(e);e!==null;){if(n=e[Ft])return n;e=rf(e)}return t}e=n,n=e.parentNode}return null}function As(e){return e=e[Ft]||e[Jt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Dr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(P(33))}function ga(e){return e[ps]||null}var dc=[],Ir=-1;function Kn(e){return{current:e}}function le(e){0>Ir||(e.current=dc[Ir],dc[Ir]=null,Ir--)}function re(e,t){Ir++,dc[Ir]=e.current,e.current=t}var Fn={},ze=Kn(Fn),Ge=Kn(!1),ar=Fn;function uo(e,t){var n=e.type.contextTypes;if(!n)return Fn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},s;for(s in n)o[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Ye(e){return e=e.childContextTypes,e!=null}function Bi(){le(Ge),le(ze)}function of(e,t,n){if(ze.current!==Fn)throw Error(P(168));re(ze,t),re(Ge,n)}function qh(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(P(108,Ny(e)||"Unknown",o));return he({},n,r)}function Vi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Fn,ar=ze.current,re(ze,e),re(Ge,Ge.current),!0}function sf(e,t,n){var r=e.stateNode;if(!r)throw Error(P(169));n?(e=qh(e,t,ar),r.__reactInternalMemoizedMergedChildContext=e,le(Ge),le(ze),re(ze,e)):le(Ge),re(Ge,n)}var Gt=null,va=!1,hl=!1;function Zh(e){Gt===null?Gt=[e]:Gt.push(e)}function Vx(e){va=!0,Zh(e)}function Qn(){if(!hl&&Gt!==null){hl=!0;var e=0,t=ee;try{var n=Gt;for(ee=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Gt=null,va=!1}catch(o){throw Gt!==null&&(Gt=Gt.slice(e+1)),Eh(du,Qn),o}finally{ee=t,hl=!1}}return null}var Mr=[],Lr=0,Wi=null,Hi=0,ut=[],dt=0,lr=null,Yt=1,Xt="";function Xn(e,t){Mr[Lr++]=Hi,Mr[Lr++]=Wi,Wi=e,Hi=t}function Jh(e,t,n){ut[dt++]=Yt,ut[dt++]=Xt,ut[dt++]=lr,lr=e;var r=Yt;e=Xt;var o=32-Ct(r)-1;r&=~(1<<o),n+=1;var s=32-Ct(t)+o;if(30<s){var i=o-o%5;s=(r&(1<<i)-1).toString(32),r>>=i,o-=i,Yt=1<<32-Ct(t)+o|n<<o|r,Xt=s+e}else Yt=1<<s|n<<o|r,Xt=e}function wu(e){e.return!==null&&(Xn(e,1),Jh(e,1,0))}function Su(e){for(;e===Wi;)Wi=Mr[--Lr],Mr[Lr]=null,Hi=Mr[--Lr],Mr[Lr]=null;for(;e===lr;)lr=ut[--dt],ut[dt]=null,Xt=ut[--dt],ut[dt]=null,Yt=ut[--dt],ut[dt]=null}var ot=null,rt=null,ue=!1,kt=null;function em(e,t){var n=pt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function af(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,ot=e,rt=_n(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,ot=e,rt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=lr!==null?{id:Yt,overflow:Xt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=pt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,ot=e,rt=null,!0):!1;default:return!1}}function fc(e){return(e.mode&1)!==0&&(e.flags&128)===0}function pc(e){if(ue){var t=rt;if(t){var n=t;if(!af(e,t)){if(fc(e))throw Error(P(418));t=_n(n.nextSibling);var r=ot;t&&af(e,t)?em(r,n):(e.flags=e.flags&-4097|2,ue=!1,ot=e)}}else{if(fc(e))throw Error(P(418));e.flags=e.flags&-4097|2,ue=!1,ot=e}}}function lf(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;ot=e}function Zs(e){if(e!==ot)return!1;if(!ue)return lf(e),ue=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!lc(e.type,e.memoizedProps)),t&&(t=rt)){if(fc(e))throw tm(),Error(P(418));for(;t;)em(e,t),t=_n(t.nextSibling)}if(lf(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(P(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){rt=_n(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}rt=null}}else rt=ot?_n(e.stateNode.nextSibling):null;return!0}function tm(){for(var e=rt;e;)e=_n(e.nextSibling)}function fo(){rt=ot=null,ue=!1}function bu(e){kt===null?kt=[e]:kt.push(e)}var Wx=on.ReactCurrentBatchConfig;function Mo(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(P(309));var r=n.stateNode}if(!r)throw Error(P(147,e));var o=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(i){var a=o.refs;i===null?delete a[s]:a[s]=i},t._stringRef=s,t)}if(typeof e!="string")throw Error(P(284));if(!n._owner)throw Error(P(290,e))}return e}function Js(e,t){throw e=Object.prototype.toString.call(t),Error(P(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function cf(e){var t=e._init;return t(e._payload)}function nm(e){function t(v,m){if(e){var y=v.deletions;y===null?(v.deletions=[m],v.flags|=16):y.push(m)}}function n(v,m){if(!e)return null;for(;m!==null;)t(v,m),m=m.sibling;return null}function r(v,m){for(v=new Map;m!==null;)m.key!==null?v.set(m.key,m):v.set(m.index,m),m=m.sibling;return v}function o(v,m){return v=Mn(v,m),v.index=0,v.sibling=null,v}function s(v,m,y){return v.index=y,e?(y=v.alternate,y!==null?(y=y.index,y<m?(v.flags|=2,m):y):(v.flags|=2,m)):(v.flags|=1048576,m)}function i(v){return e&&v.alternate===null&&(v.flags|=2),v}function a(v,m,y,w){return m===null||m.tag!==6?(m=Sl(y,v.mode,w),m.return=v,m):(m=o(m,y),m.return=v,m)}function c(v,m,y,w){var E=y.type;return E===Rr?f(v,m,y.props.children,w,y.key):m!==null&&(m.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===vn&&cf(E)===m.type)?(w=o(m,y.props),w.ref=Mo(v,m,y),w.return=v,w):(w=Ni(y.type,y.key,y.props,null,v.mode,w),w.ref=Mo(v,m,y),w.return=v,w)}function u(v,m,y,w){return m===null||m.tag!==4||m.stateNode.containerInfo!==y.containerInfo||m.stateNode.implementation!==y.implementation?(m=bl(y,v.mode,w),m.return=v,m):(m=o(m,y.children||[]),m.return=v,m)}function f(v,m,y,w,E){return m===null||m.tag!==7?(m=ir(y,v.mode,w,E),m.return=v,m):(m=o(m,y),m.return=v,m)}function p(v,m,y){if(typeof m=="string"&&m!==""||typeof m=="number")return m=Sl(""+m,v.mode,y),m.return=v,m;if(typeof m=="object"&&m!==null){switch(m.$$typeof){case Bs:return y=Ni(m.type,m.key,m.props,null,v.mode,y),y.ref=Mo(v,null,m),y.return=v,y;case Pr:return m=bl(m,v.mode,y),m.return=v,m;case vn:var w=m._init;return p(v,w(m._payload),y)}if(Bo(m)||Ao(m))return m=ir(m,v.mode,y,null),m.return=v,m;Js(v,m)}return null}function d(v,m,y,w){var E=m!==null?m.key:null;if(typeof y=="string"&&y!==""||typeof y=="number")return E!==null?null:a(v,m,""+y,w);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case Bs:return y.key===E?c(v,m,y,w):null;case Pr:return y.key===E?u(v,m,y,w):null;case vn:return E=y._init,d(v,m,E(y._payload),w)}if(Bo(y)||Ao(y))return E!==null?null:f(v,m,y,w,null);Js(v,y)}return null}function x(v,m,y,w,E){if(typeof w=="string"&&w!==""||typeof w=="number")return v=v.get(y)||null,a(m,v,""+w,E);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case Bs:return v=v.get(w.key===null?y:w.key)||null,c(m,v,w,E);case Pr:return v=v.get(w.key===null?y:w.key)||null,u(m,v,w,E);case vn:var C=w._init;return x(v,m,y,C(w._payload),E)}if(Bo(w)||Ao(w))return v=v.get(y)||null,f(m,v,w,E,null);Js(m,w)}return null}function b(v,m,y,w){for(var E=null,C=null,k=m,j=m=0,O=null;k!==null&&j<y.length;j++){k.index>j?(O=k,k=null):O=k.sibling;var _=d(v,k,y[j],w);if(_===null){k===null&&(k=O);break}e&&k&&_.alternate===null&&t(v,k),m=s(_,m,j),C===null?E=_:C.sibling=_,C=_,k=O}if(j===y.length)return n(v,k),ue&&Xn(v,j),E;if(k===null){for(;j<y.length;j++)k=p(v,y[j],w),k!==null&&(m=s(k,m,j),C===null?E=k:C.sibling=k,C=k);return ue&&Xn(v,j),E}for(k=r(v,k);j<y.length;j++)O=x(k,v,j,y[j],w),O!==null&&(e&&O.alternate!==null&&k.delete(O.key===null?j:O.key),m=s(O,m,j),C===null?E=O:C.sibling=O,C=O);return e&&k.forEach(function(F){return t(v,F)}),ue&&Xn(v,j),E}function g(v,m,y,w){var E=Ao(y);if(typeof E!="function")throw Error(P(150));if(y=E.call(y),y==null)throw Error(P(151));for(var C=E=null,k=m,j=m=0,O=null,_=y.next();k!==null&&!_.done;j++,_=y.next()){k.index>j?(O=k,k=null):O=k.sibling;var F=d(v,k,_.value,w);if(F===null){k===null&&(k=O);break}e&&k&&F.alternate===null&&t(v,k),m=s(F,m,j),C===null?E=F:C.sibling=F,C=F,k=O}if(_.done)return n(v,k),ue&&Xn(v,j),E;if(k===null){for(;!_.done;j++,_=y.next())_=p(v,_.value,w),_!==null&&(m=s(_,m,j),C===null?E=_:C.sibling=_,C=_);return ue&&Xn(v,j),E}for(k=r(v,k);!_.done;j++,_=y.next())_=x(k,v,j,_.value,w),_!==null&&(e&&_.alternate!==null&&k.delete(_.key===null?j:_.key),m=s(_,m,j),C===null?E=_:C.sibling=_,C=_);return e&&k.forEach(function(M){return t(v,M)}),ue&&Xn(v,j),E}function S(v,m,y,w){if(typeof y=="object"&&y!==null&&y.type===Rr&&y.key===null&&(y=y.props.children),typeof y=="object"&&y!==null){switch(y.$$typeof){case Bs:e:{for(var E=y.key,C=m;C!==null;){if(C.key===E){if(E=y.type,E===Rr){if(C.tag===7){n(v,C.sibling),m=o(C,y.props.children),m.return=v,v=m;break e}}else if(C.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===vn&&cf(E)===C.type){n(v,C.sibling),m=o(C,y.props),m.ref=Mo(v,C,y),m.return=v,v=m;break e}n(v,C);break}else t(v,C);C=C.sibling}y.type===Rr?(m=ir(y.props.children,v.mode,w,y.key),m.return=v,v=m):(w=Ni(y.type,y.key,y.props,null,v.mode,w),w.ref=Mo(v,m,y),w.return=v,v=w)}return i(v);case Pr:e:{for(C=y.key;m!==null;){if(m.key===C)if(m.tag===4&&m.stateNode.containerInfo===y.containerInfo&&m.stateNode.implementation===y.implementation){n(v,m.sibling),m=o(m,y.children||[]),m.return=v,v=m;break e}else{n(v,m);break}else t(v,m);m=m.sibling}m=bl(y,v.mode,w),m.return=v,v=m}return i(v);case vn:return C=y._init,S(v,m,C(y._payload),w)}if(Bo(y))return b(v,m,y,w);if(Ao(y))return g(v,m,y,w);Js(v,y)}return typeof y=="string"&&y!==""||typeof y=="number"?(y=""+y,m!==null&&m.tag===6?(n(v,m.sibling),m=o(m,y),m.return=v,v=m):(n(v,m),m=Sl(y,v.mode,w),m.return=v,v=m),i(v)):n(v,m)}return S}var po=nm(!0),rm=nm(!1),Ki=Kn(null),Qi=null,zr=null,Eu=null;function ku(){Eu=zr=Qi=null}function Cu(e){var t=Ki.current;le(Ki),e._currentValue=t}function hc(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Kr(e,t){Qi=e,Eu=zr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Qe=!0),e.firstContext=null)}function mt(e){var t=e._currentValue;if(Eu!==e)if(e={context:e,memoizedValue:t,next:null},zr===null){if(Qi===null)throw Error(P(308));zr=e,Qi.dependencies={lanes:0,firstContext:e}}else zr=zr.next=e;return t}var Jn=null;function Nu(e){Jn===null?Jn=[e]:Jn.push(e)}function om(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,Nu(t)):(n.next=o.next,o.next=n),t.interleaved=n,en(e,r)}function en(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var yn=!1;function ju(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function sm(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function qt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function On(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,X&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,en(e,n)}return o=r.interleaved,o===null?(t.next=t,Nu(r)):(t.next=o.next,o.next=t),r.interleaved=t,en(e,n)}function wi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,fu(e,n)}}function uf(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?o=s=i:s=s.next=i,n=n.next}while(n!==null);s===null?o=s=t:s=s.next=t}else o=s=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Gi(e,t,n,r){var o=e.updateQueue;yn=!1;var s=o.firstBaseUpdate,i=o.lastBaseUpdate,a=o.shared.pending;if(a!==null){o.shared.pending=null;var c=a,u=c.next;c.next=null,i===null?s=u:i.next=u,i=c;var f=e.alternate;f!==null&&(f=f.updateQueue,a=f.lastBaseUpdate,a!==i&&(a===null?f.firstBaseUpdate=u:a.next=u,f.lastBaseUpdate=c))}if(s!==null){var p=o.baseState;i=0,f=u=c=null,a=s;do{var d=a.lane,x=a.eventTime;if((r&d)===d){f!==null&&(f=f.next={eventTime:x,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var b=e,g=a;switch(d=t,x=n,g.tag){case 1:if(b=g.payload,typeof b=="function"){p=b.call(x,p,d);break e}p=b;break e;case 3:b.flags=b.flags&-65537|128;case 0:if(b=g.payload,d=typeof b=="function"?b.call(x,p,d):b,d==null)break e;p=he({},p,d);break e;case 2:yn=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,d=o.effects,d===null?o.effects=[a]:d.push(a))}else x={eventTime:x,lane:d,tag:a.tag,payload:a.payload,callback:a.callback,next:null},f===null?(u=f=x,c=p):f=f.next=x,i|=d;if(a=a.next,a===null){if(a=o.shared.pending,a===null)break;d=a,a=d.next,d.next=null,o.lastBaseUpdate=d,o.shared.pending=null}}while(!0);if(f===null&&(c=p),o.baseState=c,o.firstBaseUpdate=u,o.lastBaseUpdate=f,t=o.shared.interleaved,t!==null){o=t;do i|=o.lane,o=o.next;while(o!==t)}else s===null&&(o.shared.lanes=0);ur|=i,e.lanes=i,e.memoizedState=p}}function df(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(P(191,o));o.call(r)}}}var _s={},Bt=Kn(_s),hs=Kn(_s),ms=Kn(_s);function er(e){if(e===_s)throw Error(P(174));return e}function Tu(e,t){switch(re(ms,t),re(hs,e),re(Bt,_s),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Gl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Gl(t,e)}le(Bt),re(Bt,t)}function ho(){le(Bt),le(hs),le(ms)}function im(e){er(ms.current);var t=er(Bt.current),n=Gl(t,e.type);t!==n&&(re(hs,e),re(Bt,n))}function Pu(e){hs.current===e&&(le(Bt),le(hs))}var fe=Kn(0);function Yi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ml=[];function Ru(){for(var e=0;e<ml.length;e++)ml[e]._workInProgressVersionPrimary=null;ml.length=0}var Si=on.ReactCurrentDispatcher,gl=on.ReactCurrentBatchConfig,cr=0,pe=null,Se=null,Ee=null,Xi=!1,qo=!1,gs=0,Hx=0;function _e(){throw Error(P(321))}function Au(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!jt(e[n],t[n]))return!1;return!0}function _u(e,t,n,r,o,s){if(cr=s,pe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Si.current=e===null||e.memoizedState===null?Yx:Xx,e=n(r,o),qo){s=0;do{if(qo=!1,gs=0,25<=s)throw Error(P(301));s+=1,Ee=Se=null,t.updateQueue=null,Si.current=qx,e=n(r,o)}while(qo)}if(Si.current=qi,t=Se!==null&&Se.next!==null,cr=0,Ee=Se=pe=null,Xi=!1,t)throw Error(P(300));return e}function Ou(){var e=gs!==0;return gs=0,e}function It(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ee===null?pe.memoizedState=Ee=e:Ee=Ee.next=e,Ee}function gt(){if(Se===null){var e=pe.alternate;e=e!==null?e.memoizedState:null}else e=Se.next;var t=Ee===null?pe.memoizedState:Ee.next;if(t!==null)Ee=t,Se=e;else{if(e===null)throw Error(P(310));Se=e,e={memoizedState:Se.memoizedState,baseState:Se.baseState,baseQueue:Se.baseQueue,queue:Se.queue,next:null},Ee===null?pe.memoizedState=Ee=e:Ee=Ee.next=e}return Ee}function vs(e,t){return typeof t=="function"?t(e):t}function vl(e){var t=gt(),n=t.queue;if(n===null)throw Error(P(311));n.lastRenderedReducer=e;var r=Se,o=r.baseQueue,s=n.pending;if(s!==null){if(o!==null){var i=o.next;o.next=s.next,s.next=i}r.baseQueue=o=s,n.pending=null}if(o!==null){s=o.next,r=r.baseState;var a=i=null,c=null,u=s;do{var f=u.lane;if((cr&f)===f)c!==null&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var p={lane:f,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};c===null?(a=c=p,i=r):c=c.next=p,pe.lanes|=f,ur|=f}u=u.next}while(u!==null&&u!==s);c===null?i=r:c.next=a,jt(r,t.memoizedState)||(Qe=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=c,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do s=o.lane,pe.lanes|=s,ur|=s,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function yl(e){var t=gt(),n=t.queue;if(n===null)throw Error(P(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,s=t.memoizedState;if(o!==null){n.pending=null;var i=o=o.next;do s=e(s,i.action),i=i.next;while(i!==o);jt(s,t.memoizedState)||(Qe=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function am(){}function lm(e,t){var n=pe,r=gt(),o=t(),s=!jt(r.memoizedState,o);if(s&&(r.memoizedState=o,Qe=!0),r=r.queue,Du(dm.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||Ee!==null&&Ee.memoizedState.tag&1){if(n.flags|=2048,ys(9,um.bind(null,n,r,o,t),void 0,null),ke===null)throw Error(P(349));cr&30||cm(n,t,o)}return o}function cm(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=pe.updateQueue,t===null?(t={lastEffect:null,stores:null},pe.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function um(e,t,n,r){t.value=n,t.getSnapshot=r,fm(t)&&pm(e)}function dm(e,t,n){return n(function(){fm(t)&&pm(e)})}function fm(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!jt(e,n)}catch{return!0}}function pm(e){var t=en(e,1);t!==null&&Nt(t,e,1,-1)}function ff(e){var t=It();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:vs,lastRenderedState:e},t.queue=e,e=e.dispatch=Gx.bind(null,pe,e),[t.memoizedState,e]}function ys(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=pe.updateQueue,t===null?(t={lastEffect:null,stores:null},pe.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function hm(){return gt().memoizedState}function bi(e,t,n,r){var o=It();pe.flags|=e,o.memoizedState=ys(1|t,n,void 0,r===void 0?null:r)}function ya(e,t,n,r){var o=gt();r=r===void 0?null:r;var s=void 0;if(Se!==null){var i=Se.memoizedState;if(s=i.destroy,r!==null&&Au(r,i.deps)){o.memoizedState=ys(t,n,s,r);return}}pe.flags|=e,o.memoizedState=ys(1|t,n,s,r)}function pf(e,t){return bi(8390656,8,e,t)}function Du(e,t){return ya(2048,8,e,t)}function mm(e,t){return ya(4,2,e,t)}function gm(e,t){return ya(4,4,e,t)}function vm(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function ym(e,t,n){return n=n!=null?n.concat([e]):null,ya(4,4,vm.bind(null,t,e),n)}function Iu(){}function xm(e,t){var n=gt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Au(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function wm(e,t){var n=gt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Au(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Sm(e,t,n){return cr&21?(jt(n,t)||(n=Nh(),pe.lanes|=n,ur|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Qe=!0),e.memoizedState=n)}function Kx(e,t){var n=ee;ee=n!==0&&4>n?n:4,e(!0);var r=gl.transition;gl.transition={};try{e(!1),t()}finally{ee=n,gl.transition=r}}function bm(){return gt().memoizedState}function Qx(e,t,n){var r=In(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Em(e))km(t,n);else if(n=om(e,t,n,r),n!==null){var o=Be();Nt(n,e,r,o),Cm(n,t,r)}}function Gx(e,t,n){var r=In(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Em(e))km(t,o);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var i=t.lastRenderedState,a=s(i,n);if(o.hasEagerState=!0,o.eagerState=a,jt(a,i)){var c=t.interleaved;c===null?(o.next=o,Nu(t)):(o.next=c.next,c.next=o),t.interleaved=o;return}}catch{}finally{}n=om(e,t,o,r),n!==null&&(o=Be(),Nt(n,e,r,o),Cm(n,t,r))}}function Em(e){var t=e.alternate;return e===pe||t!==null&&t===pe}function km(e,t){qo=Xi=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Cm(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,fu(e,n)}}var qi={readContext:mt,useCallback:_e,useContext:_e,useEffect:_e,useImperativeHandle:_e,useInsertionEffect:_e,useLayoutEffect:_e,useMemo:_e,useReducer:_e,useRef:_e,useState:_e,useDebugValue:_e,useDeferredValue:_e,useTransition:_e,useMutableSource:_e,useSyncExternalStore:_e,useId:_e,unstable_isNewReconciler:!1},Yx={readContext:mt,useCallback:function(e,t){return It().memoizedState=[e,t===void 0?null:t],e},useContext:mt,useEffect:pf,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,bi(4194308,4,vm.bind(null,t,e),n)},useLayoutEffect:function(e,t){return bi(4194308,4,e,t)},useInsertionEffect:function(e,t){return bi(4,2,e,t)},useMemo:function(e,t){var n=It();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=It();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Qx.bind(null,pe,e),[r.memoizedState,e]},useRef:function(e){var t=It();return e={current:e},t.memoizedState=e},useState:ff,useDebugValue:Iu,useDeferredValue:function(e){return It().memoizedState=e},useTransition:function(){var e=ff(!1),t=e[0];return e=Kx.bind(null,e[1]),It().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=pe,o=It();if(ue){if(n===void 0)throw Error(P(407));n=n()}else{if(n=t(),ke===null)throw Error(P(349));cr&30||cm(r,t,n)}o.memoizedState=n;var s={value:n,getSnapshot:t};return o.queue=s,pf(dm.bind(null,r,s,e),[e]),r.flags|=2048,ys(9,um.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=It(),t=ke.identifierPrefix;if(ue){var n=Xt,r=Yt;n=(r&~(1<<32-Ct(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=gs++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Hx++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Xx={readContext:mt,useCallback:xm,useContext:mt,useEffect:Du,useImperativeHandle:ym,useInsertionEffect:mm,useLayoutEffect:gm,useMemo:wm,useReducer:vl,useRef:hm,useState:function(){return vl(vs)},useDebugValue:Iu,useDeferredValue:function(e){var t=gt();return Sm(t,Se.memoizedState,e)},useTransition:function(){var e=vl(vs)[0],t=gt().memoizedState;return[e,t]},useMutableSource:am,useSyncExternalStore:lm,useId:bm,unstable_isNewReconciler:!1},qx={readContext:mt,useCallback:xm,useContext:mt,useEffect:Du,useImperativeHandle:ym,useInsertionEffect:mm,useLayoutEffect:gm,useMemo:wm,useReducer:yl,useRef:hm,useState:function(){return yl(vs)},useDebugValue:Iu,useDeferredValue:function(e){var t=gt();return Se===null?t.memoizedState=e:Sm(t,Se.memoizedState,e)},useTransition:function(){var e=yl(vs)[0],t=gt().memoizedState;return[e,t]},useMutableSource:am,useSyncExternalStore:lm,useId:bm,unstable_isNewReconciler:!1};function wt(e,t){if(e&&e.defaultProps){t=he({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function mc(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:he({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var xa={isMounted:function(e){return(e=e._reactInternals)?yr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Be(),o=In(e),s=qt(r,o);s.payload=t,n!=null&&(s.callback=n),t=On(e,s,o),t!==null&&(Nt(t,e,o,r),wi(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Be(),o=In(e),s=qt(r,o);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=On(e,s,o),t!==null&&(Nt(t,e,o,r),wi(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Be(),r=In(e),o=qt(n,r);o.tag=2,t!=null&&(o.callback=t),t=On(e,o,r),t!==null&&(Nt(t,e,r,n),wi(t,e,r))}};function hf(e,t,n,r,o,s,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,i):t.prototype&&t.prototype.isPureReactComponent?!us(n,r)||!us(o,s):!0}function Nm(e,t,n){var r=!1,o=Fn,s=t.contextType;return typeof s=="object"&&s!==null?s=mt(s):(o=Ye(t)?ar:ze.current,r=t.contextTypes,s=(r=r!=null)?uo(e,o):Fn),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=xa,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=s),t}function mf(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&xa.enqueueReplaceState(t,t.state,null)}function gc(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},ju(e);var s=t.contextType;typeof s=="object"&&s!==null?o.context=mt(s):(s=Ye(t)?ar:ze.current,o.context=uo(e,s)),o.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(mc(e,t,s,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&xa.enqueueReplaceState(o,o.state,null),Gi(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function mo(e,t){try{var n="",r=t;do n+=Cy(r),r=r.return;while(r);var o=n}catch(s){o=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:o,digest:null}}function xl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function vc(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Zx=typeof WeakMap=="function"?WeakMap:Map;function jm(e,t,n){n=qt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ji||(Ji=!0,jc=r),vc(e,t)},n}function Tm(e,t,n){n=qt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){vc(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){vc(e,t),typeof r!="function"&&(Dn===null?Dn=new Set([this]):Dn.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),n}function gf(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Zx;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=fw.bind(null,e,t,n),t.then(e,e))}function vf(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function yf(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=qt(-1,1),t.tag=2,On(n,t,1))),n.lanes|=1),e)}var Jx=on.ReactCurrentOwner,Qe=!1;function $e(e,t,n,r){t.child=e===null?rm(t,null,n,r):po(t,e.child,n,r)}function xf(e,t,n,r,o){n=n.render;var s=t.ref;return Kr(t,o),r=_u(e,t,n,r,s,o),n=Ou(),e!==null&&!Qe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,tn(e,t,o)):(ue&&n&&wu(t),t.flags|=1,$e(e,t,r,o),t.child)}function wf(e,t,n,r,o){if(e===null){var s=n.type;return typeof s=="function"&&!Vu(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,Pm(e,t,s,r,o)):(e=Ni(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&o)){var i=s.memoizedProps;if(n=n.compare,n=n!==null?n:us,n(i,r)&&e.ref===t.ref)return tn(e,t,o)}return t.flags|=1,e=Mn(s,r),e.ref=t.ref,e.return=t,t.child=e}function Pm(e,t,n,r,o){if(e!==null){var s=e.memoizedProps;if(us(s,r)&&e.ref===t.ref)if(Qe=!1,t.pendingProps=r=s,(e.lanes&o)!==0)e.flags&131072&&(Qe=!0);else return t.lanes=e.lanes,tn(e,t,o)}return yc(e,t,n,r,o)}function Rm(e,t,n){var r=t.pendingProps,o=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},re($r,et),et|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,re($r,et),et|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,re($r,et),et|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,re($r,et),et|=r;return $e(e,t,o,n),t.child}function Am(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function yc(e,t,n,r,o){var s=Ye(n)?ar:ze.current;return s=uo(t,s),Kr(t,o),n=_u(e,t,n,r,s,o),r=Ou(),e!==null&&!Qe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,tn(e,t,o)):(ue&&r&&wu(t),t.flags|=1,$e(e,t,n,o),t.child)}function Sf(e,t,n,r,o){if(Ye(n)){var s=!0;Vi(t)}else s=!1;if(Kr(t,o),t.stateNode===null)Ei(e,t),Nm(t,n,r),gc(t,n,r,o),r=!0;else if(e===null){var i=t.stateNode,a=t.memoizedProps;i.props=a;var c=i.context,u=n.contextType;typeof u=="object"&&u!==null?u=mt(u):(u=Ye(n)?ar:ze.current,u=uo(t,u));var f=n.getDerivedStateFromProps,p=typeof f=="function"||typeof i.getSnapshotBeforeUpdate=="function";p||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==r||c!==u)&&mf(t,i,r,u),yn=!1;var d=t.memoizedState;i.state=d,Gi(t,r,i,o),c=t.memoizedState,a!==r||d!==c||Ge.current||yn?(typeof f=="function"&&(mc(t,n,f,r),c=t.memoizedState),(a=yn||hf(t,n,a,r,d,c,u))?(p||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=c),i.props=r,i.state=c,i.context=u,r=a):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,sm(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:wt(t.type,a),i.props=u,p=t.pendingProps,d=i.context,c=n.contextType,typeof c=="object"&&c!==null?c=mt(c):(c=Ye(n)?ar:ze.current,c=uo(t,c));var x=n.getDerivedStateFromProps;(f=typeof x=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==p||d!==c)&&mf(t,i,r,c),yn=!1,d=t.memoizedState,i.state=d,Gi(t,r,i,o);var b=t.memoizedState;a!==p||d!==b||Ge.current||yn?(typeof x=="function"&&(mc(t,n,x,r),b=t.memoizedState),(u=yn||hf(t,n,u,r,d,b,c)||!1)?(f||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(r,b,c),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(r,b,c)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=b),i.props=r,i.state=b,i.context=c,r=u):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return xc(e,t,n,r,s,o)}function xc(e,t,n,r,o,s){Am(e,t);var i=(t.flags&128)!==0;if(!r&&!i)return o&&sf(t,n,!1),tn(e,t,s);r=t.stateNode,Jx.current=t;var a=i&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&i?(t.child=po(t,e.child,null,s),t.child=po(t,null,a,s)):$e(e,t,a,s),t.memoizedState=r.state,o&&sf(t,n,!0),t.child}function _m(e){var t=e.stateNode;t.pendingContext?of(e,t.pendingContext,t.pendingContext!==t.context):t.context&&of(e,t.context,!1),Tu(e,t.containerInfo)}function bf(e,t,n,r,o){return fo(),bu(o),t.flags|=256,$e(e,t,n,r),t.child}var wc={dehydrated:null,treeContext:null,retryLane:0};function Sc(e){return{baseLanes:e,cachePool:null,transitions:null}}function Om(e,t,n){var r=t.pendingProps,o=fe.current,s=!1,i=(t.flags&128)!==0,a;if((a=i)||(a=e!==null&&e.memoizedState===null?!1:(o&2)!==0),a?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),re(fe,o&1),e===null)return pc(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(i=r.children,e=r.fallback,s?(r=t.mode,s=t.child,i={mode:"hidden",children:i},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=i):s=ba(i,r,0,null),e=ir(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=Sc(n),t.memoizedState=wc,e):Mu(t,i));if(o=e.memoizedState,o!==null&&(a=o.dehydrated,a!==null))return ew(e,t,i,r,a,o,n);if(s){s=r.fallback,i=t.mode,o=e.child,a=o.sibling;var c={mode:"hidden",children:r.children};return!(i&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=c,t.deletions=null):(r=Mn(o,c),r.subtreeFlags=o.subtreeFlags&14680064),a!==null?s=Mn(a,s):(s=ir(s,i,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,i=e.child.memoizedState,i=i===null?Sc(n):{baseLanes:i.baseLanes|n,cachePool:null,transitions:i.transitions},s.memoizedState=i,s.childLanes=e.childLanes&~n,t.memoizedState=wc,r}return s=e.child,e=s.sibling,r=Mn(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Mu(e,t){return t=ba({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function ei(e,t,n,r){return r!==null&&bu(r),po(t,e.child,null,n),e=Mu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function ew(e,t,n,r,o,s,i){if(n)return t.flags&256?(t.flags&=-257,r=xl(Error(P(422))),ei(e,t,i,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,o=t.mode,r=ba({mode:"visible",children:r.children},o,0,null),s=ir(s,o,i,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&po(t,e.child,null,i),t.child.memoizedState=Sc(i),t.memoizedState=wc,s);if(!(t.mode&1))return ei(e,t,i,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var a=r.dgst;return r=a,s=Error(P(419)),r=xl(s,r,void 0),ei(e,t,i,r)}if(a=(i&e.childLanes)!==0,Qe||a){if(r=ke,r!==null){switch(i&-i){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|i)?0:o,o!==0&&o!==s.retryLane&&(s.retryLane=o,en(e,o),Nt(r,e,o,-1))}return Bu(),r=xl(Error(P(421))),ei(e,t,i,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=pw.bind(null,e),o._reactRetry=t,null):(e=s.treeContext,rt=_n(o.nextSibling),ot=t,ue=!0,kt=null,e!==null&&(ut[dt++]=Yt,ut[dt++]=Xt,ut[dt++]=lr,Yt=e.id,Xt=e.overflow,lr=t),t=Mu(t,r.children),t.flags|=4096,t)}function Ef(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),hc(e.return,t,n)}function wl(e,t,n,r,o){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=o)}function Dm(e,t,n){var r=t.pendingProps,o=r.revealOrder,s=r.tail;if($e(e,t,r.children,n),r=fe.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Ef(e,n,t);else if(e.tag===19)Ef(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(re(fe,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Yi(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),wl(t,!1,o,n,s);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Yi(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}wl(t,!0,n,null,s);break;case"together":wl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ei(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function tn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),ur|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(P(153));if(t.child!==null){for(e=t.child,n=Mn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Mn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function tw(e,t,n){switch(t.tag){case 3:_m(t),fo();break;case 5:im(t);break;case 1:Ye(t.type)&&Vi(t);break;case 4:Tu(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;re(Ki,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(re(fe,fe.current&1),t.flags|=128,null):n&t.child.childLanes?Om(e,t,n):(re(fe,fe.current&1),e=tn(e,t,n),e!==null?e.sibling:null);re(fe,fe.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Dm(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),re(fe,fe.current),r)break;return null;case 22:case 23:return t.lanes=0,Rm(e,t,n)}return tn(e,t,n)}var Im,bc,Mm,Lm;Im=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};bc=function(){};Mm=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,er(Bt.current);var s=null;switch(n){case"input":o=Wl(e,o),r=Wl(e,r),s=[];break;case"select":o=he({},o,{value:void 0}),r=he({},r,{value:void 0}),s=[];break;case"textarea":o=Ql(e,o),r=Ql(e,r),s=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Ui)}Yl(n,r);var i;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var a=o[u];for(i in a)a.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(rs.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var c=r[u];if(a=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&c!==a&&(c!=null||a!=null))if(u==="style")if(a){for(i in a)!a.hasOwnProperty(i)||c&&c.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in c)c.hasOwnProperty(i)&&a[i]!==c[i]&&(n||(n={}),n[i]=c[i])}else n||(s||(s=[]),s.push(u,n)),n=c;else u==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,a=a?a.__html:void 0,c!=null&&a!==c&&(s=s||[]).push(u,c)):u==="children"?typeof c!="string"&&typeof c!="number"||(s=s||[]).push(u,""+c):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(rs.hasOwnProperty(u)?(c!=null&&u==="onScroll"&&ie("scroll",e),s||a===c||(s=[])):(s=s||[]).push(u,c))}n&&(s=s||[]).push("style",n);var u=s;(t.updateQueue=u)&&(t.flags|=4)}};Lm=function(e,t,n,r){n!==r&&(t.flags|=4)};function Lo(e,t){if(!ue)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Oe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function nw(e,t,n){var r=t.pendingProps;switch(Su(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Oe(t),null;case 1:return Ye(t.type)&&Bi(),Oe(t),null;case 3:return r=t.stateNode,ho(),le(Ge),le(ze),Ru(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Zs(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,kt!==null&&(Rc(kt),kt=null))),bc(e,t),Oe(t),null;case 5:Pu(t);var o=er(ms.current);if(n=t.type,e!==null&&t.stateNode!=null)Mm(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(P(166));return Oe(t),null}if(e=er(Bt.current),Zs(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[Ft]=t,r[ps]=s,e=(t.mode&1)!==0,n){case"dialog":ie("cancel",r),ie("close",r);break;case"iframe":case"object":case"embed":ie("load",r);break;case"video":case"audio":for(o=0;o<Wo.length;o++)ie(Wo[o],r);break;case"source":ie("error",r);break;case"img":case"image":case"link":ie("error",r),ie("load",r);break;case"details":ie("toggle",r);break;case"input":Ad(r,s),ie("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},ie("invalid",r);break;case"textarea":Od(r,s),ie("invalid",r)}Yl(n,s),o=null;for(var i in s)if(s.hasOwnProperty(i)){var a=s[i];i==="children"?typeof a=="string"?r.textContent!==a&&(s.suppressHydrationWarning!==!0&&qs(r.textContent,a,e),o=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(s.suppressHydrationWarning!==!0&&qs(r.textContent,a,e),o=["children",""+a]):rs.hasOwnProperty(i)&&a!=null&&i==="onScroll"&&ie("scroll",r)}switch(n){case"input":Vs(r),_d(r,s,!0);break;case"textarea":Vs(r),Dd(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=Ui)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{i=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=dh(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=i.createElement(n,{is:r.is}):(e=i.createElement(n),n==="select"&&(i=e,r.multiple?i.multiple=!0:r.size&&(i.size=r.size))):e=i.createElementNS(e,n),e[Ft]=t,e[ps]=r,Im(e,t,!1,!1),t.stateNode=e;e:{switch(i=Xl(n,r),n){case"dialog":ie("cancel",e),ie("close",e),o=r;break;case"iframe":case"object":case"embed":ie("load",e),o=r;break;case"video":case"audio":for(o=0;o<Wo.length;o++)ie(Wo[o],e);o=r;break;case"source":ie("error",e),o=r;break;case"img":case"image":case"link":ie("error",e),ie("load",e),o=r;break;case"details":ie("toggle",e),o=r;break;case"input":Ad(e,r),o=Wl(e,r),ie("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=he({},r,{value:void 0}),ie("invalid",e);break;case"textarea":Od(e,r),o=Ql(e,r),ie("invalid",e);break;default:o=r}Yl(n,o),a=o;for(s in a)if(a.hasOwnProperty(s)){var c=a[s];s==="style"?hh(e,c):s==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,c!=null&&fh(e,c)):s==="children"?typeof c=="string"?(n!=="textarea"||c!=="")&&os(e,c):typeof c=="number"&&os(e,""+c):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(rs.hasOwnProperty(s)?c!=null&&s==="onScroll"&&ie("scroll",e):c!=null&&iu(e,s,c,i))}switch(n){case"input":Vs(e),_d(e,r,!1);break;case"textarea":Vs(e),Dd(e);break;case"option":r.value!=null&&e.setAttribute("value",""+zn(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?Br(e,!!r.multiple,s,!1):r.defaultValue!=null&&Br(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=Ui)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Oe(t),null;case 6:if(e&&t.stateNode!=null)Lm(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(P(166));if(n=er(ms.current),er(Bt.current),Zs(t)){if(r=t.stateNode,n=t.memoizedProps,r[Ft]=t,(s=r.nodeValue!==n)&&(e=ot,e!==null))switch(e.tag){case 3:qs(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&qs(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Ft]=t,t.stateNode=r}return Oe(t),null;case 13:if(le(fe),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ue&&rt!==null&&t.mode&1&&!(t.flags&128))tm(),fo(),t.flags|=98560,s=!1;else if(s=Zs(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(P(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(P(317));s[Ft]=t}else fo(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Oe(t),s=!1}else kt!==null&&(Rc(kt),kt=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||fe.current&1?be===0&&(be=3):Bu())),t.updateQueue!==null&&(t.flags|=4),Oe(t),null);case 4:return ho(),bc(e,t),e===null&&ds(t.stateNode.containerInfo),Oe(t),null;case 10:return Cu(t.type._context),Oe(t),null;case 17:return Ye(t.type)&&Bi(),Oe(t),null;case 19:if(le(fe),s=t.memoizedState,s===null)return Oe(t),null;if(r=(t.flags&128)!==0,i=s.rendering,i===null)if(r)Lo(s,!1);else{if(be!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(i=Yi(e),i!==null){for(t.flags|=128,Lo(s,!1),r=i.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,i=s.alternate,i===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=i.childLanes,s.lanes=i.lanes,s.child=i.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=i.memoizedProps,s.memoizedState=i.memoizedState,s.updateQueue=i.updateQueue,s.type=i.type,e=i.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return re(fe,fe.current&1|2),t.child}e=e.sibling}s.tail!==null&&ve()>go&&(t.flags|=128,r=!0,Lo(s,!1),t.lanes=4194304)}else{if(!r)if(e=Yi(i),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Lo(s,!0),s.tail===null&&s.tailMode==="hidden"&&!i.alternate&&!ue)return Oe(t),null}else 2*ve()-s.renderingStartTime>go&&n!==1073741824&&(t.flags|=128,r=!0,Lo(s,!1),t.lanes=4194304);s.isBackwards?(i.sibling=t.child,t.child=i):(n=s.last,n!==null?n.sibling=i:t.child=i,s.last=i)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=ve(),t.sibling=null,n=fe.current,re(fe,r?n&1|2:n&1),t):(Oe(t),null);case 22:case 23:return Uu(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?et&1073741824&&(Oe(t),t.subtreeFlags&6&&(t.flags|=8192)):Oe(t),null;case 24:return null;case 25:return null}throw Error(P(156,t.tag))}function rw(e,t){switch(Su(t),t.tag){case 1:return Ye(t.type)&&Bi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return ho(),le(Ge),le(ze),Ru(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Pu(t),null;case 13:if(le(fe),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(P(340));fo()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return le(fe),null;case 4:return ho(),null;case 10:return Cu(t.type._context),null;case 22:case 23:return Uu(),null;case 24:return null;default:return null}}var ti=!1,Le=!1,ow=typeof WeakSet=="function"?WeakSet:Set,I=null;function Fr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ge(e,t,r)}else n.current=null}function Ec(e,t,n){try{n()}catch(r){ge(e,t,r)}}var kf=!1;function sw(e,t){if(ic=zi,e=Bh(),xu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var i=0,a=-1,c=-1,u=0,f=0,p=e,d=null;t:for(;;){for(var x;p!==n||o!==0&&p.nodeType!==3||(a=i+o),p!==s||r!==0&&p.nodeType!==3||(c=i+r),p.nodeType===3&&(i+=p.nodeValue.length),(x=p.firstChild)!==null;)d=p,p=x;for(;;){if(p===e)break t;if(d===n&&++u===o&&(a=i),d===s&&++f===r&&(c=i),(x=p.nextSibling)!==null)break;p=d,d=p.parentNode}p=x}n=a===-1||c===-1?null:{start:a,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(ac={focusedElem:e,selectionRange:n},zi=!1,I=t;I!==null;)if(t=I,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,I=e;else for(;I!==null;){t=I;try{var b=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(b!==null){var g=b.memoizedProps,S=b.memoizedState,v=t.stateNode,m=v.getSnapshotBeforeUpdate(t.elementType===t.type?g:wt(t.type,g),S);v.__reactInternalSnapshotBeforeUpdate=m}break;case 3:var y=t.stateNode.containerInfo;y.nodeType===1?y.textContent="":y.nodeType===9&&y.documentElement&&y.removeChild(y.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(P(163))}}catch(w){ge(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,I=e;break}I=t.return}return b=kf,kf=!1,b}function Zo(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var s=o.destroy;o.destroy=void 0,s!==void 0&&Ec(t,n,s)}o=o.next}while(o!==r)}}function wa(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function kc(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function zm(e){var t=e.alternate;t!==null&&(e.alternate=null,zm(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Ft],delete t[ps],delete t[uc],delete t[Ux],delete t[Bx])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Fm(e){return e.tag===5||e.tag===3||e.tag===4}function Cf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Fm(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Cc(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Ui));else if(r!==4&&(e=e.child,e!==null))for(Cc(e,t,n),e=e.sibling;e!==null;)Cc(e,t,n),e=e.sibling}function Nc(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Nc(e,t,n),e=e.sibling;e!==null;)Nc(e,t,n),e=e.sibling}var Ne=null,Et=!1;function fn(e,t,n){for(n=n.child;n!==null;)$m(e,t,n),n=n.sibling}function $m(e,t,n){if(Ut&&typeof Ut.onCommitFiberUnmount=="function")try{Ut.onCommitFiberUnmount(fa,n)}catch{}switch(n.tag){case 5:Le||Fr(n,t);case 6:var r=Ne,o=Et;Ne=null,fn(e,t,n),Ne=r,Et=o,Ne!==null&&(Et?(e=Ne,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Ne.removeChild(n.stateNode));break;case 18:Ne!==null&&(Et?(e=Ne,n=n.stateNode,e.nodeType===8?pl(e.parentNode,n):e.nodeType===1&&pl(e,n),ls(e)):pl(Ne,n.stateNode));break;case 4:r=Ne,o=Et,Ne=n.stateNode.containerInfo,Et=!0,fn(e,t,n),Ne=r,Et=o;break;case 0:case 11:case 14:case 15:if(!Le&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var s=o,i=s.destroy;s=s.tag,i!==void 0&&(s&2||s&4)&&Ec(n,t,i),o=o.next}while(o!==r)}fn(e,t,n);break;case 1:if(!Le&&(Fr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){ge(n,t,a)}fn(e,t,n);break;case 21:fn(e,t,n);break;case 22:n.mode&1?(Le=(r=Le)||n.memoizedState!==null,fn(e,t,n),Le=r):fn(e,t,n);break;default:fn(e,t,n)}}function Nf(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new ow),t.forEach(function(r){var o=hw.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function xt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var s=e,i=t,a=i;e:for(;a!==null;){switch(a.tag){case 5:Ne=a.stateNode,Et=!1;break e;case 3:Ne=a.stateNode.containerInfo,Et=!0;break e;case 4:Ne=a.stateNode.containerInfo,Et=!0;break e}a=a.return}if(Ne===null)throw Error(P(160));$m(s,i,o),Ne=null,Et=!1;var c=o.alternate;c!==null&&(c.return=null),o.return=null}catch(u){ge(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Um(t,e),t=t.sibling}function Um(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(xt(t,e),Dt(e),r&4){try{Zo(3,e,e.return),wa(3,e)}catch(g){ge(e,e.return,g)}try{Zo(5,e,e.return)}catch(g){ge(e,e.return,g)}}break;case 1:xt(t,e),Dt(e),r&512&&n!==null&&Fr(n,n.return);break;case 5:if(xt(t,e),Dt(e),r&512&&n!==null&&Fr(n,n.return),e.flags&32){var o=e.stateNode;try{os(o,"")}catch(g){ge(e,e.return,g)}}if(r&4&&(o=e.stateNode,o!=null)){var s=e.memoizedProps,i=n!==null?n.memoizedProps:s,a=e.type,c=e.updateQueue;if(e.updateQueue=null,c!==null)try{a==="input"&&s.type==="radio"&&s.name!=null&&ch(o,s),Xl(a,i);var u=Xl(a,s);for(i=0;i<c.length;i+=2){var f=c[i],p=c[i+1];f==="style"?hh(o,p):f==="dangerouslySetInnerHTML"?fh(o,p):f==="children"?os(o,p):iu(o,f,p,u)}switch(a){case"input":Hl(o,s);break;case"textarea":uh(o,s);break;case"select":var d=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!s.multiple;var x=s.value;x!=null?Br(o,!!s.multiple,x,!1):d!==!!s.multiple&&(s.defaultValue!=null?Br(o,!!s.multiple,s.defaultValue,!0):Br(o,!!s.multiple,s.multiple?[]:"",!1))}o[ps]=s}catch(g){ge(e,e.return,g)}}break;case 6:if(xt(t,e),Dt(e),r&4){if(e.stateNode===null)throw Error(P(162));o=e.stateNode,s=e.memoizedProps;try{o.nodeValue=s}catch(g){ge(e,e.return,g)}}break;case 3:if(xt(t,e),Dt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{ls(t.containerInfo)}catch(g){ge(e,e.return,g)}break;case 4:xt(t,e),Dt(e);break;case 13:xt(t,e),Dt(e),o=e.child,o.flags&8192&&(s=o.memoizedState!==null,o.stateNode.isHidden=s,!s||o.alternate!==null&&o.alternate.memoizedState!==null||(Fu=ve())),r&4&&Nf(e);break;case 22:if(f=n!==null&&n.memoizedState!==null,e.mode&1?(Le=(u=Le)||f,xt(t,e),Le=u):xt(t,e),Dt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!f&&e.mode&1)for(I=e,f=e.child;f!==null;){for(p=I=f;I!==null;){switch(d=I,x=d.child,d.tag){case 0:case 11:case 14:case 15:Zo(4,d,d.return);break;case 1:Fr(d,d.return);var b=d.stateNode;if(typeof b.componentWillUnmount=="function"){r=d,n=d.return;try{t=r,b.props=t.memoizedProps,b.state=t.memoizedState,b.componentWillUnmount()}catch(g){ge(r,n,g)}}break;case 5:Fr(d,d.return);break;case 22:if(d.memoizedState!==null){Tf(p);continue}}x!==null?(x.return=d,I=x):Tf(p)}f=f.sibling}e:for(f=null,p=e;;){if(p.tag===5){if(f===null){f=p;try{o=p.stateNode,u?(s=o.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(a=p.stateNode,c=p.memoizedProps.style,i=c!=null&&c.hasOwnProperty("display")?c.display:null,a.style.display=ph("display",i))}catch(g){ge(e,e.return,g)}}}else if(p.tag===6){if(f===null)try{p.stateNode.nodeValue=u?"":p.memoizedProps}catch(g){ge(e,e.return,g)}}else if((p.tag!==22&&p.tag!==23||p.memoizedState===null||p===e)&&p.child!==null){p.child.return=p,p=p.child;continue}if(p===e)break e;for(;p.sibling===null;){if(p.return===null||p.return===e)break e;f===p&&(f=null),p=p.return}f===p&&(f=null),p.sibling.return=p.return,p=p.sibling}}break;case 19:xt(t,e),Dt(e),r&4&&Nf(e);break;case 21:break;default:xt(t,e),Dt(e)}}function Dt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Fm(n)){var r=n;break e}n=n.return}throw Error(P(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(os(o,""),r.flags&=-33);var s=Cf(e);Nc(e,s,o);break;case 3:case 4:var i=r.stateNode.containerInfo,a=Cf(e);Cc(e,a,i);break;default:throw Error(P(161))}}catch(c){ge(e,e.return,c)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function iw(e,t,n){I=e,Bm(e)}function Bm(e,t,n){for(var r=(e.mode&1)!==0;I!==null;){var o=I,s=o.child;if(o.tag===22&&r){var i=o.memoizedState!==null||ti;if(!i){var a=o.alternate,c=a!==null&&a.memoizedState!==null||Le;a=ti;var u=Le;if(ti=i,(Le=c)&&!u)for(I=o;I!==null;)i=I,c=i.child,i.tag===22&&i.memoizedState!==null?Pf(o):c!==null?(c.return=i,I=c):Pf(o);for(;s!==null;)I=s,Bm(s),s=s.sibling;I=o,ti=a,Le=u}jf(e)}else o.subtreeFlags&8772&&s!==null?(s.return=o,I=s):jf(e)}}function jf(e){for(;I!==null;){var t=I;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Le||wa(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Le)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:wt(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&df(t,s,r);break;case 3:var i=t.updateQueue;if(i!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}df(t,i,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var f=u.memoizedState;if(f!==null){var p=f.dehydrated;p!==null&&ls(p)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(P(163))}Le||t.flags&512&&kc(t)}catch(d){ge(t,t.return,d)}}if(t===e){I=null;break}if(n=t.sibling,n!==null){n.return=t.return,I=n;break}I=t.return}}function Tf(e){for(;I!==null;){var t=I;if(t===e){I=null;break}var n=t.sibling;if(n!==null){n.return=t.return,I=n;break}I=t.return}}function Pf(e){for(;I!==null;){var t=I;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{wa(4,t)}catch(c){ge(t,n,c)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(c){ge(t,o,c)}}var s=t.return;try{kc(t)}catch(c){ge(t,s,c)}break;case 5:var i=t.return;try{kc(t)}catch(c){ge(t,i,c)}}}catch(c){ge(t,t.return,c)}if(t===e){I=null;break}var a=t.sibling;if(a!==null){a.return=t.return,I=a;break}I=t.return}}var aw=Math.ceil,Zi=on.ReactCurrentDispatcher,Lu=on.ReactCurrentOwner,ht=on.ReactCurrentBatchConfig,X=0,ke=null,xe=null,Te=0,et=0,$r=Kn(0),be=0,xs=null,ur=0,Sa=0,zu=0,Jo=null,Ke=null,Fu=0,go=1/0,Qt=null,Ji=!1,jc=null,Dn=null,ni=!1,jn=null,ea=0,es=0,Tc=null,ki=-1,Ci=0;function Be(){return X&6?ve():ki!==-1?ki:ki=ve()}function In(e){return e.mode&1?X&2&&Te!==0?Te&-Te:Wx.transition!==null?(Ci===0&&(Ci=Nh()),Ci):(e=ee,e!==0||(e=window.event,e=e===void 0?16:Oh(e.type)),e):1}function Nt(e,t,n,r){if(50<es)throw es=0,Tc=null,Error(P(185));Ps(e,n,r),(!(X&2)||e!==ke)&&(e===ke&&(!(X&2)&&(Sa|=n),be===4&&wn(e,Te)),Xe(e,r),n===1&&X===0&&!(t.mode&1)&&(go=ve()+500,va&&Qn()))}function Xe(e,t){var n=e.callbackNode;Wy(e,t);var r=Li(e,e===ke?Te:0);if(r===0)n!==null&&Ld(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Ld(n),t===1)e.tag===0?Vx(Rf.bind(null,e)):Zh(Rf.bind(null,e)),Fx(function(){!(X&6)&&Qn()}),n=null;else{switch(jh(r)){case 1:n=du;break;case 4:n=kh;break;case 16:n=Mi;break;case 536870912:n=Ch;break;default:n=Mi}n=Xm(n,Vm.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Vm(e,t){if(ki=-1,Ci=0,X&6)throw Error(P(327));var n=e.callbackNode;if(Qr()&&e.callbackNode!==n)return null;var r=Li(e,e===ke?Te:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=ta(e,r);else{t=r;var o=X;X|=2;var s=Hm();(ke!==e||Te!==t)&&(Qt=null,go=ve()+500,sr(e,t));do try{uw();break}catch(a){Wm(e,a)}while(!0);ku(),Zi.current=s,X=o,xe!==null?t=0:(ke=null,Te=0,t=be)}if(t!==0){if(t===2&&(o=tc(e),o!==0&&(r=o,t=Pc(e,o))),t===1)throw n=xs,sr(e,0),wn(e,r),Xe(e,ve()),n;if(t===6)wn(e,r);else{if(o=e.current.alternate,!(r&30)&&!lw(o)&&(t=ta(e,r),t===2&&(s=tc(e),s!==0&&(r=s,t=Pc(e,s))),t===1))throw n=xs,sr(e,0),wn(e,r),Xe(e,ve()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(P(345));case 2:qn(e,Ke,Qt);break;case 3:if(wn(e,r),(r&130023424)===r&&(t=Fu+500-ve(),10<t)){if(Li(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){Be(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=cc(qn.bind(null,e,Ke,Qt),t);break}qn(e,Ke,Qt);break;case 4:if(wn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var i=31-Ct(r);s=1<<i,i=t[i],i>o&&(o=i),r&=~s}if(r=o,r=ve()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*aw(r/1960))-r,10<r){e.timeoutHandle=cc(qn.bind(null,e,Ke,Qt),r);break}qn(e,Ke,Qt);break;case 5:qn(e,Ke,Qt);break;default:throw Error(P(329))}}}return Xe(e,ve()),e.callbackNode===n?Vm.bind(null,e):null}function Pc(e,t){var n=Jo;return e.current.memoizedState.isDehydrated&&(sr(e,t).flags|=256),e=ta(e,t),e!==2&&(t=Ke,Ke=n,t!==null&&Rc(t)),e}function Rc(e){Ke===null?Ke=e:Ke.push.apply(Ke,e)}function lw(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],s=o.getSnapshot;o=o.value;try{if(!jt(s(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function wn(e,t){for(t&=~zu,t&=~Sa,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ct(t),r=1<<n;e[n]=-1,t&=~r}}function Rf(e){if(X&6)throw Error(P(327));Qr();var t=Li(e,0);if(!(t&1))return Xe(e,ve()),null;var n=ta(e,t);if(e.tag!==0&&n===2){var r=tc(e);r!==0&&(t=r,n=Pc(e,r))}if(n===1)throw n=xs,sr(e,0),wn(e,t),Xe(e,ve()),n;if(n===6)throw Error(P(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,qn(e,Ke,Qt),Xe(e,ve()),null}function $u(e,t){var n=X;X|=1;try{return e(t)}finally{X=n,X===0&&(go=ve()+500,va&&Qn())}}function dr(e){jn!==null&&jn.tag===0&&!(X&6)&&Qr();var t=X;X|=1;var n=ht.transition,r=ee;try{if(ht.transition=null,ee=1,e)return e()}finally{ee=r,ht.transition=n,X=t,!(X&6)&&Qn()}}function Uu(){et=$r.current,le($r)}function sr(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,zx(n)),xe!==null)for(n=xe.return;n!==null;){var r=n;switch(Su(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Bi();break;case 3:ho(),le(Ge),le(ze),Ru();break;case 5:Pu(r);break;case 4:ho();break;case 13:le(fe);break;case 19:le(fe);break;case 10:Cu(r.type._context);break;case 22:case 23:Uu()}n=n.return}if(ke=e,xe=e=Mn(e.current,null),Te=et=t,be=0,xs=null,zu=Sa=ur=0,Ke=Jo=null,Jn!==null){for(t=0;t<Jn.length;t++)if(n=Jn[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,s=n.pending;if(s!==null){var i=s.next;s.next=o,r.next=i}n.pending=r}Jn=null}return e}function Wm(e,t){do{var n=xe;try{if(ku(),Si.current=qi,Xi){for(var r=pe.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Xi=!1}if(cr=0,Ee=Se=pe=null,qo=!1,gs=0,Lu.current=null,n===null||n.return===null){be=1,xs=t,xe=null;break}e:{var s=e,i=n.return,a=n,c=t;if(t=Te,a.flags|=32768,c!==null&&typeof c=="object"&&typeof c.then=="function"){var u=c,f=a,p=f.tag;if(!(f.mode&1)&&(p===0||p===11||p===15)){var d=f.alternate;d?(f.updateQueue=d.updateQueue,f.memoizedState=d.memoizedState,f.lanes=d.lanes):(f.updateQueue=null,f.memoizedState=null)}var x=vf(i);if(x!==null){x.flags&=-257,yf(x,i,a,s,t),x.mode&1&&gf(s,u,t),t=x,c=u;var b=t.updateQueue;if(b===null){var g=new Set;g.add(c),t.updateQueue=g}else b.add(c);break e}else{if(!(t&1)){gf(s,u,t),Bu();break e}c=Error(P(426))}}else if(ue&&a.mode&1){var S=vf(i);if(S!==null){!(S.flags&65536)&&(S.flags|=256),yf(S,i,a,s,t),bu(mo(c,a));break e}}s=c=mo(c,a),be!==4&&(be=2),Jo===null?Jo=[s]:Jo.push(s),s=i;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var v=jm(s,c,t);uf(s,v);break e;case 1:a=c;var m=s.type,y=s.stateNode;if(!(s.flags&128)&&(typeof m.getDerivedStateFromError=="function"||y!==null&&typeof y.componentDidCatch=="function"&&(Dn===null||!Dn.has(y)))){s.flags|=65536,t&=-t,s.lanes|=t;var w=Tm(s,a,t);uf(s,w);break e}}s=s.return}while(s!==null)}Qm(n)}catch(E){t=E,xe===n&&n!==null&&(xe=n=n.return);continue}break}while(!0)}function Hm(){var e=Zi.current;return Zi.current=qi,e===null?qi:e}function Bu(){(be===0||be===3||be===2)&&(be=4),ke===null||!(ur&268435455)&&!(Sa&268435455)||wn(ke,Te)}function ta(e,t){var n=X;X|=2;var r=Hm();(ke!==e||Te!==t)&&(Qt=null,sr(e,t));do try{cw();break}catch(o){Wm(e,o)}while(!0);if(ku(),X=n,Zi.current=r,xe!==null)throw Error(P(261));return ke=null,Te=0,be}function cw(){for(;xe!==null;)Km(xe)}function uw(){for(;xe!==null&&!Iy();)Km(xe)}function Km(e){var t=Ym(e.alternate,e,et);e.memoizedProps=e.pendingProps,t===null?Qm(e):xe=t,Lu.current=null}function Qm(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=rw(n,t),n!==null){n.flags&=32767,xe=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{be=6,xe=null;return}}else if(n=nw(n,t,et),n!==null){xe=n;return}if(t=t.sibling,t!==null){xe=t;return}xe=t=e}while(t!==null);be===0&&(be=5)}function qn(e,t,n){var r=ee,o=ht.transition;try{ht.transition=null,ee=1,dw(e,t,n,r)}finally{ht.transition=o,ee=r}return null}function dw(e,t,n,r){do Qr();while(jn!==null);if(X&6)throw Error(P(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(P(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(Hy(e,s),e===ke&&(xe=ke=null,Te=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||ni||(ni=!0,Xm(Mi,function(){return Qr(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=ht.transition,ht.transition=null;var i=ee;ee=1;var a=X;X|=4,Lu.current=null,sw(e,n),Um(n,e),Ax(ac),zi=!!ic,ac=ic=null,e.current=n,iw(n),My(),X=a,ee=i,ht.transition=s}else e.current=n;if(ni&&(ni=!1,jn=e,ea=o),s=e.pendingLanes,s===0&&(Dn=null),Fy(n.stateNode),Xe(e,ve()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Ji)throw Ji=!1,e=jc,jc=null,e;return ea&1&&e.tag!==0&&Qr(),s=e.pendingLanes,s&1?e===Tc?es++:(es=0,Tc=e):es=0,Qn(),null}function Qr(){if(jn!==null){var e=jh(ea),t=ht.transition,n=ee;try{if(ht.transition=null,ee=16>e?16:e,jn===null)var r=!1;else{if(e=jn,jn=null,ea=0,X&6)throw Error(P(331));var o=X;for(X|=4,I=e.current;I!==null;){var s=I,i=s.child;if(I.flags&16){var a=s.deletions;if(a!==null){for(var c=0;c<a.length;c++){var u=a[c];for(I=u;I!==null;){var f=I;switch(f.tag){case 0:case 11:case 15:Zo(8,f,s)}var p=f.child;if(p!==null)p.return=f,I=p;else for(;I!==null;){f=I;var d=f.sibling,x=f.return;if(zm(f),f===u){I=null;break}if(d!==null){d.return=x,I=d;break}I=x}}}var b=s.alternate;if(b!==null){var g=b.child;if(g!==null){b.child=null;do{var S=g.sibling;g.sibling=null,g=S}while(g!==null)}}I=s}}if(s.subtreeFlags&2064&&i!==null)i.return=s,I=i;else e:for(;I!==null;){if(s=I,s.flags&2048)switch(s.tag){case 0:case 11:case 15:Zo(9,s,s.return)}var v=s.sibling;if(v!==null){v.return=s.return,I=v;break e}I=s.return}}var m=e.current;for(I=m;I!==null;){i=I;var y=i.child;if(i.subtreeFlags&2064&&y!==null)y.return=i,I=y;else e:for(i=m;I!==null;){if(a=I,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:wa(9,a)}}catch(E){ge(a,a.return,E)}if(a===i){I=null;break e}var w=a.sibling;if(w!==null){w.return=a.return,I=w;break e}I=a.return}}if(X=o,Qn(),Ut&&typeof Ut.onPostCommitFiberRoot=="function")try{Ut.onPostCommitFiberRoot(fa,e)}catch{}r=!0}return r}finally{ee=n,ht.transition=t}}return!1}function Af(e,t,n){t=mo(n,t),t=jm(e,t,1),e=On(e,t,1),t=Be(),e!==null&&(Ps(e,1,t),Xe(e,t))}function ge(e,t,n){if(e.tag===3)Af(e,e,n);else for(;t!==null;){if(t.tag===3){Af(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Dn===null||!Dn.has(r))){e=mo(n,e),e=Tm(t,e,1),t=On(t,e,1),e=Be(),t!==null&&(Ps(t,1,e),Xe(t,e));break}}t=t.return}}function fw(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Be(),e.pingedLanes|=e.suspendedLanes&n,ke===e&&(Te&n)===n&&(be===4||be===3&&(Te&130023424)===Te&&500>ve()-Fu?sr(e,0):zu|=n),Xe(e,t)}function Gm(e,t){t===0&&(e.mode&1?(t=Ks,Ks<<=1,!(Ks&130023424)&&(Ks=4194304)):t=1);var n=Be();e=en(e,t),e!==null&&(Ps(e,t,n),Xe(e,n))}function pw(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Gm(e,n)}function hw(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(P(314))}r!==null&&r.delete(t),Gm(e,n)}var Ym;Ym=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ge.current)Qe=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Qe=!1,tw(e,t,n);Qe=!!(e.flags&131072)}else Qe=!1,ue&&t.flags&1048576&&Jh(t,Hi,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Ei(e,t),e=t.pendingProps;var o=uo(t,ze.current);Kr(t,n),o=_u(null,t,r,e,o,n);var s=Ou();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ye(r)?(s=!0,Vi(t)):s=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,ju(t),o.updater=xa,t.stateNode=o,o._reactInternals=t,gc(t,r,e,n),t=xc(null,t,r,!0,s,n)):(t.tag=0,ue&&s&&wu(t),$e(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Ei(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=gw(r),e=wt(r,e),o){case 0:t=yc(null,t,r,e,n);break e;case 1:t=Sf(null,t,r,e,n);break e;case 11:t=xf(null,t,r,e,n);break e;case 14:t=wf(null,t,r,wt(r.type,e),n);break e}throw Error(P(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:wt(r,o),yc(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:wt(r,o),Sf(e,t,r,o,n);case 3:e:{if(_m(t),e===null)throw Error(P(387));r=t.pendingProps,s=t.memoizedState,o=s.element,sm(e,t),Gi(t,r,null,n);var i=t.memoizedState;if(r=i.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){o=mo(Error(P(423)),t),t=bf(e,t,r,n,o);break e}else if(r!==o){o=mo(Error(P(424)),t),t=bf(e,t,r,n,o);break e}else for(rt=_n(t.stateNode.containerInfo.firstChild),ot=t,ue=!0,kt=null,n=rm(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(fo(),r===o){t=tn(e,t,n);break e}$e(e,t,r,n)}t=t.child}return t;case 5:return im(t),e===null&&pc(t),r=t.type,o=t.pendingProps,s=e!==null?e.memoizedProps:null,i=o.children,lc(r,o)?i=null:s!==null&&lc(r,s)&&(t.flags|=32),Am(e,t),$e(e,t,i,n),t.child;case 6:return e===null&&pc(t),null;case 13:return Om(e,t,n);case 4:return Tu(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=po(t,null,r,n):$e(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:wt(r,o),xf(e,t,r,o,n);case 7:return $e(e,t,t.pendingProps,n),t.child;case 8:return $e(e,t,t.pendingProps.children,n),t.child;case 12:return $e(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,s=t.memoizedProps,i=o.value,re(Ki,r._currentValue),r._currentValue=i,s!==null)if(jt(s.value,i)){if(s.children===o.children&&!Ge.current){t=tn(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var a=s.dependencies;if(a!==null){i=s.child;for(var c=a.firstContext;c!==null;){if(c.context===r){if(s.tag===1){c=qt(-1,n&-n),c.tag=2;var u=s.updateQueue;if(u!==null){u=u.shared;var f=u.pending;f===null?c.next=c:(c.next=f.next,f.next=c),u.pending=c}}s.lanes|=n,c=s.alternate,c!==null&&(c.lanes|=n),hc(s.return,n,t),a.lanes|=n;break}c=c.next}}else if(s.tag===10)i=s.type===t.type?null:s.child;else if(s.tag===18){if(i=s.return,i===null)throw Error(P(341));i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),hc(i,n,t),i=s.sibling}else i=s.child;if(i!==null)i.return=s;else for(i=s;i!==null;){if(i===t){i=null;break}if(s=i.sibling,s!==null){s.return=i.return,i=s;break}i=i.return}s=i}$e(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Kr(t,n),o=mt(o),r=r(o),t.flags|=1,$e(e,t,r,n),t.child;case 14:return r=t.type,o=wt(r,t.pendingProps),o=wt(r.type,o),wf(e,t,r,o,n);case 15:return Pm(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:wt(r,o),Ei(e,t),t.tag=1,Ye(r)?(e=!0,Vi(t)):e=!1,Kr(t,n),Nm(t,r,o),gc(t,r,o,n),xc(null,t,r,!0,e,n);case 19:return Dm(e,t,n);case 22:return Rm(e,t,n)}throw Error(P(156,t.tag))};function Xm(e,t){return Eh(e,t)}function mw(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function pt(e,t,n,r){return new mw(e,t,n,r)}function Vu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function gw(e){if(typeof e=="function")return Vu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===lu)return 11;if(e===cu)return 14}return 2}function Mn(e,t){var n=e.alternate;return n===null?(n=pt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Ni(e,t,n,r,o,s){var i=2;if(r=e,typeof e=="function")Vu(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case Rr:return ir(n.children,o,s,t);case au:i=8,o|=8;break;case $l:return e=pt(12,n,t,o|2),e.elementType=$l,e.lanes=s,e;case Ul:return e=pt(13,n,t,o),e.elementType=Ul,e.lanes=s,e;case Bl:return e=pt(19,n,t,o),e.elementType=Bl,e.lanes=s,e;case ih:return ba(n,o,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case oh:i=10;break e;case sh:i=9;break e;case lu:i=11;break e;case cu:i=14;break e;case vn:i=16,r=null;break e}throw Error(P(130,e==null?e:typeof e,""))}return t=pt(i,n,t,o),t.elementType=e,t.type=r,t.lanes=s,t}function ir(e,t,n,r){return e=pt(7,e,r,t),e.lanes=n,e}function ba(e,t,n,r){return e=pt(22,e,r,t),e.elementType=ih,e.lanes=n,e.stateNode={isHidden:!1},e}function Sl(e,t,n){return e=pt(6,e,null,t),e.lanes=n,e}function bl(e,t,n){return t=pt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function vw(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=nl(0),this.expirationTimes=nl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=nl(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Wu(e,t,n,r,o,s,i,a,c){return e=new vw(e,t,n,a,c),t===1?(t=1,s===!0&&(t|=8)):t=0,s=pt(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},ju(s),e}function yw(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Pr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function qm(e){if(!e)return Fn;e=e._reactInternals;e:{if(yr(e)!==e||e.tag!==1)throw Error(P(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ye(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(P(171))}if(e.tag===1){var n=e.type;if(Ye(n))return qh(e,n,t)}return t}function Zm(e,t,n,r,o,s,i,a,c){return e=Wu(n,r,!0,e,o,s,i,a,c),e.context=qm(null),n=e.current,r=Be(),o=In(n),s=qt(r,o),s.callback=t??null,On(n,s,o),e.current.lanes=o,Ps(e,o,r),Xe(e,r),e}function Ea(e,t,n,r){var o=t.current,s=Be(),i=In(o);return n=qm(n),t.context===null?t.context=n:t.pendingContext=n,t=qt(s,i),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=On(o,t,i),e!==null&&(Nt(e,o,i,s),wi(e,o,i)),i}function na(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function _f(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Hu(e,t){_f(e,t),(e=e.alternate)&&_f(e,t)}function xw(){return null}var Jm=typeof reportError=="function"?reportError:function(e){console.error(e)};function Ku(e){this._internalRoot=e}ka.prototype.render=Ku.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(P(409));Ea(e,t,null,null)};ka.prototype.unmount=Ku.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;dr(function(){Ea(null,e,null,null)}),t[Jt]=null}};function ka(e){this._internalRoot=e}ka.prototype.unstable_scheduleHydration=function(e){if(e){var t=Rh();e={blockedOn:null,target:e,priority:t};for(var n=0;n<xn.length&&t!==0&&t<xn[n].priority;n++);xn.splice(n,0,e),n===0&&_h(e)}};function Qu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ca(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Of(){}function ww(e,t,n,r,o){if(o){if(typeof r=="function"){var s=r;r=function(){var u=na(i);s.call(u)}}var i=Zm(t,r,e,0,null,!1,!1,"",Of);return e._reactRootContainer=i,e[Jt]=i.current,ds(e.nodeType===8?e.parentNode:e),dr(),i}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var a=r;r=function(){var u=na(c);a.call(u)}}var c=Wu(e,0,!1,null,null,!1,!1,"",Of);return e._reactRootContainer=c,e[Jt]=c.current,ds(e.nodeType===8?e.parentNode:e),dr(function(){Ea(t,c,n,r)}),c}function Na(e,t,n,r,o){var s=n._reactRootContainer;if(s){var i=s;if(typeof o=="function"){var a=o;o=function(){var c=na(i);a.call(c)}}Ea(t,i,e,o)}else i=ww(n,t,e,o,r);return na(i)}Th=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Vo(t.pendingLanes);n!==0&&(fu(t,n|1),Xe(t,ve()),!(X&6)&&(go=ve()+500,Qn()))}break;case 13:dr(function(){var r=en(e,1);if(r!==null){var o=Be();Nt(r,e,1,o)}}),Hu(e,1)}};pu=function(e){if(e.tag===13){var t=en(e,134217728);if(t!==null){var n=Be();Nt(t,e,134217728,n)}Hu(e,134217728)}};Ph=function(e){if(e.tag===13){var t=In(e),n=en(e,t);if(n!==null){var r=Be();Nt(n,e,t,r)}Hu(e,t)}};Rh=function(){return ee};Ah=function(e,t){var n=ee;try{return ee=e,t()}finally{ee=n}};Zl=function(e,t,n){switch(t){case"input":if(Hl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=ga(r);if(!o)throw Error(P(90));lh(r),Hl(r,o)}}}break;case"textarea":uh(e,n);break;case"select":t=n.value,t!=null&&Br(e,!!n.multiple,t,!1)}};vh=$u;yh=dr;var Sw={usingClientEntryPoint:!1,Events:[As,Dr,ga,mh,gh,$u]},zo={findFiberByHostInstance:Zn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},bw={bundleType:zo.bundleType,version:zo.version,rendererPackageName:zo.rendererPackageName,rendererConfig:zo.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:on.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Sh(e),e===null?null:e.stateNode},findFiberByHostInstance:zo.findFiberByHostInstance||xw,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ri=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ri.isDisabled&&ri.supportsFiber)try{fa=ri.inject(bw),Ut=ri}catch{}}at.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Sw;at.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Qu(t))throw Error(P(200));return yw(e,t,null,n)};at.createRoot=function(e,t){if(!Qu(e))throw Error(P(299));var n=!1,r="",o=Jm;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=Wu(e,1,!1,null,null,n,!1,r,o),e[Jt]=t.current,ds(e.nodeType===8?e.parentNode:e),new Ku(t)};at.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(P(188)):(e=Object.keys(e).join(","),Error(P(268,e)));return e=Sh(t),e=e===null?null:e.stateNode,e};at.flushSync=function(e){return dr(e)};at.hydrate=function(e,t,n){if(!Ca(t))throw Error(P(200));return Na(null,e,t,!0,n)};at.hydrateRoot=function(e,t,n){if(!Qu(e))throw Error(P(405));var r=n!=null&&n.hydratedSources||null,o=!1,s="",i=Jm;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(i=n.onRecoverableError)),t=Zm(t,null,e,1,n??null,o,!1,s,i),e[Jt]=t.current,ds(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new ka(t)};at.render=function(e,t,n){if(!Ca(t))throw Error(P(200));return Na(null,e,t,!1,n)};at.unmountComponentAtNode=function(e){if(!Ca(e))throw Error(P(40));return e._reactRootContainer?(dr(function(){Na(null,null,e,!1,function(){e._reactRootContainer=null,e[Jt]=null})}),!0):!1};at.unstable_batchedUpdates=$u;at.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ca(n))throw Error(P(200));if(e==null||e._reactInternals===void 0)throw Error(P(38));return Na(e,t,n,!1,r)};at.version="18.3.1-next-f1338f8080-20240426";function eg(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(eg)}catch(e){console.error(e)}}eg(),eh.exports=at;var Os=eh.exports;const tg=Up(Os);var ng,Df=Os;ng=Df.createRoot,Df.hydrateRoot;const Ew=1,kw=1e6;let El=0;function Cw(){return El=(El+1)%Number.MAX_SAFE_INTEGER,El.toString()}const kl=new Map,If=e=>{if(kl.has(e))return;const t=setTimeout(()=>{kl.delete(e),ts({type:"REMOVE_TOAST",toastId:e})},kw);kl.set(e,t)},Nw=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,Ew)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(n=>n.id===t.toast.id?{...n,...t.toast}:n)};case"DISMISS_TOAST":{const{toastId:n}=t;return n?If(n):e.toasts.forEach(r=>{If(r.id)}),{...e,toasts:e.toasts.map(r=>r.id===n||n===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(n=>n.id!==t.toastId)}}},ji=[];let Ti={toasts:[]};function ts(e){Ti=Nw(Ti,e),ji.forEach(t=>{t(Ti)})}function jw({...e}){const t=Cw(),n=o=>ts({type:"UPDATE_TOAST",toast:{...o,id:t}}),r=()=>ts({type:"DISMISS_TOAST",toastId:t});return ts({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:o=>{o||r()}}}),{id:t,dismiss:r,update:n}}function Tw(){const[e,t]=h.useState(Ti);return h.useEffect(()=>(ji.push(t),()=>{const n=ji.indexOf(t);n>-1&&ji.splice(n,1)}),[e]),{...e,toast:jw,dismiss:n=>ts({type:"DISMISS_TOAST",toastId:n})}}function ae(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Pw(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function rg(...e){return t=>e.forEach(n=>Pw(n,t))}function Re(...e){return h.useCallback(rg(...e),e)}function Rw(e,t=[]){let n=[];function r(s,i){const a=h.createContext(i),c=n.length;n=[...n,i];function u(p){const{scope:d,children:x,...b}=p,g=(d==null?void 0:d[e][c])||a,S=h.useMemo(()=>b,Object.values(b));return l.jsx(g.Provider,{value:S,children:x})}function f(p,d){const x=(d==null?void 0:d[e][c])||a,b=h.useContext(x);if(b)return b;if(i!==void 0)return i;throw new Error(`\`${p}\` must be used within \`${s}\``)}return u.displayName=s+"Provider",[u,f]}const o=()=>{const s=n.map(i=>h.createContext(i));return function(a){const c=(a==null?void 0:a[e])||s;return h.useMemo(()=>({[`__scope${e}`]:{...a,[e]:c}}),[a,c])}};return o.scopeName=e,[r,Aw(o,...t)]}function Aw(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((a,{useScope:c,scopeName:u})=>{const p=c(s)[`__scope${u}`];return{...a,...p}},{});return h.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}var vo=h.forwardRef((e,t)=>{const{children:n,...r}=e,o=h.Children.toArray(n),s=o.find(_w);if(s){const i=s.props.children,a=o.map(c=>c===s?h.Children.count(i)>1?h.Children.only(null):h.isValidElement(i)?i.props.children:null:c);return l.jsx(Ac,{...r,ref:t,children:h.isValidElement(i)?h.cloneElement(i,void 0,a):null})}return l.jsx(Ac,{...r,ref:t,children:n})});vo.displayName="Slot";var Ac=h.forwardRef((e,t)=>{const{children:n,...r}=e;if(h.isValidElement(n)){const o=Dw(n);return h.cloneElement(n,{...Ow(r,n.props),ref:t?rg(t,o):o})}return h.Children.count(n)>1?h.Children.only(null):null});Ac.displayName="SlotClone";var Gu=({children:e})=>l.jsx(l.Fragment,{children:e});function _w(e){return h.isValidElement(e)&&e.type===Gu}function Ow(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...a)=>{s(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function Dw(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Iw(e){const t=e+"CollectionProvider",[n,r]=Rw(t),[o,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=x=>{const{scope:b,children:g}=x,S=A.useRef(null),v=A.useRef(new Map).current;return l.jsx(o,{scope:b,itemMap:v,collectionRef:S,children:g})};i.displayName=t;const a=e+"CollectionSlot",c=A.forwardRef((x,b)=>{const{scope:g,children:S}=x,v=s(a,g),m=Re(b,v.collectionRef);return l.jsx(vo,{ref:m,children:S})});c.displayName=a;const u=e+"CollectionItemSlot",f="data-radix-collection-item",p=A.forwardRef((x,b)=>{const{scope:g,children:S,...v}=x,m=A.useRef(null),y=Re(b,m),w=s(u,g);return A.useEffect(()=>(w.itemMap.set(m,{ref:m,...v}),()=>void w.itemMap.delete(m))),l.jsx(vo,{[f]:"",ref:y,children:S})});p.displayName=u;function d(x){const b=s(e+"CollectionConsumer",x);return A.useCallback(()=>{const S=b.collectionRef.current;if(!S)return[];const v=Array.from(S.querySelectorAll(`[${f}]`));return Array.from(b.itemMap.values()).sort((w,E)=>v.indexOf(w.ref.current)-v.indexOf(E.ref.current))},[b.collectionRef,b.itemMap])}return[{Provider:i,Slot:c,ItemSlot:p},d,r]}function Mw(e,t){const n=h.createContext(t),r=s=>{const{children:i,...a}=s,c=h.useMemo(()=>a,Object.values(a));return l.jsx(n.Provider,{value:c,children:i})};r.displayName=e+"Provider";function o(s){const i=h.useContext(n);if(i)return i;if(t!==void 0)return t;throw new Error(`\`${s}\` must be used within \`${e}\``)}return[r,o]}function ja(e,t=[]){let n=[];function r(s,i){const a=h.createContext(i),c=n.length;n=[...n,i];const u=p=>{var v;const{scope:d,children:x,...b}=p,g=((v=d==null?void 0:d[e])==null?void 0:v[c])||a,S=h.useMemo(()=>b,Object.values(b));return l.jsx(g.Provider,{value:S,children:x})};u.displayName=s+"Provider";function f(p,d){var g;const x=((g=d==null?void 0:d[e])==null?void 0:g[c])||a,b=h.useContext(x);if(b)return b;if(i!==void 0)return i;throw new Error(`\`${p}\` must be used within \`${s}\``)}return[u,f]}const o=()=>{const s=n.map(i=>h.createContext(i));return function(a){const c=(a==null?void 0:a[e])||s;return h.useMemo(()=>({[`__scope${e}`]:{...a,[e]:c}}),[a,c])}};return o.scopeName=e,[r,Lw(o,...t)]}function Lw(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((a,{useScope:c,scopeName:u})=>{const p=c(s)[`__scope${u}`];return{...a,...p}},{});return h.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}var zw=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],ye=zw.reduce((e,t)=>{const n=h.forwardRef((r,o)=>{const{asChild:s,...i}=r,a=s?vo:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),l.jsx(a,{...i,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function og(e,t){e&&Os.flushSync(()=>e.dispatchEvent(t))}function vt(e){const t=h.useRef(e);return h.useEffect(()=>{t.current=e}),h.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function Fw(e,t=globalThis==null?void 0:globalThis.document){const n=vt(e);h.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var $w="DismissableLayer",_c="dismissableLayer.update",Uw="dismissableLayer.pointerDownOutside",Bw="dismissableLayer.focusOutside",Mf,sg=h.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Ta=h.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:s,onInteractOutside:i,onDismiss:a,...c}=e,u=h.useContext(sg),[f,p]=h.useState(null),d=(f==null?void 0:f.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,x]=h.useState({}),b=Re(t,k=>p(k)),g=Array.from(u.layers),[S]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),v=g.indexOf(S),m=f?g.indexOf(f):-1,y=u.layersWithOutsidePointerEventsDisabled.size>0,w=m>=v,E=Ww(k=>{const j=k.target,O=[...u.branches].some(_=>_.contains(j));!w||O||(o==null||o(k),i==null||i(k),k.defaultPrevented||a==null||a())},d),C=Hw(k=>{const j=k.target;[...u.branches].some(_=>_.contains(j))||(s==null||s(k),i==null||i(k),k.defaultPrevented||a==null||a())},d);return Fw(k=>{m===u.layers.size-1&&(r==null||r(k),!k.defaultPrevented&&a&&(k.preventDefault(),a()))},d),h.useEffect(()=>{if(f)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(Mf=d.body.style.pointerEvents,d.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(f)),u.layers.add(f),Lf(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(d.body.style.pointerEvents=Mf)}},[f,d,n,u]),h.useEffect(()=>()=>{f&&(u.layers.delete(f),u.layersWithOutsidePointerEventsDisabled.delete(f),Lf())},[f,u]),h.useEffect(()=>{const k=()=>x({});return document.addEventListener(_c,k),()=>document.removeEventListener(_c,k)},[]),l.jsx(ye.div,{...c,ref:b,style:{pointerEvents:y?w?"auto":"none":void 0,...e.style},onFocusCapture:ae(e.onFocusCapture,C.onFocusCapture),onBlurCapture:ae(e.onBlurCapture,C.onBlurCapture),onPointerDownCapture:ae(e.onPointerDownCapture,E.onPointerDownCapture)})});Ta.displayName=$w;var Vw="DismissableLayerBranch",ig=h.forwardRef((e,t)=>{const n=h.useContext(sg),r=h.useRef(null),o=Re(t,r);return h.useEffect(()=>{const s=r.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),l.jsx(ye.div,{...e,ref:o})});ig.displayName=Vw;function Ww(e,t=globalThis==null?void 0:globalThis.document){const n=vt(e),r=h.useRef(!1),o=h.useRef(()=>{});return h.useEffect(()=>{const s=a=>{if(a.target&&!r.current){let c=function(){ag(Uw,n,u,{discrete:!0})};const u={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=c,t.addEventListener("click",o.current,{once:!0})):c()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",s),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Hw(e,t=globalThis==null?void 0:globalThis.document){const n=vt(e),r=h.useRef(!1);return h.useEffect(()=>{const o=s=>{s.target&&!r.current&&ag(Bw,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Lf(){const e=new CustomEvent(_c);document.dispatchEvent(e)}function ag(e,t,n,{discrete:r}){const o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?og(o,s):o.dispatchEvent(s)}var Kw=Ta,Qw=ig,$n=globalThis!=null&&globalThis.document?h.useLayoutEffect:()=>{},Gw="Portal",Yu=h.forwardRef((e,t)=>{var a;const{container:n,...r}=e,[o,s]=h.useState(!1);$n(()=>s(!0),[]);const i=n||o&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return i?tg.createPortal(l.jsx(ye.div,{...r,ref:t}),i):null});Yu.displayName=Gw;function Yw(e,t){return h.useReducer((n,r)=>t[n][r]??n,e)}var ko=e=>{const{present:t,children:n}=e,r=Xw(t),o=typeof n=="function"?n({present:r.isPresent}):h.Children.only(n),s=Re(r.ref,qw(o));return typeof n=="function"||r.isPresent?h.cloneElement(o,{ref:s}):null};ko.displayName="Presence";function Xw(e){const[t,n]=h.useState(),r=h.useRef({}),o=h.useRef(e),s=h.useRef("none"),i=e?"mounted":"unmounted",[a,c]=Yw(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return h.useEffect(()=>{const u=oi(r.current);s.current=a==="mounted"?u:"none"},[a]),$n(()=>{const u=r.current,f=o.current;if(f!==e){const d=s.current,x=oi(u);e?c("MOUNT"):x==="none"||(u==null?void 0:u.display)==="none"?c("UNMOUNT"):c(f&&d!==x?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,c]),$n(()=>{if(t){let u;const f=t.ownerDocument.defaultView??window,p=x=>{const g=oi(r.current).includes(x.animationName);if(x.target===t&&g&&(c("ANIMATION_END"),!o.current)){const S=t.style.animationFillMode;t.style.animationFillMode="forwards",u=f.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=S)})}},d=x=>{x.target===t&&(s.current=oi(r.current))};return t.addEventListener("animationstart",d),t.addEventListener("animationcancel",p),t.addEventListener("animationend",p),()=>{f.clearTimeout(u),t.removeEventListener("animationstart",d),t.removeEventListener("animationcancel",p),t.removeEventListener("animationend",p)}}else c("ANIMATION_END")},[t,c]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:h.useCallback(u=>{u&&(r.current=getComputedStyle(u)),n(u)},[])}}function oi(e){return(e==null?void 0:e.animationName)||"none"}function qw(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Xu({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=Zw({defaultProp:t,onChange:n}),s=e!==void 0,i=s?e:r,a=vt(n),c=h.useCallback(u=>{if(s){const p=typeof u=="function"?u(e):u;p!==e&&a(p)}else o(u)},[s,e,o,a]);return[i,c]}function Zw({defaultProp:e,onChange:t}){const n=h.useState(e),[r]=n,o=h.useRef(r),s=vt(t);return h.useEffect(()=>{o.current!==r&&(s(r),o.current=r)},[r,o,s]),n}var Jw="VisuallyHidden",Pa=h.forwardRef((e,t)=>l.jsx(ye.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));Pa.displayName=Jw;var e1=Pa,qu="ToastProvider",[Zu,t1,n1]=Iw("Toast"),[lg,K2]=ja("Toast",[n1]),[r1,Ra]=lg(qu),cg=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:o="right",swipeThreshold:s=50,children:i}=e,[a,c]=h.useState(null),[u,f]=h.useState(0),p=h.useRef(!1),d=h.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${qu}\`. Expected non-empty \`string\`.`),l.jsx(Zu.Provider,{scope:t,children:l.jsx(r1,{scope:t,label:n,duration:r,swipeDirection:o,swipeThreshold:s,toastCount:u,viewport:a,onViewportChange:c,onToastAdd:h.useCallback(()=>f(x=>x+1),[]),onToastRemove:h.useCallback(()=>f(x=>x-1),[]),isFocusedToastEscapeKeyDownRef:p,isClosePausedRef:d,children:i})})};cg.displayName=qu;var ug="ToastViewport",o1=["F8"],Oc="toast.viewportPause",Dc="toast.viewportResume",dg=h.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=o1,label:o="Notifications ({hotkey})",...s}=e,i=Ra(ug,n),a=t1(n),c=h.useRef(null),u=h.useRef(null),f=h.useRef(null),p=h.useRef(null),d=Re(t,p,i.onViewportChange),x=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),b=i.toastCount>0;h.useEffect(()=>{const S=v=>{var y;r.length!==0&&r.every(w=>v[w]||v.code===w)&&((y=p.current)==null||y.focus())};return document.addEventListener("keydown",S),()=>document.removeEventListener("keydown",S)},[r]),h.useEffect(()=>{const S=c.current,v=p.current;if(b&&S&&v){const m=()=>{if(!i.isClosePausedRef.current){const C=new CustomEvent(Oc);v.dispatchEvent(C),i.isClosePausedRef.current=!0}},y=()=>{if(i.isClosePausedRef.current){const C=new CustomEvent(Dc);v.dispatchEvent(C),i.isClosePausedRef.current=!1}},w=C=>{!S.contains(C.relatedTarget)&&y()},E=()=>{S.contains(document.activeElement)||y()};return S.addEventListener("focusin",m),S.addEventListener("focusout",w),S.addEventListener("pointermove",m),S.addEventListener("pointerleave",E),window.addEventListener("blur",m),window.addEventListener("focus",y),()=>{S.removeEventListener("focusin",m),S.removeEventListener("focusout",w),S.removeEventListener("pointermove",m),S.removeEventListener("pointerleave",E),window.removeEventListener("blur",m),window.removeEventListener("focus",y)}}},[b,i.isClosePausedRef]);const g=h.useCallback(({tabbingDirection:S})=>{const m=a().map(y=>{const w=y.ref.current,E=[w,...v1(w)];return S==="forwards"?E:E.reverse()});return(S==="forwards"?m.reverse():m).flat()},[a]);return h.useEffect(()=>{const S=p.current;if(S){const v=m=>{var E,C,k;const y=m.altKey||m.ctrlKey||m.metaKey;if(m.key==="Tab"&&!y){const j=document.activeElement,O=m.shiftKey;if(m.target===S&&O){(E=u.current)==null||E.focus();return}const M=g({tabbingDirection:O?"backwards":"forwards"}),H=M.findIndex(D=>D===j);Cl(M.slice(H+1))?m.preventDefault():O?(C=u.current)==null||C.focus():(k=f.current)==null||k.focus()}};return S.addEventListener("keydown",v),()=>S.removeEventListener("keydown",v)}},[a,g]),l.jsxs(Qw,{ref:c,role:"region","aria-label":o.replace("{hotkey}",x),tabIndex:-1,style:{pointerEvents:b?void 0:"none"},children:[b&&l.jsx(Ic,{ref:u,onFocusFromOutsideViewport:()=>{const S=g({tabbingDirection:"forwards"});Cl(S)}}),l.jsx(Zu.Slot,{scope:n,children:l.jsx(ye.ol,{tabIndex:-1,...s,ref:d})}),b&&l.jsx(Ic,{ref:f,onFocusFromOutsideViewport:()=>{const S=g({tabbingDirection:"backwards"});Cl(S)}})]})});dg.displayName=ug;var fg="ToastFocusProxy",Ic=h.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,s=Ra(fg,n);return l.jsx(Pa,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:i=>{var u;const a=i.relatedTarget;!((u=s.viewport)!=null&&u.contains(a))&&r()}})});Ic.displayName=fg;var Aa="Toast",s1="toast.swipeStart",i1="toast.swipeMove",a1="toast.swipeCancel",l1="toast.swipeEnd",pg=h.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:s,...i}=e,[a=!0,c]=Xu({prop:r,defaultProp:o,onChange:s});return l.jsx(ko,{present:n||a,children:l.jsx(d1,{open:a,...i,ref:t,onClose:()=>c(!1),onPause:vt(e.onPause),onResume:vt(e.onResume),onSwipeStart:ae(e.onSwipeStart,u=>{u.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:ae(e.onSwipeMove,u=>{const{x:f,y:p}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","move"),u.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${f}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${p}px`)}),onSwipeCancel:ae(e.onSwipeCancel,u=>{u.currentTarget.setAttribute("data-swipe","cancel"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:ae(e.onSwipeEnd,u=>{const{x:f,y:p}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","end"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${f}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${p}px`),c(!1)})})})});pg.displayName=Aa;var[c1,u1]=lg(Aa,{onClose(){}}),d1=h.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:o,open:s,onClose:i,onEscapeKeyDown:a,onPause:c,onResume:u,onSwipeStart:f,onSwipeMove:p,onSwipeCancel:d,onSwipeEnd:x,...b}=e,g=Ra(Aa,n),[S,v]=h.useState(null),m=Re(t,D=>v(D)),y=h.useRef(null),w=h.useRef(null),E=o||g.duration,C=h.useRef(0),k=h.useRef(E),j=h.useRef(0),{onToastAdd:O,onToastRemove:_}=g,F=vt(()=>{var K;(S==null?void 0:S.contains(document.activeElement))&&((K=g.viewport)==null||K.focus()),i()}),M=h.useCallback(D=>{!D||D===1/0||(window.clearTimeout(j.current),C.current=new Date().getTime(),j.current=window.setTimeout(F,D))},[F]);h.useEffect(()=>{const D=g.viewport;if(D){const K=()=>{M(k.current),u==null||u()},U=()=>{const Q=new Date().getTime()-C.current;k.current=k.current-Q,window.clearTimeout(j.current),c==null||c()};return D.addEventListener(Oc,U),D.addEventListener(Dc,K),()=>{D.removeEventListener(Oc,U),D.removeEventListener(Dc,K)}}},[g.viewport,E,c,u,M]),h.useEffect(()=>{s&&!g.isClosePausedRef.current&&M(E)},[s,E,g.isClosePausedRef,M]),h.useEffect(()=>(O(),()=>_()),[O,_]);const H=h.useMemo(()=>S?wg(S):null,[S]);return g.viewport?l.jsxs(l.Fragment,{children:[H&&l.jsx(f1,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:H}),l.jsx(c1,{scope:n,onClose:F,children:Os.createPortal(l.jsx(Zu.ItemSlot,{scope:n,children:l.jsx(Kw,{asChild:!0,onEscapeKeyDown:ae(a,()=>{g.isFocusedToastEscapeKeyDownRef.current||F(),g.isFocusedToastEscapeKeyDownRef.current=!1}),children:l.jsx(ye.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":s?"open":"closed","data-swipe-direction":g.swipeDirection,...b,ref:m,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:ae(e.onKeyDown,D=>{D.key==="Escape"&&(a==null||a(D.nativeEvent),D.nativeEvent.defaultPrevented||(g.isFocusedToastEscapeKeyDownRef.current=!0,F()))}),onPointerDown:ae(e.onPointerDown,D=>{D.button===0&&(y.current={x:D.clientX,y:D.clientY})}),onPointerMove:ae(e.onPointerMove,D=>{if(!y.current)return;const K=D.clientX-y.current.x,U=D.clientY-y.current.y,Q=!!w.current,N=["left","right"].includes(g.swipeDirection),R=["left","up"].includes(g.swipeDirection)?Math.min:Math.max,z=N?R(0,K):0,L=N?0:R(0,U),$=D.pointerType==="touch"?10:2,Y={x:z,y:L},de={originalEvent:D,delta:Y};Q?(w.current=Y,si(i1,p,de,{discrete:!1})):zf(Y,g.swipeDirection,$)?(w.current=Y,si(s1,f,de,{discrete:!1}),D.target.setPointerCapture(D.pointerId)):(Math.abs(K)>$||Math.abs(U)>$)&&(y.current=null)}),onPointerUp:ae(e.onPointerUp,D=>{const K=w.current,U=D.target;if(U.hasPointerCapture(D.pointerId)&&U.releasePointerCapture(D.pointerId),w.current=null,y.current=null,K){const Q=D.currentTarget,N={originalEvent:D,delta:K};zf(K,g.swipeDirection,g.swipeThreshold)?si(l1,x,N,{discrete:!0}):si(a1,d,N,{discrete:!0}),Q.addEventListener("click",R=>R.preventDefault(),{once:!0})}})})})}),g.viewport)})]}):null}),f1=e=>{const{__scopeToast:t,children:n,...r}=e,o=Ra(Aa,t),[s,i]=h.useState(!1),[a,c]=h.useState(!1);return m1(()=>i(!0)),h.useEffect(()=>{const u=window.setTimeout(()=>c(!0),1e3);return()=>window.clearTimeout(u)},[]),a?null:l.jsx(Yu,{asChild:!0,children:l.jsx(Pa,{...r,children:s&&l.jsxs(l.Fragment,{children:[o.label," ",n]})})})},p1="ToastTitle",hg=h.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return l.jsx(ye.div,{...r,ref:t})});hg.displayName=p1;var h1="ToastDescription",mg=h.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return l.jsx(ye.div,{...r,ref:t})});mg.displayName=h1;var gg="ToastAction",vg=h.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?l.jsx(xg,{altText:n,asChild:!0,children:l.jsx(Ju,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${gg}\`. Expected non-empty \`string\`.`),null)});vg.displayName=gg;var yg="ToastClose",Ju=h.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,o=u1(yg,n);return l.jsx(xg,{asChild:!0,children:l.jsx(ye.button,{type:"button",...r,ref:t,onClick:ae(e.onClick,o.onClose)})})});Ju.displayName=yg;var xg=h.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...o}=e;return l.jsx(ye.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function wg(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),g1(r)){const o=r.ariaHidden||r.hidden||r.style.display==="none",s=r.dataset.radixToastAnnounceExclude==="";if(!o)if(s){const i=r.dataset.radixToastAnnounceAlt;i&&t.push(i)}else t.push(...wg(r))}}),t}function si(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,s=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?og(o,s):o.dispatchEvent(s)}var zf=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),s=r>o;return t==="left"||t==="right"?s&&r>n:!s&&o>n};function m1(e=()=>{}){const t=vt(e);$n(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function g1(e){return e.nodeType===e.ELEMENT_NODE}function v1(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Cl(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var y1=cg,Sg=dg,bg=pg,Eg=hg,kg=mg,Cg=vg,Ng=Ju;function jg(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=jg(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function Tg(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=jg(e))&&(r&&(r+=" "),r+=t);return r}const Ff=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,$f=Tg,_a=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return $f(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:s}=t,i=Object.keys(o).map(u=>{const f=n==null?void 0:n[u],p=s==null?void 0:s[u];if(f===null)return null;const d=Ff(f)||Ff(p);return o[u][d]}),a=n&&Object.entries(n).reduce((u,f)=>{let[p,d]=f;return d===void 0||(u[p]=d),u},{}),c=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,f)=>{let{class:p,className:d,...x}=f;return Object.entries(x).every(b=>{let[g,S]=b;return Array.isArray(S)?S.includes({...s,...a}[g]):{...s,...a}[g]===S})?[...u,p,d]:u},[]);return $f(e,i,c,n==null?void 0:n.class,n==null?void 0:n.className)};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const x1=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Pg=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim();/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var w1={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S1=h.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:s,iconNode:i,...a},c)=>h.createElement("svg",{ref:c,...w1,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:Pg("lucide",o),...a},[...i.map(([u,f])=>h.createElement(u,f)),...Array.isArray(s)?s:[s]]));/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Z=(e,t)=>{const n=h.forwardRef(({className:r,...o},s)=>h.createElement(S1,{ref:s,iconNode:t,className:Pg(`lucide-${x1(e)}`,r),...o}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const b1=Z("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E1=Z("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const je=Z("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const k1=Z("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C1=Z("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N1=Z("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oa=Z("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const j1=Z("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nl=Z("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rg=Z("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ur=Z("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const T1=Z("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Da=Z("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ag=Z("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _g=Z("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const P1=Z("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const R1=Z("Pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uf=Z("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bf=Z("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const A1=Z("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Og=Z("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dg=Z("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vf=Z("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _1=Z("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wf=Z("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const O1=Z("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D1=Z("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const I1=Z("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const M1=Z("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const L1=Z("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),ed="-",z1=e=>{const t=$1(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:i=>{const a=i.split(ed);return a[0]===""&&a.length!==1&&a.shift(),Ig(a,t)||F1(i)},getConflictingClassGroupIds:(i,a)=>{const c=n[i]||[];return a&&r[i]?[...c,...r[i]]:c}}},Ig=(e,t)=>{var i;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?Ig(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const s=e.join(ed);return(i=t.validators.find(({validator:a})=>a(s)))==null?void 0:i.classGroupId},Hf=/^\[(.+)\]$/,F1=e=>{if(Hf.test(e)){const t=Hf.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},$1=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return B1(Object.entries(e.classGroups),n).forEach(([s,i])=>{Mc(i,r,s,t)}),r},Mc=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const s=o===""?t:Kf(t,o);s.classGroupId=n;return}if(typeof o=="function"){if(U1(o)){Mc(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([s,i])=>{Mc(i,Kf(t,s),n,r)})})},Kf=(e,t)=>{let n=e;return t.split(ed).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},U1=e=>e.isThemeGetter,B1=(e,t)=>t?e.map(([n,r])=>{const o=r.map(s=>typeof s=="string"?t+s:typeof s=="object"?Object.fromEntries(Object.entries(s).map(([i,a])=>[t+i,a])):s);return[n,o]}):e,V1=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(s,i)=>{n.set(s,i),t++,t>e&&(t=0,r=n,n=new Map)};return{get(s){let i=n.get(s);if(i!==void 0)return i;if((i=r.get(s))!==void 0)return o(s,i),i},set(s,i){n.has(s)?n.set(s,i):o(s,i)}}},Mg="!",W1=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],s=t.length,i=a=>{const c=[];let u=0,f=0,p;for(let S=0;S<a.length;S++){let v=a[S];if(u===0){if(v===o&&(r||a.slice(S,S+s)===t)){c.push(a.slice(f,S)),f=S+s;continue}if(v==="/"){p=S;continue}}v==="["?u++:v==="]"&&u--}const d=c.length===0?a:a.substring(f),x=d.startsWith(Mg),b=x?d.substring(1):d,g=p&&p>f?p-f:void 0;return{modifiers:c,hasImportantModifier:x,baseClassName:b,maybePostfixModifierPosition:g}};return n?a=>n({className:a,parseClassName:i}):i},H1=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},K1=e=>({cache:V1(e.cacheSize),parseClassName:W1(e),...z1(e)}),Q1=/\s+/,G1=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,s=[],i=e.trim().split(Q1);let a="";for(let c=i.length-1;c>=0;c-=1){const u=i[c],{modifiers:f,hasImportantModifier:p,baseClassName:d,maybePostfixModifierPosition:x}=n(u);let b=!!x,g=r(b?d.substring(0,x):d);if(!g){if(!b){a=u+(a.length>0?" "+a:a);continue}if(g=r(d),!g){a=u+(a.length>0?" "+a:a);continue}b=!1}const S=H1(f).join(":"),v=p?S+Mg:S,m=v+g;if(s.includes(m))continue;s.push(m);const y=o(g,b);for(let w=0;w<y.length;++w){const E=y[w];s.push(v+E)}a=u+(a.length>0?" "+a:a)}return a};function Y1(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=Lg(t))&&(r&&(r+=" "),r+=n);return r}const Lg=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Lg(e[r]))&&(n&&(n+=" "),n+=t);return n};function X1(e,...t){let n,r,o,s=i;function i(c){const u=t.reduce((f,p)=>p(f),e());return n=K1(u),r=n.cache.get,o=n.cache.set,s=a,a(c)}function a(c){const u=r(c);if(u)return u;const f=G1(c,n);return o(c,f),f}return function(){return s(Y1.apply(null,arguments))}}const se=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},zg=/^\[(?:([a-z-]+):)?(.+)\]$/i,q1=/^\d+\/\d+$/,Z1=new Set(["px","full","screen"]),J1=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,eS=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,tS=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,nS=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,rS=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ht=e=>Gr(e)||Z1.has(e)||q1.test(e),pn=e=>Co(e,"length",dS),Gr=e=>!!e&&!Number.isNaN(Number(e)),jl=e=>Co(e,"number",Gr),Fo=e=>!!e&&Number.isInteger(Number(e)),oS=e=>e.endsWith("%")&&Gr(e.slice(0,-1)),V=e=>zg.test(e),hn=e=>J1.test(e),sS=new Set(["length","size","percentage"]),iS=e=>Co(e,sS,Fg),aS=e=>Co(e,"position",Fg),lS=new Set(["image","url"]),cS=e=>Co(e,lS,pS),uS=e=>Co(e,"",fS),$o=()=>!0,Co=(e,t,n)=>{const r=zg.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},dS=e=>eS.test(e)&&!tS.test(e),Fg=()=>!1,fS=e=>nS.test(e),pS=e=>rS.test(e),hS=()=>{const e=se("colors"),t=se("spacing"),n=se("blur"),r=se("brightness"),o=se("borderColor"),s=se("borderRadius"),i=se("borderSpacing"),a=se("borderWidth"),c=se("contrast"),u=se("grayscale"),f=se("hueRotate"),p=se("invert"),d=se("gap"),x=se("gradientColorStops"),b=se("gradientColorStopPositions"),g=se("inset"),S=se("margin"),v=se("opacity"),m=se("padding"),y=se("saturate"),w=se("scale"),E=se("sepia"),C=se("skew"),k=se("space"),j=se("translate"),O=()=>["auto","contain","none"],_=()=>["auto","hidden","clip","visible","scroll"],F=()=>["auto",V,t],M=()=>[V,t],H=()=>["",Ht,pn],D=()=>["auto",Gr,V],K=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],U=()=>["solid","dashed","dotted","double","none"],Q=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],N=()=>["start","end","center","between","around","evenly","stretch"],R=()=>["","0",V],z=()=>["auto","avoid","all","avoid-page","page","left","right","column"],L=()=>[Gr,V];return{cacheSize:500,separator:":",theme:{colors:[$o],spacing:[Ht,pn],blur:["none","",hn,V],brightness:L(),borderColor:[e],borderRadius:["none","","full",hn,V],borderSpacing:M(),borderWidth:H(),contrast:L(),grayscale:R(),hueRotate:L(),invert:R(),gap:M(),gradientColorStops:[e],gradientColorStopPositions:[oS,pn],inset:F(),margin:F(),opacity:L(),padding:M(),saturate:L(),scale:L(),sepia:R(),skew:L(),space:M(),translate:M()},classGroups:{aspect:[{aspect:["auto","square","video",V]}],container:["container"],columns:[{columns:[hn]}],"break-after":[{"break-after":z()}],"break-before":[{"break-before":z()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...K(),V]}],overflow:[{overflow:_()}],"overflow-x":[{"overflow-x":_()}],"overflow-y":[{"overflow-y":_()}],overscroll:[{overscroll:O()}],"overscroll-x":[{"overscroll-x":O()}],"overscroll-y":[{"overscroll-y":O()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[g]}],"inset-x":[{"inset-x":[g]}],"inset-y":[{"inset-y":[g]}],start:[{start:[g]}],end:[{end:[g]}],top:[{top:[g]}],right:[{right:[g]}],bottom:[{bottom:[g]}],left:[{left:[g]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Fo,V]}],basis:[{basis:F()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",V]}],grow:[{grow:R()}],shrink:[{shrink:R()}],order:[{order:["first","last","none",Fo,V]}],"grid-cols":[{"grid-cols":[$o]}],"col-start-end":[{col:["auto",{span:["full",Fo,V]},V]}],"col-start":[{"col-start":D()}],"col-end":[{"col-end":D()}],"grid-rows":[{"grid-rows":[$o]}],"row-start-end":[{row:["auto",{span:[Fo,V]},V]}],"row-start":[{"row-start":D()}],"row-end":[{"row-end":D()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",V]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",V]}],gap:[{gap:[d]}],"gap-x":[{"gap-x":[d]}],"gap-y":[{"gap-y":[d]}],"justify-content":[{justify:["normal",...N()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...N(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...N(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[m]}],px:[{px:[m]}],py:[{py:[m]}],ps:[{ps:[m]}],pe:[{pe:[m]}],pt:[{pt:[m]}],pr:[{pr:[m]}],pb:[{pb:[m]}],pl:[{pl:[m]}],m:[{m:[S]}],mx:[{mx:[S]}],my:[{my:[S]}],ms:[{ms:[S]}],me:[{me:[S]}],mt:[{mt:[S]}],mr:[{mr:[S]}],mb:[{mb:[S]}],ml:[{ml:[S]}],"space-x":[{"space-x":[k]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[k]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",V,t]}],"min-w":[{"min-w":[V,t,"min","max","fit"]}],"max-w":[{"max-w":[V,t,"none","full","min","max","fit","prose",{screen:[hn]},hn]}],h:[{h:[V,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[V,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[V,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[V,t,"auto","min","max","fit"]}],"font-size":[{text:["base",hn,pn]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",jl]}],"font-family":[{font:[$o]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",V]}],"line-clamp":[{"line-clamp":["none",Gr,jl]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Ht,V]}],"list-image":[{"list-image":["none",V]}],"list-style-type":[{list:["none","disc","decimal",V]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[v]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[v]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...U(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Ht,pn]}],"underline-offset":[{"underline-offset":["auto",Ht,V]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:M()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",V]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",V]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[v]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...K(),aS]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",iS]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},cS]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[b]}],"gradient-via-pos":[{via:[b]}],"gradient-to-pos":[{to:[b]}],"gradient-from":[{from:[x]}],"gradient-via":[{via:[x]}],"gradient-to":[{to:[x]}],rounded:[{rounded:[s]}],"rounded-s":[{"rounded-s":[s]}],"rounded-e":[{"rounded-e":[s]}],"rounded-t":[{"rounded-t":[s]}],"rounded-r":[{"rounded-r":[s]}],"rounded-b":[{"rounded-b":[s]}],"rounded-l":[{"rounded-l":[s]}],"rounded-ss":[{"rounded-ss":[s]}],"rounded-se":[{"rounded-se":[s]}],"rounded-ee":[{"rounded-ee":[s]}],"rounded-es":[{"rounded-es":[s]}],"rounded-tl":[{"rounded-tl":[s]}],"rounded-tr":[{"rounded-tr":[s]}],"rounded-br":[{"rounded-br":[s]}],"rounded-bl":[{"rounded-bl":[s]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[v]}],"border-style":[{border:[...U(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[v]}],"divide-style":[{divide:U()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...U()]}],"outline-offset":[{"outline-offset":[Ht,V]}],"outline-w":[{outline:[Ht,pn]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:H()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[v]}],"ring-offset-w":[{"ring-offset":[Ht,pn]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",hn,uS]}],"shadow-color":[{shadow:[$o]}],opacity:[{opacity:[v]}],"mix-blend":[{"mix-blend":[...Q(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":Q()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[c]}],"drop-shadow":[{"drop-shadow":["","none",hn,V]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[f]}],invert:[{invert:[p]}],saturate:[{saturate:[y]}],sepia:[{sepia:[E]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[c]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[f]}],"backdrop-invert":[{"backdrop-invert":[p]}],"backdrop-opacity":[{"backdrop-opacity":[v]}],"backdrop-saturate":[{"backdrop-saturate":[y]}],"backdrop-sepia":[{"backdrop-sepia":[E]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",V]}],duration:[{duration:L()}],ease:[{ease:["linear","in","out","in-out",V]}],delay:[{delay:L()}],animate:[{animate:["none","spin","ping","pulse","bounce",V]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[w]}],"scale-x":[{"scale-x":[w]}],"scale-y":[{"scale-y":[w]}],rotate:[{rotate:[Fo,V]}],"translate-x":[{"translate-x":[j]}],"translate-y":[{"translate-y":[j]}],"skew-x":[{"skew-x":[C]}],"skew-y":[{"skew-y":[C]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",V]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",V]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":M()}],"scroll-mx":[{"scroll-mx":M()}],"scroll-my":[{"scroll-my":M()}],"scroll-ms":[{"scroll-ms":M()}],"scroll-me":[{"scroll-me":M()}],"scroll-mt":[{"scroll-mt":M()}],"scroll-mr":[{"scroll-mr":M()}],"scroll-mb":[{"scroll-mb":M()}],"scroll-ml":[{"scroll-ml":M()}],"scroll-p":[{"scroll-p":M()}],"scroll-px":[{"scroll-px":M()}],"scroll-py":[{"scroll-py":M()}],"scroll-ps":[{"scroll-ps":M()}],"scroll-pe":[{"scroll-pe":M()}],"scroll-pt":[{"scroll-pt":M()}],"scroll-pr":[{"scroll-pr":M()}],"scroll-pb":[{"scroll-pb":M()}],"scroll-pl":[{"scroll-pl":M()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",V]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Ht,pn,jl]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},mS=X1(hS);function ce(...e){return mS(Tg(e))}const gS=y1,$g=h.forwardRef(({className:e,...t},n)=>l.jsx(Sg,{ref:n,className:ce("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));$g.displayName=Sg.displayName;const vS=_a("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),Ug=h.forwardRef(({className:e,variant:t,...n},r)=>l.jsx(bg,{ref:r,className:ce(vS({variant:t}),e),...n}));Ug.displayName=bg.displayName;const yS=h.forwardRef(({className:e,...t},n)=>l.jsx(Cg,{ref:n,className:ce("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));yS.displayName=Cg.displayName;const Bg=h.forwardRef(({className:e,...t},n)=>l.jsx(Ng,{ref:n,className:ce("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:l.jsx(M1,{className:"h-4 w-4"})}));Bg.displayName=Ng.displayName;const Vg=h.forwardRef(({className:e,...t},n)=>l.jsx(Eg,{ref:n,className:ce("text-sm font-semibold",e),...t}));Vg.displayName=Eg.displayName;const Wg=h.forwardRef(({className:e,...t},n)=>l.jsx(kg,{ref:n,className:ce("text-sm opacity-90",e),...t}));Wg.displayName=kg.displayName;function xS(){const{toasts:e}=Tw();return l.jsxs(gS,{children:[e.map(function({id:t,title:n,description:r,action:o,...s}){return l.jsxs(Ug,{...s,children:[l.jsxs("div",{className:"grid gap-1",children:[n&&l.jsx(Vg,{children:n}),r&&l.jsx(Wg,{children:r})]}),o,l.jsx(Bg,{})]},t)}),l.jsx($g,{})]})}var Qf=["light","dark"],wS="(prefers-color-scheme: dark)",SS=h.createContext(void 0),bS={setTheme:e=>{},themes:[]},ES=()=>{var e;return(e=h.useContext(SS))!=null?e:bS};h.memo(({forcedTheme:e,storageKey:t,attribute:n,enableSystem:r,enableColorScheme:o,defaultTheme:s,value:i,attrs:a,nonce:c})=>{let u=s==="system",f=n==="class"?`var d=document.documentElement,c=d.classList;${`c.remove(${a.map(b=>`'${b}'`).join(",")})`};`:`var d=document.documentElement,n='${n}',s='setAttribute';`,p=o?Qf.includes(s)&&s?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${s}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",d=(b,g=!1,S=!0)=>{let v=i?i[b]:b,m=g?b+"|| ''":`'${v}'`,y="";return o&&S&&!g&&Qf.includes(b)&&(y+=`d.style.colorScheme = '${b}';`),n==="class"?g||v?y+=`c.add(${m})`:y+="null":v&&(y+=`d[s](n,${m})`),y},x=e?`!function(){${f}${d(e)}}()`:r?`!function(){try{${f}var e=localStorage.getItem('${t}');if('system'===e||(!e&&${u})){var t='${wS}',m=window.matchMedia(t);if(m.media!==t||m.matches){${d("dark")}}else{${d("light")}}}else if(e){${i?`var x=${JSON.stringify(i)};`:""}${d(i?"x[e]":"e",!0)}}${u?"":"else{"+d(s,!1,!1)+"}"}${p}}catch(e){}}()`:`!function(){try{${f}var e=localStorage.getItem('${t}');if(e){${i?`var x=${JSON.stringify(i)};`:""}${d(i?"x[e]":"e",!0)}}else{${d(s,!1,!1)};}${p}}catch(t){}}();`;return h.createElement("script",{nonce:c,dangerouslySetInnerHTML:{__html:x}})});var kS=e=>{switch(e){case"success":return jS;case"info":return PS;case"warning":return TS;case"error":return RS;default:return null}},CS=Array(12).fill(0),NS=({visible:e})=>A.createElement("div",{className:"sonner-loading-wrapper","data-visible":e},A.createElement("div",{className:"sonner-spinner"},CS.map((t,n)=>A.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${n}`})))),jS=A.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},A.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),TS=A.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},A.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),PS=A.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},A.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),RS=A.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},A.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),AS=()=>{let[e,t]=A.useState(document.hidden);return A.useEffect(()=>{let n=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",n),()=>window.removeEventListener("visibilitychange",n)},[]),e},Lc=1,_S=class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:n,...r}=e,o=typeof(e==null?void 0:e.id)=="number"||((t=e.id)==null?void 0:t.length)>0?e.id:Lc++,s=this.toasts.find(a=>a.id===o),i=e.dismissible===void 0?!0:e.dismissible;return s?this.toasts=this.toasts.map(a=>a.id===o?(this.publish({...a,...e,id:o,title:n}),{...a,...e,id:o,dismissible:i,title:n}):a):this.addToast({title:n,...r,dismissible:i,id:o}),o},this.dismiss=e=>(e||this.toasts.forEach(t=>{this.subscribers.forEach(n=>n({id:t.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{if(!t)return;let n;t.loading!==void 0&&(n=this.create({...t,promise:e,type:"loading",message:t.loading,description:typeof t.description!="function"?t.description:void 0}));let r=e instanceof Promise?e:e(),o=n!==void 0;return r.then(async s=>{if(DS(s)&&!s.ok){o=!1;let i=typeof t.error=="function"?await t.error(`HTTP error! status: ${s.status}`):t.error,a=typeof t.description=="function"?await t.description(`HTTP error! status: ${s.status}`):t.description;this.create({id:n,type:"error",message:i,description:a})}else if(t.success!==void 0){o=!1;let i=typeof t.success=="function"?await t.success(s):t.success,a=typeof t.description=="function"?await t.description(s):t.description;this.create({id:n,type:"success",message:i,description:a})}}).catch(async s=>{if(t.error!==void 0){o=!1;let i=typeof t.error=="function"?await t.error(s):t.error,a=typeof t.description=="function"?await t.description(s):t.description;this.create({id:n,type:"error",message:i,description:a})}}).finally(()=>{var s;o&&(this.dismiss(n),n=void 0),(s=t.finally)==null||s.call(t)}),n},this.custom=(e,t)=>{let n=(t==null?void 0:t.id)||Lc++;return this.create({jsx:e(n),id:n,...t}),n},this.subscribers=[],this.toasts=[]}},Je=new _S,OS=(e,t)=>{let n=(t==null?void 0:t.id)||Lc++;return Je.addToast({title:e,...t,id:n}),n},DS=e=>e&&typeof e=="object"&&"ok"in e&&typeof e.ok=="boolean"&&"status"in e&&typeof e.status=="number",IS=OS,MS=()=>Je.toasts,ft=Object.assign(IS,{success:Je.success,info:Je.info,warning:Je.warning,error:Je.error,custom:Je.custom,message:Je.message,promise:Je.promise,dismiss:Je.dismiss,loading:Je.loading},{getHistory:MS});function LS(e,{insertAt:t}={}){if(typeof document>"u")return;let n=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",t==="top"&&n.firstChild?n.insertBefore(r,n.firstChild):n.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}LS(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999}:where([data-sonner-toaster][data-x-position="right"]){right:max(var(--offset),env(safe-area-inset-right))}:where([data-sonner-toaster][data-x-position="left"]){left:max(var(--offset),env(safe-area-inset-left))}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:max(var(--offset),env(safe-area-inset-top))}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:max(var(--offset),env(safe-area-inset-bottom))}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;background:var(--gray1);color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:0;right:0;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);function ii(e){return e.label!==void 0}var zS=3,FS="32px",$S=4e3,US=356,BS=14,VS=20,WS=200;function HS(...e){return e.filter(Boolean).join(" ")}var KS=e=>{var t,n,r,o,s,i,a,c,u,f;let{invert:p,toast:d,unstyled:x,interacting:b,setHeights:g,visibleToasts:S,heights:v,index:m,toasts:y,expanded:w,removeToast:E,defaultRichColors:C,closeButton:k,style:j,cancelButtonStyle:O,actionButtonStyle:_,className:F="",descriptionClassName:M="",duration:H,position:D,gap:K,loadingIcon:U,expandByDefault:Q,classNames:N,icons:R,closeButtonAriaLabel:z="Close toast",pauseWhenPageIsHidden:L,cn:$}=e,[Y,de]=A.useState(!1),[qe,J]=A.useState(!1),[yt,an]=A.useState(!1),[ln,cn]=A.useState(!1),[Ms,Sr]=A.useState(0),[Yn,Ro]=A.useState(0),Ls=A.useRef(null),un=A.useRef(null),Ka=m===0,Qa=m+1<=S,Ce=d.type,br=d.dismissible!==!1,W0=d.className||"",H0=d.descriptionClassName||"",zs=A.useMemo(()=>v.findIndex(B=>B.toastId===d.id)||0,[v,d.id]),K0=A.useMemo(()=>{var B;return(B=d.closeButton)!=null?B:k},[d.closeButton,k]),xd=A.useMemo(()=>d.duration||H||$S,[d.duration,H]),Ga=A.useRef(0),Er=A.useRef(0),wd=A.useRef(0),kr=A.useRef(null),[Sd,Q0]=D.split("-"),bd=A.useMemo(()=>v.reduce((B,oe,te)=>te>=zs?B:B+oe.height,0),[v,zs]),Ed=AS(),G0=d.invert||p,Ya=Ce==="loading";Er.current=A.useMemo(()=>zs*K+bd,[zs,bd]),A.useEffect(()=>{de(!0)},[]),A.useLayoutEffect(()=>{if(!Y)return;let B=un.current,oe=B.style.height;B.style.height="auto";let te=B.getBoundingClientRect().height;B.style.height=oe,Ro(te),g(_t=>_t.find(Ot=>Ot.toastId===d.id)?_t.map(Ot=>Ot.toastId===d.id?{...Ot,height:te}:Ot):[{toastId:d.id,height:te,position:d.position},..._t])},[Y,d.title,d.description,g,d.id]);let dn=A.useCallback(()=>{J(!0),Sr(Er.current),g(B=>B.filter(oe=>oe.toastId!==d.id)),setTimeout(()=>{E(d)},WS)},[d,E,g,Er]);A.useEffect(()=>{if(d.promise&&Ce==="loading"||d.duration===1/0||d.type==="loading")return;let B,oe=xd;return w||b||L&&Ed?(()=>{if(wd.current<Ga.current){let te=new Date().getTime()-Ga.current;oe=oe-te}wd.current=new Date().getTime()})():oe!==1/0&&(Ga.current=new Date().getTime(),B=setTimeout(()=>{var te;(te=d.onAutoClose)==null||te.call(d,d),dn()},oe)),()=>clearTimeout(B)},[w,b,Q,d,xd,dn,d.promise,Ce,L,Ed]),A.useEffect(()=>{let B=un.current;if(B){let oe=B.getBoundingClientRect().height;return Ro(oe),g(te=>[{toastId:d.id,height:oe,position:d.position},...te]),()=>g(te=>te.filter(_t=>_t.toastId!==d.id))}},[g,d.id]),A.useEffect(()=>{d.delete&&dn()},[dn,d.delete]);function Y0(){return R!=null&&R.loading?A.createElement("div",{className:"sonner-loader","data-visible":Ce==="loading"},R.loading):U?A.createElement("div",{className:"sonner-loader","data-visible":Ce==="loading"},U):A.createElement(NS,{visible:Ce==="loading"})}return A.createElement("li",{"aria-live":d.important?"assertive":"polite","aria-atomic":"true",role:"status",tabIndex:0,ref:un,className:$(F,W0,N==null?void 0:N.toast,(t=d==null?void 0:d.classNames)==null?void 0:t.toast,N==null?void 0:N.default,N==null?void 0:N[Ce],(n=d==null?void 0:d.classNames)==null?void 0:n[Ce]),"data-sonner-toast":"","data-rich-colors":(r=d.richColors)!=null?r:C,"data-styled":!(d.jsx||d.unstyled||x),"data-mounted":Y,"data-promise":!!d.promise,"data-removed":qe,"data-visible":Qa,"data-y-position":Sd,"data-x-position":Q0,"data-index":m,"data-front":Ka,"data-swiping":yt,"data-dismissible":br,"data-type":Ce,"data-invert":G0,"data-swipe-out":ln,"data-expanded":!!(w||Q&&Y),style:{"--index":m,"--toasts-before":m,"--z-index":y.length-m,"--offset":`${qe?Ms:Er.current}px`,"--initial-height":Q?"auto":`${Yn}px`,...j,...d.style},onPointerDown:B=>{Ya||!br||(Ls.current=new Date,Sr(Er.current),B.target.setPointerCapture(B.pointerId),B.target.tagName!=="BUTTON"&&(an(!0),kr.current={x:B.clientX,y:B.clientY}))},onPointerUp:()=>{var B,oe,te,_t;if(ln||!br)return;kr.current=null;let Ot=Number(((B=un.current)==null?void 0:B.style.getPropertyValue("--swipe-amount").replace("px",""))||0),Fs=new Date().getTime()-((oe=Ls.current)==null?void 0:oe.getTime()),X0=Math.abs(Ot)/Fs;if(Math.abs(Ot)>=VS||X0>.11){Sr(Er.current),(te=d.onDismiss)==null||te.call(d,d),dn(),cn(!0);return}(_t=un.current)==null||_t.style.setProperty("--swipe-amount","0px"),an(!1)},onPointerMove:B=>{var oe;if(!kr.current||!br)return;let te=B.clientY-kr.current.y,_t=B.clientX-kr.current.x,Ot=(Sd==="top"?Math.min:Math.max)(0,te),Fs=B.pointerType==="touch"?10:2;Math.abs(Ot)>Fs?(oe=un.current)==null||oe.style.setProperty("--swipe-amount",`${te}px`):Math.abs(_t)>Fs&&(kr.current=null)}},K0&&!d.jsx?A.createElement("button",{"aria-label":z,"data-disabled":Ya,"data-close-button":!0,onClick:Ya||!br?()=>{}:()=>{var B;dn(),(B=d.onDismiss)==null||B.call(d,d)},className:$(N==null?void 0:N.closeButton,(o=d==null?void 0:d.classNames)==null?void 0:o.closeButton)},A.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},A.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),A.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))):null,d.jsx||A.isValidElement(d.title)?d.jsx||d.title:A.createElement(A.Fragment,null,Ce||d.icon||d.promise?A.createElement("div",{"data-icon":"",className:$(N==null?void 0:N.icon,(s=d==null?void 0:d.classNames)==null?void 0:s.icon)},d.promise||d.type==="loading"&&!d.icon?d.icon||Y0():null,d.type!=="loading"?d.icon||(R==null?void 0:R[Ce])||kS(Ce):null):null,A.createElement("div",{"data-content":"",className:$(N==null?void 0:N.content,(i=d==null?void 0:d.classNames)==null?void 0:i.content)},A.createElement("div",{"data-title":"",className:$(N==null?void 0:N.title,(a=d==null?void 0:d.classNames)==null?void 0:a.title)},d.title),d.description?A.createElement("div",{"data-description":"",className:$(M,H0,N==null?void 0:N.description,(c=d==null?void 0:d.classNames)==null?void 0:c.description)},d.description):null),A.isValidElement(d.cancel)?d.cancel:d.cancel&&ii(d.cancel)?A.createElement("button",{"data-button":!0,"data-cancel":!0,style:d.cancelButtonStyle||O,onClick:B=>{var oe,te;ii(d.cancel)&&br&&((te=(oe=d.cancel).onClick)==null||te.call(oe,B),dn())},className:$(N==null?void 0:N.cancelButton,(u=d==null?void 0:d.classNames)==null?void 0:u.cancelButton)},d.cancel.label):null,A.isValidElement(d.action)?d.action:d.action&&ii(d.action)?A.createElement("button",{"data-button":!0,"data-action":!0,style:d.actionButtonStyle||_,onClick:B=>{var oe,te;ii(d.action)&&(B.defaultPrevented||((te=(oe=d.action).onClick)==null||te.call(oe,B),dn()))},className:$(N==null?void 0:N.actionButton,(f=d==null?void 0:d.classNames)==null?void 0:f.actionButton)},d.action.label):null))};function Gf(){if(typeof window>"u"||typeof document>"u")return"ltr";let e=document.documentElement.getAttribute("dir");return e==="auto"||!e?window.getComputedStyle(document.documentElement).direction:e}var QS=e=>{let{invert:t,position:n="bottom-right",hotkey:r=["altKey","KeyT"],expand:o,closeButton:s,className:i,offset:a,theme:c="light",richColors:u,duration:f,style:p,visibleToasts:d=zS,toastOptions:x,dir:b=Gf(),gap:g=BS,loadingIcon:S,icons:v,containerAriaLabel:m="Notifications",pauseWhenPageIsHidden:y,cn:w=HS}=e,[E,C]=A.useState([]),k=A.useMemo(()=>Array.from(new Set([n].concat(E.filter(L=>L.position).map(L=>L.position)))),[E,n]),[j,O]=A.useState([]),[_,F]=A.useState(!1),[M,H]=A.useState(!1),[D,K]=A.useState(c!=="system"?c:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),U=A.useRef(null),Q=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),N=A.useRef(null),R=A.useRef(!1),z=A.useCallback(L=>{var $;($=E.find(Y=>Y.id===L.id))!=null&&$.delete||Je.dismiss(L.id),C(Y=>Y.filter(({id:de})=>de!==L.id))},[E]);return A.useEffect(()=>Je.subscribe(L=>{if(L.dismiss){C($=>$.map(Y=>Y.id===L.id?{...Y,delete:!0}:Y));return}setTimeout(()=>{tg.flushSync(()=>{C($=>{let Y=$.findIndex(de=>de.id===L.id);return Y!==-1?[...$.slice(0,Y),{...$[Y],...L},...$.slice(Y+1)]:[L,...$]})})})}),[]),A.useEffect(()=>{if(c!=="system"){K(c);return}c==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?K("dark"):K("light")),typeof window<"u"&&window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",({matches:L})=>{K(L?"dark":"light")})},[c]),A.useEffect(()=>{E.length<=1&&F(!1)},[E]),A.useEffect(()=>{let L=$=>{var Y,de;r.every(qe=>$[qe]||$.code===qe)&&(F(!0),(Y=U.current)==null||Y.focus()),$.code==="Escape"&&(document.activeElement===U.current||(de=U.current)!=null&&de.contains(document.activeElement))&&F(!1)};return document.addEventListener("keydown",L),()=>document.removeEventListener("keydown",L)},[r]),A.useEffect(()=>{if(U.current)return()=>{N.current&&(N.current.focus({preventScroll:!0}),N.current=null,R.current=!1)}},[U.current]),E.length?A.createElement("section",{"aria-label":`${m} ${Q}`,tabIndex:-1},k.map((L,$)=>{var Y;let[de,qe]=L.split("-");return A.createElement("ol",{key:L,dir:b==="auto"?Gf():b,tabIndex:-1,ref:U,className:i,"data-sonner-toaster":!0,"data-theme":D,"data-y-position":de,"data-x-position":qe,style:{"--front-toast-height":`${((Y=j[0])==null?void 0:Y.height)||0}px`,"--offset":typeof a=="number"?`${a}px`:a||FS,"--width":`${US}px`,"--gap":`${g}px`,...p},onBlur:J=>{R.current&&!J.currentTarget.contains(J.relatedTarget)&&(R.current=!1,N.current&&(N.current.focus({preventScroll:!0}),N.current=null))},onFocus:J=>{J.target instanceof HTMLElement&&J.target.dataset.dismissible==="false"||R.current||(R.current=!0,N.current=J.relatedTarget)},onMouseEnter:()=>F(!0),onMouseMove:()=>F(!0),onMouseLeave:()=>{M||F(!1)},onPointerDown:J=>{J.target instanceof HTMLElement&&J.target.dataset.dismissible==="false"||H(!0)},onPointerUp:()=>H(!1)},E.filter(J=>!J.position&&$===0||J.position===L).map((J,yt)=>{var an,ln;return A.createElement(KS,{key:J.id,icons:v,index:yt,toast:J,defaultRichColors:u,duration:(an=x==null?void 0:x.duration)!=null?an:f,className:x==null?void 0:x.className,descriptionClassName:x==null?void 0:x.descriptionClassName,invert:t,visibleToasts:d,closeButton:(ln=x==null?void 0:x.closeButton)!=null?ln:s,interacting:M,position:L,style:x==null?void 0:x.style,unstyled:x==null?void 0:x.unstyled,classNames:x==null?void 0:x.classNames,cancelButtonStyle:x==null?void 0:x.cancelButtonStyle,actionButtonStyle:x==null?void 0:x.actionButtonStyle,removeToast:z,toasts:E.filter(cn=>cn.position==J.position),heights:j.filter(cn=>cn.position==J.position),setHeights:O,expandByDefault:o,gap:g,loadingIcon:S,expanded:_,pauseWhenPageIsHidden:y,cn:w})}))})):null};const GS=({...e})=>{const{theme:t="system"}=ES();return l.jsx(QS,{theme:t,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})};var YS=Zp.useId||(()=>{}),XS=0;function Pi(e){const[t,n]=h.useState(YS());return $n(()=>{n(r=>r??String(XS++))},[e]),t?`radix-${t}`:""}const qS=["top","right","bottom","left"],Un=Math.min,tt=Math.max,ra=Math.round,ai=Math.floor,Bn=e=>({x:e,y:e}),ZS={left:"right",right:"left",bottom:"top",top:"bottom"},JS={start:"end",end:"start"};function zc(e,t,n){return tt(e,Un(t,n))}function nn(e,t){return typeof e=="function"?e(t):e}function rn(e){return e.split("-")[0]}function No(e){return e.split("-")[1]}function td(e){return e==="x"?"y":"x"}function nd(e){return e==="y"?"height":"width"}function Vn(e){return["top","bottom"].includes(rn(e))?"y":"x"}function rd(e){return td(Vn(e))}function eb(e,t,n){n===void 0&&(n=!1);const r=No(e),o=rd(e),s=nd(o);let i=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(i=oa(i)),[i,oa(i)]}function tb(e){const t=oa(e);return[Fc(e),t,Fc(t)]}function Fc(e){return e.replace(/start|end/g,t=>JS[t])}function nb(e,t,n){const r=["left","right"],o=["right","left"],s=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?s:i;default:return[]}}function rb(e,t,n,r){const o=No(e);let s=nb(rn(e),n==="start",r);return o&&(s=s.map(i=>i+"-"+o),t&&(s=s.concat(s.map(Fc)))),s}function oa(e){return e.replace(/left|right|bottom|top/g,t=>ZS[t])}function ob(e){return{top:0,right:0,bottom:0,left:0,...e}}function Hg(e){return typeof e!="number"?ob(e):{top:e,right:e,bottom:e,left:e}}function sa(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function Yf(e,t,n){let{reference:r,floating:o}=e;const s=Vn(t),i=rd(t),a=nd(i),c=rn(t),u=s==="y",f=r.x+r.width/2-o.width/2,p=r.y+r.height/2-o.height/2,d=r[a]/2-o[a]/2;let x;switch(c){case"top":x={x:f,y:r.y-o.height};break;case"bottom":x={x:f,y:r.y+r.height};break;case"right":x={x:r.x+r.width,y:p};break;case"left":x={x:r.x-o.width,y:p};break;default:x={x:r.x,y:r.y}}switch(No(t)){case"start":x[i]-=d*(n&&u?-1:1);break;case"end":x[i]+=d*(n&&u?-1:1);break}return x}const sb=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:s=[],platform:i}=n,a=s.filter(Boolean),c=await(i.isRTL==null?void 0:i.isRTL(t));let u=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:f,y:p}=Yf(u,r,c),d=r,x={},b=0;for(let g=0;g<a.length;g++){const{name:S,fn:v}=a[g],{x:m,y,data:w,reset:E}=await v({x:f,y:p,initialPlacement:r,placement:d,strategy:o,middlewareData:x,rects:u,platform:i,elements:{reference:e,floating:t}});f=m??f,p=y??p,x={...x,[S]:{...x[S],...w}},E&&b<=50&&(b++,typeof E=="object"&&(E.placement&&(d=E.placement),E.rects&&(u=E.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:o}):E.rects),{x:f,y:p}=Yf(u,d,c)),g=-1)}return{x:f,y:p,placement:d,strategy:o,middlewareData:x}};async function ws(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:s,rects:i,elements:a,strategy:c}=e,{boundary:u="clippingAncestors",rootBoundary:f="viewport",elementContext:p="floating",altBoundary:d=!1,padding:x=0}=nn(t,e),b=Hg(x),S=a[d?p==="floating"?"reference":"floating":p],v=sa(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(S)))==null||n?S:S.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(a.floating)),boundary:u,rootBoundary:f,strategy:c})),m=p==="floating"?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,y=await(s.getOffsetParent==null?void 0:s.getOffsetParent(a.floating)),w=await(s.isElement==null?void 0:s.isElement(y))?await(s.getScale==null?void 0:s.getScale(y))||{x:1,y:1}:{x:1,y:1},E=sa(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:m,offsetParent:y,strategy:c}):m);return{top:(v.top-E.top+b.top)/w.y,bottom:(E.bottom-v.bottom+b.bottom)/w.y,left:(v.left-E.left+b.left)/w.x,right:(E.right-v.right+b.right)/w.x}}const ib=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:s,platform:i,elements:a,middlewareData:c}=t,{element:u,padding:f=0}=nn(e,t)||{};if(u==null)return{};const p=Hg(f),d={x:n,y:r},x=rd(o),b=nd(x),g=await i.getDimensions(u),S=x==="y",v=S?"top":"left",m=S?"bottom":"right",y=S?"clientHeight":"clientWidth",w=s.reference[b]+s.reference[x]-d[x]-s.floating[b],E=d[x]-s.reference[x],C=await(i.getOffsetParent==null?void 0:i.getOffsetParent(u));let k=C?C[y]:0;(!k||!await(i.isElement==null?void 0:i.isElement(C)))&&(k=a.floating[y]||s.floating[b]);const j=w/2-E/2,O=k/2-g[b]/2-1,_=Un(p[v],O),F=Un(p[m],O),M=_,H=k-g[b]-F,D=k/2-g[b]/2+j,K=zc(M,D,H),U=!c.arrow&&No(o)!=null&&D!==K&&s.reference[b]/2-(D<M?_:F)-g[b]/2<0,Q=U?D<M?D-M:D-H:0;return{[x]:d[x]+Q,data:{[x]:K,centerOffset:D-K-Q,...U&&{alignmentOffset:Q}},reset:U}}}),ab=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:s,rects:i,initialPlacement:a,platform:c,elements:u}=t,{mainAxis:f=!0,crossAxis:p=!0,fallbackPlacements:d,fallbackStrategy:x="bestFit",fallbackAxisSideDirection:b="none",flipAlignment:g=!0,...S}=nn(e,t);if((n=s.arrow)!=null&&n.alignmentOffset)return{};const v=rn(o),m=Vn(a),y=rn(a)===a,w=await(c.isRTL==null?void 0:c.isRTL(u.floating)),E=d||(y||!g?[oa(a)]:tb(a)),C=b!=="none";!d&&C&&E.push(...rb(a,g,b,w));const k=[a,...E],j=await ws(t,S),O=[];let _=((r=s.flip)==null?void 0:r.overflows)||[];if(f&&O.push(j[v]),p){const D=eb(o,i,w);O.push(j[D[0]],j[D[1]])}if(_=[..._,{placement:o,overflows:O}],!O.every(D=>D<=0)){var F,M;const D=(((F=s.flip)==null?void 0:F.index)||0)+1,K=k[D];if(K)return{data:{index:D,overflows:_},reset:{placement:K}};let U=(M=_.filter(Q=>Q.overflows[0]<=0).sort((Q,N)=>Q.overflows[1]-N.overflows[1])[0])==null?void 0:M.placement;if(!U)switch(x){case"bestFit":{var H;const Q=(H=_.filter(N=>{if(C){const R=Vn(N.placement);return R===m||R==="y"}return!0}).map(N=>[N.placement,N.overflows.filter(R=>R>0).reduce((R,z)=>R+z,0)]).sort((N,R)=>N[1]-R[1])[0])==null?void 0:H[0];Q&&(U=Q);break}case"initialPlacement":U=a;break}if(o!==U)return{reset:{placement:U}}}return{}}}};function Xf(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function qf(e){return qS.some(t=>e[t]>=0)}const lb=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=nn(e,t);switch(r){case"referenceHidden":{const s=await ws(t,{...o,elementContext:"reference"}),i=Xf(s,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:qf(i)}}}case"escaped":{const s=await ws(t,{...o,altBoundary:!0}),i=Xf(s,n.floating);return{data:{escapedOffsets:i,escaped:qf(i)}}}default:return{}}}}};async function cb(e,t){const{placement:n,platform:r,elements:o}=e,s=await(r.isRTL==null?void 0:r.isRTL(o.floating)),i=rn(n),a=No(n),c=Vn(n)==="y",u=["left","top"].includes(i)?-1:1,f=s&&c?-1:1,p=nn(t,e);let{mainAxis:d,crossAxis:x,alignmentAxis:b}=typeof p=="number"?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:p.mainAxis||0,crossAxis:p.crossAxis||0,alignmentAxis:p.alignmentAxis};return a&&typeof b=="number"&&(x=a==="end"?b*-1:b),c?{x:x*f,y:d*u}:{x:d*u,y:x*f}}const ub=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:s,placement:i,middlewareData:a}=t,c=await cb(t,e);return i===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:o+c.x,y:s+c.y,data:{...c,placement:i}}}}},db=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:s=!0,crossAxis:i=!1,limiter:a={fn:S=>{let{x:v,y:m}=S;return{x:v,y:m}}},...c}=nn(e,t),u={x:n,y:r},f=await ws(t,c),p=Vn(rn(o)),d=td(p);let x=u[d],b=u[p];if(s){const S=d==="y"?"top":"left",v=d==="y"?"bottom":"right",m=x+f[S],y=x-f[v];x=zc(m,x,y)}if(i){const S=p==="y"?"top":"left",v=p==="y"?"bottom":"right",m=b+f[S],y=b-f[v];b=zc(m,b,y)}const g=a.fn({...t,[d]:x,[p]:b});return{...g,data:{x:g.x-n,y:g.y-r,enabled:{[d]:s,[p]:i}}}}}},fb=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:s,middlewareData:i}=t,{offset:a=0,mainAxis:c=!0,crossAxis:u=!0}=nn(e,t),f={x:n,y:r},p=Vn(o),d=td(p);let x=f[d],b=f[p];const g=nn(a,t),S=typeof g=="number"?{mainAxis:g,crossAxis:0}:{mainAxis:0,crossAxis:0,...g};if(c){const y=d==="y"?"height":"width",w=s.reference[d]-s.floating[y]+S.mainAxis,E=s.reference[d]+s.reference[y]-S.mainAxis;x<w?x=w:x>E&&(x=E)}if(u){var v,m;const y=d==="y"?"width":"height",w=["top","left"].includes(rn(o)),E=s.reference[p]-s.floating[y]+(w&&((v=i.offset)==null?void 0:v[p])||0)+(w?0:S.crossAxis),C=s.reference[p]+s.reference[y]+(w?0:((m=i.offset)==null?void 0:m[p])||0)-(w?S.crossAxis:0);b<E?b=E:b>C&&(b=C)}return{[d]:x,[p]:b}}}},pb=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:s,platform:i,elements:a}=t,{apply:c=()=>{},...u}=nn(e,t),f=await ws(t,u),p=rn(o),d=No(o),x=Vn(o)==="y",{width:b,height:g}=s.floating;let S,v;p==="top"||p==="bottom"?(S=p,v=d===(await(i.isRTL==null?void 0:i.isRTL(a.floating))?"start":"end")?"left":"right"):(v=p,S=d==="end"?"top":"bottom");const m=g-f.top-f.bottom,y=b-f.left-f.right,w=Un(g-f[S],m),E=Un(b-f[v],y),C=!t.middlewareData.shift;let k=w,j=E;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(j=y),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(k=m),C&&!d){const _=tt(f.left,0),F=tt(f.right,0),M=tt(f.top,0),H=tt(f.bottom,0);x?j=b-2*(_!==0||F!==0?_+F:tt(f.left,f.right)):k=g-2*(M!==0||H!==0?M+H:tt(f.top,f.bottom))}await c({...t,availableWidth:j,availableHeight:k});const O=await i.getDimensions(a.floating);return b!==O.width||g!==O.height?{reset:{rects:!0}}:{}}}};function Ia(){return typeof window<"u"}function jo(e){return Kg(e)?(e.nodeName||"").toLowerCase():"#document"}function st(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Wt(e){var t;return(t=(Kg(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Kg(e){return Ia()?e instanceof Node||e instanceof st(e).Node:!1}function Tt(e){return Ia()?e instanceof Element||e instanceof st(e).Element:!1}function Vt(e){return Ia()?e instanceof HTMLElement||e instanceof st(e).HTMLElement:!1}function Zf(e){return!Ia()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof st(e).ShadowRoot}function Ds(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Pt(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function hb(e){return["table","td","th"].includes(jo(e))}function Ma(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function od(e){const t=sd(),n=Tt(e)?Pt(e):e;return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function mb(e){let t=Wn(e);for(;Vt(t)&&!yo(t);){if(od(t))return t;if(Ma(t))return null;t=Wn(t)}return null}function sd(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function yo(e){return["html","body","#document"].includes(jo(e))}function Pt(e){return st(e).getComputedStyle(e)}function La(e){return Tt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Wn(e){if(jo(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Zf(e)&&e.host||Wt(e);return Zf(t)?t.host:t}function Qg(e){const t=Wn(e);return yo(t)?e.ownerDocument?e.ownerDocument.body:e.body:Vt(t)&&Ds(t)?t:Qg(t)}function Ss(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=Qg(e),s=o===((r=e.ownerDocument)==null?void 0:r.body),i=st(o);if(s){const a=$c(i);return t.concat(i,i.visualViewport||[],Ds(o)?o:[],a&&n?Ss(a):[])}return t.concat(o,Ss(o,[],n))}function $c(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Gg(e){const t=Pt(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=Vt(e),s=o?e.offsetWidth:n,i=o?e.offsetHeight:r,a=ra(n)!==s||ra(r)!==i;return a&&(n=s,r=i),{width:n,height:r,$:a}}function id(e){return Tt(e)?e:e.contextElement}function Yr(e){const t=id(e);if(!Vt(t))return Bn(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:s}=Gg(t);let i=(s?ra(n.width):n.width)/r,a=(s?ra(n.height):n.height)/o;return(!i||!Number.isFinite(i))&&(i=1),(!a||!Number.isFinite(a))&&(a=1),{x:i,y:a}}const gb=Bn(0);function Yg(e){const t=st(e);return!sd()||!t.visualViewport?gb:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function vb(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==st(e)?!1:t}function fr(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),s=id(e);let i=Bn(1);t&&(r?Tt(r)&&(i=Yr(r)):i=Yr(e));const a=vb(s,n,r)?Yg(s):Bn(0);let c=(o.left+a.x)/i.x,u=(o.top+a.y)/i.y,f=o.width/i.x,p=o.height/i.y;if(s){const d=st(s),x=r&&Tt(r)?st(r):r;let b=d,g=$c(b);for(;g&&r&&x!==b;){const S=Yr(g),v=g.getBoundingClientRect(),m=Pt(g),y=v.left+(g.clientLeft+parseFloat(m.paddingLeft))*S.x,w=v.top+(g.clientTop+parseFloat(m.paddingTop))*S.y;c*=S.x,u*=S.y,f*=S.x,p*=S.y,c+=y,u+=w,b=st(g),g=$c(b)}}return sa({width:f,height:p,x:c,y:u})}function yb(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const s=o==="fixed",i=Wt(r),a=t?Ma(t.floating):!1;if(r===i||a&&s)return n;let c={scrollLeft:0,scrollTop:0},u=Bn(1);const f=Bn(0),p=Vt(r);if((p||!p&&!s)&&((jo(r)!=="body"||Ds(i))&&(c=La(r)),Vt(r))){const d=fr(r);u=Yr(r),f.x=d.x+r.clientLeft,f.y=d.y+r.clientTop}return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-c.scrollLeft*u.x+f.x,y:n.y*u.y-c.scrollTop*u.y+f.y}}function xb(e){return Array.from(e.getClientRects())}function Uc(e,t){const n=La(e).scrollLeft;return t?t.left+n:fr(Wt(e)).left+n}function wb(e){const t=Wt(e),n=La(e),r=e.ownerDocument.body,o=tt(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),s=tt(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+Uc(e);const a=-n.scrollTop;return Pt(r).direction==="rtl"&&(i+=tt(t.clientWidth,r.clientWidth)-o),{width:o,height:s,x:i,y:a}}function Sb(e,t){const n=st(e),r=Wt(e),o=n.visualViewport;let s=r.clientWidth,i=r.clientHeight,a=0,c=0;if(o){s=o.width,i=o.height;const u=sd();(!u||u&&t==="fixed")&&(a=o.offsetLeft,c=o.offsetTop)}return{width:s,height:i,x:a,y:c}}function bb(e,t){const n=fr(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,s=Vt(e)?Yr(e):Bn(1),i=e.clientWidth*s.x,a=e.clientHeight*s.y,c=o*s.x,u=r*s.y;return{width:i,height:a,x:c,y:u}}function Jf(e,t,n){let r;if(t==="viewport")r=Sb(e,n);else if(t==="document")r=wb(Wt(e));else if(Tt(t))r=bb(t,n);else{const o=Yg(e);r={...t,x:t.x-o.x,y:t.y-o.y}}return sa(r)}function Xg(e,t){const n=Wn(e);return n===t||!Tt(n)||yo(n)?!1:Pt(n).position==="fixed"||Xg(n,t)}function Eb(e,t){const n=t.get(e);if(n)return n;let r=Ss(e,[],!1).filter(a=>Tt(a)&&jo(a)!=="body"),o=null;const s=Pt(e).position==="fixed";let i=s?Wn(e):e;for(;Tt(i)&&!yo(i);){const a=Pt(i),c=od(i);!c&&a.position==="fixed"&&(o=null),(s?!c&&!o:!c&&a.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||Ds(i)&&!c&&Xg(e,i))?r=r.filter(f=>f!==i):o=a,i=Wn(i)}return t.set(e,r),r}function kb(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[...n==="clippingAncestors"?Ma(t)?[]:Eb(t,this._c):[].concat(n),r],a=i[0],c=i.reduce((u,f)=>{const p=Jf(t,f,o);return u.top=tt(p.top,u.top),u.right=Un(p.right,u.right),u.bottom=Un(p.bottom,u.bottom),u.left=tt(p.left,u.left),u},Jf(t,a,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}}function Cb(e){const{width:t,height:n}=Gg(e);return{width:t,height:n}}function Nb(e,t,n){const r=Vt(t),o=Wt(t),s=n==="fixed",i=fr(e,!0,s,t);let a={scrollLeft:0,scrollTop:0};const c=Bn(0);if(r||!r&&!s)if((jo(t)!=="body"||Ds(o))&&(a=La(t)),r){const x=fr(t,!0,s,t);c.x=x.x+t.clientLeft,c.y=x.y+t.clientTop}else o&&(c.x=Uc(o));let u=0,f=0;if(o&&!r&&!s){const x=o.getBoundingClientRect();f=x.top+a.scrollTop,u=x.left+a.scrollLeft-Uc(o,x)}const p=i.left+a.scrollLeft-c.x-u,d=i.top+a.scrollTop-c.y-f;return{x:p,y:d,width:i.width,height:i.height}}function Tl(e){return Pt(e).position==="static"}function ep(e,t){if(!Vt(e)||Pt(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Wt(e)===n&&(n=n.ownerDocument.body),n}function qg(e,t){const n=st(e);if(Ma(e))return n;if(!Vt(e)){let o=Wn(e);for(;o&&!yo(o);){if(Tt(o)&&!Tl(o))return o;o=Wn(o)}return n}let r=ep(e,t);for(;r&&hb(r)&&Tl(r);)r=ep(r,t);return r&&yo(r)&&Tl(r)&&!od(r)?n:r||mb(e)||n}const jb=async function(e){const t=this.getOffsetParent||qg,n=this.getDimensions,r=await n(e.floating);return{reference:Nb(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function Tb(e){return Pt(e).direction==="rtl"}const Pb={convertOffsetParentRelativeRectToViewportRelativeRect:yb,getDocumentElement:Wt,getClippingRect:kb,getOffsetParent:qg,getElementRects:jb,getClientRects:xb,getDimensions:Cb,getScale:Yr,isElement:Tt,isRTL:Tb};function Rb(e,t){let n=null,r;const o=Wt(e);function s(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function i(a,c){a===void 0&&(a=!1),c===void 0&&(c=1),s();const{left:u,top:f,width:p,height:d}=e.getBoundingClientRect();if(a||t(),!p||!d)return;const x=ai(f),b=ai(o.clientWidth-(u+p)),g=ai(o.clientHeight-(f+d)),S=ai(u),m={rootMargin:-x+"px "+-b+"px "+-g+"px "+-S+"px",threshold:tt(0,Un(1,c))||1};let y=!0;function w(E){const C=E[0].intersectionRatio;if(C!==c){if(!y)return i();C?i(!1,C):r=setTimeout(()=>{i(!1,1e-7)},1e3)}y=!1}try{n=new IntersectionObserver(w,{...m,root:o.ownerDocument})}catch{n=new IntersectionObserver(w,m)}n.observe(e)}return i(!0),s}function Ab(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:s=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:c=!1}=r,u=id(e),f=o||s?[...u?Ss(u):[],...Ss(t)]:[];f.forEach(v=>{o&&v.addEventListener("scroll",n,{passive:!0}),s&&v.addEventListener("resize",n)});const p=u&&a?Rb(u,n):null;let d=-1,x=null;i&&(x=new ResizeObserver(v=>{let[m]=v;m&&m.target===u&&x&&(x.unobserve(t),cancelAnimationFrame(d),d=requestAnimationFrame(()=>{var y;(y=x)==null||y.observe(t)})),n()}),u&&!c&&x.observe(u),x.observe(t));let b,g=c?fr(e):null;c&&S();function S(){const v=fr(e);g&&(v.x!==g.x||v.y!==g.y||v.width!==g.width||v.height!==g.height)&&n(),g=v,b=requestAnimationFrame(S)}return n(),()=>{var v;f.forEach(m=>{o&&m.removeEventListener("scroll",n),s&&m.removeEventListener("resize",n)}),p==null||p(),(v=x)==null||v.disconnect(),x=null,c&&cancelAnimationFrame(b)}}const _b=ub,Ob=db,Db=ab,Ib=pb,Mb=lb,tp=ib,Lb=fb,zb=(e,t,n)=>{const r=new Map,o={platform:Pb,...n},s={...o.platform,_c:r};return sb(e,t,{...o,platform:s})};var Ri=typeof document<"u"?h.useLayoutEffect:h.useEffect;function ia(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!ia(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const s=o[r];if(!(s==="_owner"&&e.$$typeof)&&!ia(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function Zg(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function np(e,t){const n=Zg(e);return Math.round(t*n)/n}function Pl(e){const t=h.useRef(e);return Ri(()=>{t.current=e}),t}function Fb(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:s,floating:i}={},transform:a=!0,whileElementsMounted:c,open:u}=e,[f,p]=h.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[d,x]=h.useState(r);ia(d,r)||x(r);const[b,g]=h.useState(null),[S,v]=h.useState(null),m=h.useCallback(N=>{N!==C.current&&(C.current=N,g(N))},[]),y=h.useCallback(N=>{N!==k.current&&(k.current=N,v(N))},[]),w=s||b,E=i||S,C=h.useRef(null),k=h.useRef(null),j=h.useRef(f),O=c!=null,_=Pl(c),F=Pl(o),M=Pl(u),H=h.useCallback(()=>{if(!C.current||!k.current)return;const N={placement:t,strategy:n,middleware:d};F.current&&(N.platform=F.current),zb(C.current,k.current,N).then(R=>{const z={...R,isPositioned:M.current!==!1};D.current&&!ia(j.current,z)&&(j.current=z,Os.flushSync(()=>{p(z)}))})},[d,t,n,F,M]);Ri(()=>{u===!1&&j.current.isPositioned&&(j.current.isPositioned=!1,p(N=>({...N,isPositioned:!1})))},[u]);const D=h.useRef(!1);Ri(()=>(D.current=!0,()=>{D.current=!1}),[]),Ri(()=>{if(w&&(C.current=w),E&&(k.current=E),w&&E){if(_.current)return _.current(w,E,H);H()}},[w,E,H,_,O]);const K=h.useMemo(()=>({reference:C,floating:k,setReference:m,setFloating:y}),[m,y]),U=h.useMemo(()=>({reference:w,floating:E}),[w,E]),Q=h.useMemo(()=>{const N={position:n,left:0,top:0};if(!U.floating)return N;const R=np(U.floating,f.x),z=np(U.floating,f.y);return a?{...N,transform:"translate("+R+"px, "+z+"px)",...Zg(U.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:R,top:z}},[n,a,U.floating,f.x,f.y]);return h.useMemo(()=>({...f,update:H,refs:K,elements:U,floatingStyles:Q}),[f,H,K,U,Q])}const $b=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?tp({element:r.current,padding:o}).fn(n):{}:r?tp({element:r,padding:o}).fn(n):{}}}},Ub=(e,t)=>({..._b(e),options:[e,t]}),Bb=(e,t)=>({...Ob(e),options:[e,t]}),Vb=(e,t)=>({...Lb(e),options:[e,t]}),Wb=(e,t)=>({...Db(e),options:[e,t]}),Hb=(e,t)=>({...Ib(e),options:[e,t]}),Kb=(e,t)=>({...Mb(e),options:[e,t]}),Qb=(e,t)=>({...$b(e),options:[e,t]});var Gb="Arrow",Jg=h.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...s}=e;return l.jsx(ye.svg,{...s,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:l.jsx("polygon",{points:"0,0 30,0 15,10"})})});Jg.displayName=Gb;var Yb=Jg;function Xb(e,t=[]){let n=[];function r(s,i){const a=h.createContext(i),c=n.length;n=[...n,i];function u(p){const{scope:d,children:x,...b}=p,g=(d==null?void 0:d[e][c])||a,S=h.useMemo(()=>b,Object.values(b));return l.jsx(g.Provider,{value:S,children:x})}function f(p,d){const x=(d==null?void 0:d[e][c])||a,b=h.useContext(x);if(b)return b;if(i!==void 0)return i;throw new Error(`\`${p}\` must be used within \`${s}\``)}return u.displayName=s+"Provider",[u,f]}const o=()=>{const s=n.map(i=>h.createContext(i));return function(a){const c=(a==null?void 0:a[e])||s;return h.useMemo(()=>({[`__scope${e}`]:{...a,[e]:c}}),[a,c])}};return o.scopeName=e,[r,qb(o,...t)]}function qb(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((a,{useScope:c,scopeName:u})=>{const p=c(s)[`__scope${u}`];return{...a,...p}},{});return h.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}function Zb(e){const[t,n]=h.useState(void 0);return $n(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const s=o[0];let i,a;if("borderBoxSize"in s){const c=s.borderBoxSize,u=Array.isArray(c)?c[0]:c;i=u.inlineSize,a=u.blockSize}else i=e.offsetWidth,a=e.offsetHeight;n({width:i,height:a})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var ad="Popper",[ev,tv]=Xb(ad),[Jb,nv]=ev(ad),rv=e=>{const{__scopePopper:t,children:n}=e,[r,o]=h.useState(null);return l.jsx(Jb,{scope:t,anchor:r,onAnchorChange:o,children:n})};rv.displayName=ad;var ov="PopperAnchor",sv=h.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,s=nv(ov,n),i=h.useRef(null),a=Re(t,i);return h.useEffect(()=>{s.onAnchorChange((r==null?void 0:r.current)||i.current)}),r?null:l.jsx(ye.div,{...o,ref:a})});sv.displayName=ov;var ld="PopperContent",[eE,tE]=ev(ld),iv=h.forwardRef((e,t)=>{var yt,an,ln,cn,Ms,Sr;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:s="center",alignOffset:i=0,arrowPadding:a=0,avoidCollisions:c=!0,collisionBoundary:u=[],collisionPadding:f=0,sticky:p="partial",hideWhenDetached:d=!1,updatePositionStrategy:x="optimized",onPlaced:b,...g}=e,S=nv(ld,n),[v,m]=h.useState(null),y=Re(t,Yn=>m(Yn)),[w,E]=h.useState(null),C=Zb(w),k=(C==null?void 0:C.width)??0,j=(C==null?void 0:C.height)??0,O=r+(s!=="center"?"-"+s:""),_=typeof f=="number"?f:{top:0,right:0,bottom:0,left:0,...f},F=Array.isArray(u)?u:[u],M=F.length>0,H={padding:_,boundary:F.filter(rE),altBoundary:M},{refs:D,floatingStyles:K,placement:U,isPositioned:Q,middlewareData:N}=Fb({strategy:"fixed",placement:O,whileElementsMounted:(...Yn)=>Ab(...Yn,{animationFrame:x==="always"}),elements:{reference:S.anchor},middleware:[Ub({mainAxis:o+j,alignmentAxis:i}),c&&Bb({mainAxis:!0,crossAxis:!1,limiter:p==="partial"?Vb():void 0,...H}),c&&Wb({...H}),Hb({...H,apply:({elements:Yn,rects:Ro,availableWidth:Ls,availableHeight:un})=>{const{width:Ka,height:Qa}=Ro.reference,Ce=Yn.floating.style;Ce.setProperty("--radix-popper-available-width",`${Ls}px`),Ce.setProperty("--radix-popper-available-height",`${un}px`),Ce.setProperty("--radix-popper-anchor-width",`${Ka}px`),Ce.setProperty("--radix-popper-anchor-height",`${Qa}px`)}}),w&&Qb({element:w,padding:a}),oE({arrowWidth:k,arrowHeight:j}),d&&Kb({strategy:"referenceHidden",...H})]}),[R,z]=cv(U),L=vt(b);$n(()=>{Q&&(L==null||L())},[Q,L]);const $=(yt=N.arrow)==null?void 0:yt.x,Y=(an=N.arrow)==null?void 0:an.y,de=((ln=N.arrow)==null?void 0:ln.centerOffset)!==0,[qe,J]=h.useState();return $n(()=>{v&&J(window.getComputedStyle(v).zIndex)},[v]),l.jsx("div",{ref:D.setFloating,"data-radix-popper-content-wrapper":"",style:{...K,transform:Q?K.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:qe,"--radix-popper-transform-origin":[(cn=N.transformOrigin)==null?void 0:cn.x,(Ms=N.transformOrigin)==null?void 0:Ms.y].join(" "),...((Sr=N.hide)==null?void 0:Sr.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:l.jsx(eE,{scope:n,placedSide:R,onArrowChange:E,arrowX:$,arrowY:Y,shouldHideArrow:de,children:l.jsx(ye.div,{"data-side":R,"data-align":z,...g,ref:y,style:{...g.style,animation:Q?void 0:"none"}})})})});iv.displayName=ld;var av="PopperArrow",nE={top:"bottom",right:"left",bottom:"top",left:"right"},lv=h.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,s=tE(av,r),i=nE[s.placedSide];return l.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:l.jsx(Yb,{...o,ref:n,style:{...o.style,display:"block"}})})});lv.displayName=av;function rE(e){return e!==null}var oE=e=>({name:"transformOrigin",options:e,fn(t){var S,v,m;const{placement:n,rects:r,middlewareData:o}=t,i=((S=o.arrow)==null?void 0:S.centerOffset)!==0,a=i?0:e.arrowWidth,c=i?0:e.arrowHeight,[u,f]=cv(n),p={start:"0%",center:"50%",end:"100%"}[f],d=(((v=o.arrow)==null?void 0:v.x)??0)+a/2,x=(((m=o.arrow)==null?void 0:m.y)??0)+c/2;let b="",g="";return u==="bottom"?(b=i?p:`${d}px`,g=`${-c}px`):u==="top"?(b=i?p:`${d}px`,g=`${r.floating.height+c}px`):u==="right"?(b=`${-c}px`,g=i?p:`${x}px`):u==="left"&&(b=`${r.floating.width+c}px`,g=i?p:`${x}px`),{data:{x:b,y:g}}}});function cv(e){const[t,n="center"]=e.split("-");return[t,n]}var sE=rv,iE=sv,aE=iv,lE=lv,[za,Q2]=ja("Tooltip",[tv]),Fa=tv(),uv="TooltipProvider",cE=700,Bc="tooltip.open",[uE,cd]=za(uv),dv=e=>{const{__scopeTooltip:t,delayDuration:n=cE,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:s}=e,[i,a]=h.useState(!0),c=h.useRef(!1),u=h.useRef(0);return h.useEffect(()=>{const f=u.current;return()=>window.clearTimeout(f)},[]),l.jsx(uE,{scope:t,isOpenDelayed:i,delayDuration:n,onOpen:h.useCallback(()=>{window.clearTimeout(u.current),a(!1)},[]),onClose:h.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a(!0),r)},[r]),isPointerInTransitRef:c,onPointerInTransitChange:h.useCallback(f=>{c.current=f},[]),disableHoverableContent:o,children:s})};dv.displayName=uv;var $a="Tooltip",[dE,Ua]=za($a),fv=e=>{const{__scopeTooltip:t,children:n,open:r,defaultOpen:o=!1,onOpenChange:s,disableHoverableContent:i,delayDuration:a}=e,c=cd($a,e.__scopeTooltip),u=Fa(t),[f,p]=h.useState(null),d=Pi(),x=h.useRef(0),b=i??c.disableHoverableContent,g=a??c.delayDuration,S=h.useRef(!1),[v=!1,m]=Xu({prop:r,defaultProp:o,onChange:k=>{k?(c.onOpen(),document.dispatchEvent(new CustomEvent(Bc))):c.onClose(),s==null||s(k)}}),y=h.useMemo(()=>v?S.current?"delayed-open":"instant-open":"closed",[v]),w=h.useCallback(()=>{window.clearTimeout(x.current),x.current=0,S.current=!1,m(!0)},[m]),E=h.useCallback(()=>{window.clearTimeout(x.current),x.current=0,m(!1)},[m]),C=h.useCallback(()=>{window.clearTimeout(x.current),x.current=window.setTimeout(()=>{S.current=!0,m(!0),x.current=0},g)},[g,m]);return h.useEffect(()=>()=>{x.current&&(window.clearTimeout(x.current),x.current=0)},[]),l.jsx(sE,{...u,children:l.jsx(dE,{scope:t,contentId:d,open:v,stateAttribute:y,trigger:f,onTriggerChange:p,onTriggerEnter:h.useCallback(()=>{c.isOpenDelayed?C():w()},[c.isOpenDelayed,C,w]),onTriggerLeave:h.useCallback(()=>{b?E():(window.clearTimeout(x.current),x.current=0)},[E,b]),onOpen:w,onClose:E,disableHoverableContent:b,children:n})})};fv.displayName=$a;var Vc="TooltipTrigger",pv=h.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=Ua(Vc,n),s=cd(Vc,n),i=Fa(n),a=h.useRef(null),c=Re(t,a,o.onTriggerChange),u=h.useRef(!1),f=h.useRef(!1),p=h.useCallback(()=>u.current=!1,[]);return h.useEffect(()=>()=>document.removeEventListener("pointerup",p),[p]),l.jsx(iE,{asChild:!0,...i,children:l.jsx(ye.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:c,onPointerMove:ae(e.onPointerMove,d=>{d.pointerType!=="touch"&&!f.current&&!s.isPointerInTransitRef.current&&(o.onTriggerEnter(),f.current=!0)}),onPointerLeave:ae(e.onPointerLeave,()=>{o.onTriggerLeave(),f.current=!1}),onPointerDown:ae(e.onPointerDown,()=>{u.current=!0,document.addEventListener("pointerup",p,{once:!0})}),onFocus:ae(e.onFocus,()=>{u.current||o.onOpen()}),onBlur:ae(e.onBlur,o.onClose),onClick:ae(e.onClick,o.onClose)})})});pv.displayName=Vc;var fE="TooltipPortal",[G2,pE]=za(fE,{forceMount:void 0}),xo="TooltipContent",hv=h.forwardRef((e,t)=>{const n=pE(xo,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...s}=e,i=Ua(xo,e.__scopeTooltip);return l.jsx(ko,{present:r||i.open,children:i.disableHoverableContent?l.jsx(mv,{side:o,...s,ref:t}):l.jsx(hE,{side:o,...s,ref:t})})}),hE=h.forwardRef((e,t)=>{const n=Ua(xo,e.__scopeTooltip),r=cd(xo,e.__scopeTooltip),o=h.useRef(null),s=Re(t,o),[i,a]=h.useState(null),{trigger:c,onClose:u}=n,f=o.current,{onPointerInTransitChange:p}=r,d=h.useCallback(()=>{a(null),p(!1)},[p]),x=h.useCallback((b,g)=>{const S=b.currentTarget,v={x:b.clientX,y:b.clientY},m=yE(v,S.getBoundingClientRect()),y=xE(v,m),w=wE(g.getBoundingClientRect()),E=bE([...y,...w]);a(E),p(!0)},[p]);return h.useEffect(()=>()=>d(),[d]),h.useEffect(()=>{if(c&&f){const b=S=>x(S,f),g=S=>x(S,c);return c.addEventListener("pointerleave",b),f.addEventListener("pointerleave",g),()=>{c.removeEventListener("pointerleave",b),f.removeEventListener("pointerleave",g)}}},[c,f,x,d]),h.useEffect(()=>{if(i){const b=g=>{const S=g.target,v={x:g.clientX,y:g.clientY},m=(c==null?void 0:c.contains(S))||(f==null?void 0:f.contains(S)),y=!SE(v,i);m?d():y&&(d(),u())};return document.addEventListener("pointermove",b),()=>document.removeEventListener("pointermove",b)}},[c,f,i,u,d]),l.jsx(mv,{...e,ref:s})}),[mE,gE]=za($a,{isInside:!1}),mv=h.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:s,onPointerDownOutside:i,...a}=e,c=Ua(xo,n),u=Fa(n),{onClose:f}=c;return h.useEffect(()=>(document.addEventListener(Bc,f),()=>document.removeEventListener(Bc,f)),[f]),h.useEffect(()=>{if(c.trigger){const p=d=>{const x=d.target;x!=null&&x.contains(c.trigger)&&f()};return window.addEventListener("scroll",p,{capture:!0}),()=>window.removeEventListener("scroll",p,{capture:!0})}},[c.trigger,f]),l.jsx(Ta,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:s,onPointerDownOutside:i,onFocusOutside:p=>p.preventDefault(),onDismiss:f,children:l.jsxs(aE,{"data-state":c.stateAttribute,...u,...a,ref:t,style:{...a.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[l.jsx(Gu,{children:r}),l.jsx(mE,{scope:n,isInside:!0,children:l.jsx(e1,{id:c.contentId,role:"tooltip",children:o||r})})]})})});hv.displayName=xo;var gv="TooltipArrow",vE=h.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=Fa(n);return gE(gv,n).isInside?null:l.jsx(lE,{...o,...r,ref:t})});vE.displayName=gv;function yE(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),s=Math.abs(t.left-e.x);switch(Math.min(n,r,o,s)){case s:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function xE(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function wE(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function SE(e,t){const{x:n,y:r}=e;let o=!1;for(let s=0,i=t.length-1;s<t.length;i=s++){const a=t[s].x,c=t[s].y,u=t[i].x,f=t[i].y;c>r!=f>r&&n<(u-a)*(r-c)/(f-c)+a&&(o=!o)}return o}function bE(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),EE(t)}function EE(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const s=t[t.length-1],i=t[t.length-2];if((s.x-i.x)*(o.y-i.y)>=(s.y-i.y)*(o.x-i.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const s=n[n.length-1],i=n[n.length-2];if((s.x-i.x)*(o.y-i.y)>=(s.y-i.y)*(o.x-i.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var kE=dv,CE=fv,NE=pv,vv=hv;const yv=kE,li=CE,ci=NE,Ho=h.forwardRef(({className:e,sideOffset:t=4,...n},r)=>l.jsx(vv,{ref:r,sideOffset:t,className:ce("z-50 overflow-hidden rounded-lg bg-gray-900 px-3 py-2 text-xs text-white shadow-lg animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 font-inter font-medium",e),...n}));Ho.displayName=vv.displayName;var Ba=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Va=typeof window>"u"||"Deno"in globalThis;function St(){}function jE(e,t){return typeof e=="function"?e(t):e}function TE(e){return typeof e=="number"&&e>=0&&e!==1/0}function PE(e,t){return Math.max(e+(t||0)-Date.now(),0)}function rp(e,t){return typeof e=="function"?e(t):e}function RE(e,t){return typeof e=="function"?e(t):e}function op(e,t){const{type:n="all",exact:r,fetchStatus:o,predicate:s,queryKey:i,stale:a}=e;if(i){if(r){if(t.queryHash!==ud(i,t.options))return!1}else if(!Es(t.queryKey,i))return!1}if(n!=="all"){const c=t.isActive();if(n==="active"&&!c||n==="inactive"&&c)return!1}return!(typeof a=="boolean"&&t.isStale()!==a||o&&o!==t.state.fetchStatus||s&&!s(t))}function sp(e,t){const{exact:n,status:r,predicate:o,mutationKey:s}=e;if(s){if(!t.options.mutationKey)return!1;if(n){if(bs(t.options.mutationKey)!==bs(s))return!1}else if(!Es(t.options.mutationKey,s))return!1}return!(r&&t.state.status!==r||o&&!o(t))}function ud(e,t){return((t==null?void 0:t.queryKeyHashFn)||bs)(e)}function bs(e){return JSON.stringify(e,(t,n)=>Wc(n)?Object.keys(n).sort().reduce((r,o)=>(r[o]=n[o],r),{}):n)}function Es(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?!Object.keys(t).some(n=>!Es(e[n],t[n])):!1}function xv(e,t){if(e===t)return e;const n=ip(e)&&ip(t);if(n||Wc(e)&&Wc(t)){const r=n?e:Object.keys(e),o=r.length,s=n?t:Object.keys(t),i=s.length,a=n?[]:{};let c=0;for(let u=0;u<i;u++){const f=n?u:s[u];(!n&&r.includes(f)||n)&&e[f]===void 0&&t[f]===void 0?(a[f]=void 0,c++):(a[f]=xv(e[f],t[f]),a[f]===e[f]&&e[f]!==void 0&&c++)}return o===i&&c===o?e:a}return t}function ip(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function Wc(e){if(!ap(e))return!1;const t=e.constructor;if(t===void 0)return!0;const n=t.prototype;return!(!ap(n)||!n.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function ap(e){return Object.prototype.toString.call(e)==="[object Object]"}function AE(e){return new Promise(t=>{setTimeout(t,e)})}function _E(e,t,n){return typeof n.structuralSharing=="function"?n.structuralSharing(e,t):n.structuralSharing!==!1?xv(e,t):t}function OE(e,t,n=0){const r=[...e,t];return n&&r.length>n?r.slice(1):r}function DE(e,t,n=0){const r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var dd=Symbol();function wv(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===dd?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var tr,Sn,eo,Op,IE=(Op=class extends Ba{constructor(){super();q(this,tr);q(this,Sn);q(this,eo);W(this,eo,t=>{if(!Va&&window.addEventListener){const n=()=>t();return window.addEventListener("visibilitychange",n,!1),()=>{window.removeEventListener("visibilitychange",n)}}})}onSubscribe(){T(this,Sn)||this.setEventListener(T(this,eo))}onUnsubscribe(){var t;this.hasListeners()||((t=T(this,Sn))==null||t.call(this),W(this,Sn,void 0))}setEventListener(t){var n;W(this,eo,t),(n=T(this,Sn))==null||n.call(this),W(this,Sn,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){T(this,tr)!==t&&(W(this,tr,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(n=>{n(t)})}isFocused(){var t;return typeof T(this,tr)=="boolean"?T(this,tr):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},tr=new WeakMap,Sn=new WeakMap,eo=new WeakMap,Op),Sv=new IE,to,bn,no,Dp,ME=(Dp=class extends Ba{constructor(){super();q(this,to,!0);q(this,bn);q(this,no);W(this,no,t=>{if(!Va&&window.addEventListener){const n=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",n,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",n),window.removeEventListener("offline",r)}}})}onSubscribe(){T(this,bn)||this.setEventListener(T(this,no))}onUnsubscribe(){var t;this.hasListeners()||((t=T(this,bn))==null||t.call(this),W(this,bn,void 0))}setEventListener(t){var n;W(this,no,t),(n=T(this,bn))==null||n.call(this),W(this,bn,t(this.setOnline.bind(this)))}setOnline(t){T(this,to)!==t&&(W(this,to,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return T(this,to)}},to=new WeakMap,bn=new WeakMap,no=new WeakMap,Dp),aa=new ME;function LE(){let e,t;const n=new Promise((o,s)=>{e=o,t=s});n.status="pending",n.catch(()=>{});function r(o){Object.assign(n,o),delete n.resolve,delete n.reject}return n.resolve=o=>{r({status:"fulfilled",value:o}),e(o)},n.reject=o=>{r({status:"rejected",reason:o}),t(o)},n}function zE(e){return Math.min(1e3*2**e,3e4)}function bv(e){return(e??"online")==="online"?aa.isOnline():!0}var Ev=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function Rl(e){return e instanceof Ev}function kv(e){let t=!1,n=0,r=!1,o;const s=LE(),i=g=>{var S;r||(d(new Ev(g)),(S=e.abort)==null||S.call(e))},a=()=>{t=!0},c=()=>{t=!1},u=()=>Sv.isFocused()&&(e.networkMode==="always"||aa.isOnline())&&e.canRun(),f=()=>bv(e.networkMode)&&e.canRun(),p=g=>{var S;r||(r=!0,(S=e.onSuccess)==null||S.call(e,g),o==null||o(),s.resolve(g))},d=g=>{var S;r||(r=!0,(S=e.onError)==null||S.call(e,g),o==null||o(),s.reject(g))},x=()=>new Promise(g=>{var S;o=v=>{(r||u())&&g(v)},(S=e.onPause)==null||S.call(e)}).then(()=>{var g;o=void 0,r||(g=e.onContinue)==null||g.call(e)}),b=()=>{if(r)return;let g;const S=n===0?e.initialPromise:void 0;try{g=S??e.fn()}catch(v){g=Promise.reject(v)}Promise.resolve(g).then(p).catch(v=>{var C;if(r)return;const m=e.retry??(Va?0:3),y=e.retryDelay??zE,w=typeof y=="function"?y(n,v):y,E=m===!0||typeof m=="number"&&n<m||typeof m=="function"&&m(n,v);if(t||!E){d(v);return}n++,(C=e.onFail)==null||C.call(e,n,v),AE(w).then(()=>u()?void 0:x()).then(()=>{t?d(v):b()})})};return{promise:s,cancel:i,continue:()=>(o==null||o(),s),cancelRetry:a,continueRetry:c,canStart:f,start:()=>(f()?b():x().then(b),s)}}function FE(){let e=[],t=0,n=a=>{a()},r=a=>{a()},o=a=>setTimeout(a,0);const s=a=>{t?e.push(a):o(()=>{n(a)})},i=()=>{const a=e;e=[],a.length&&o(()=>{r(()=>{a.forEach(c=>{n(c)})})})};return{batch:a=>{let c;t++;try{c=a()}finally{t--,t||i()}return c},batchCalls:a=>(...c)=>{s(()=>{a(...c)})},schedule:s,setNotifyFunction:a=>{n=a},setBatchNotifyFunction:a=>{r=a},setScheduler:a=>{o=a}}}var Ue=FE(),nr,Ip,Cv=(Ip=class{constructor(){q(this,nr)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),TE(this.gcTime)&&W(this,nr,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(Va?1/0:5*60*1e3))}clearGcTimeout(){T(this,nr)&&(clearTimeout(T(this,nr)),W(this,nr,void 0))}},nr=new WeakMap,Ip),ro,oo,ct,De,Ns,rr,bt,Kt,Mp,$E=(Mp=class extends Cv{constructor(t){super();q(this,bt);q(this,ro);q(this,oo);q(this,ct);q(this,De);q(this,Ns);q(this,rr);W(this,rr,!1),W(this,Ns,t.defaultOptions),this.setOptions(t.options),this.observers=[],W(this,ct,t.cache),this.queryKey=t.queryKey,this.queryHash=t.queryHash,W(this,ro,BE(this.options)),this.state=t.state??T(this,ro),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=T(this,De))==null?void 0:t.promise}setOptions(t){this.options={...T(this,Ns),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&T(this,ct).remove(this)}setData(t,n){const r=_E(this.state.data,t,this.options);return Ae(this,bt,Kt).call(this,{data:r,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt,manual:n==null?void 0:n.manual}),r}setState(t,n){Ae(this,bt,Kt).call(this,{type:"setState",state:t,setStateOptions:n})}cancel(t){var r,o;const n=(r=T(this,De))==null?void 0:r.promise;return(o=T(this,De))==null||o.cancel(t),n?n.then(St).catch(St):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(T(this,ro))}isActive(){return this.observers.some(t=>RE(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===dd||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return this.state.isInvalidated?!0:this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0}isStaleByTime(t=0){return this.state.isInvalidated||this.state.data===void 0||!PE(this.state.dataUpdatedAt,t)}onFocus(){var n;const t=this.observers.find(r=>r.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(n=T(this,De))==null||n.continue()}onOnline(){var n;const t=this.observers.find(r=>r.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(n=T(this,De))==null||n.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),T(this,ct).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(n=>n!==t),this.observers.length||(T(this,De)&&(T(this,rr)?T(this,De).cancel({revert:!0}):T(this,De).cancelRetry()),this.scheduleGc()),T(this,ct).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||Ae(this,bt,Kt).call(this,{type:"invalidate"})}fetch(t,n){var c,u,f;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if(T(this,De))return T(this,De).continueRetry(),T(this,De).promise}if(t&&this.setOptions(t),!this.options.queryFn){const p=this.observers.find(d=>d.options.queryFn);p&&this.setOptions(p.options)}const r=new AbortController,o=p=>{Object.defineProperty(p,"signal",{enumerable:!0,get:()=>(W(this,rr,!0),r.signal)})},s=()=>{const p=wv(this.options,n),d={queryKey:this.queryKey,meta:this.meta};return o(d),W(this,rr,!1),this.options.persister?this.options.persister(p,d,this):p(d)},i={fetchOptions:n,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:s};o(i),(c=this.options.behavior)==null||c.onFetch(i,this),W(this,oo,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((u=i.fetchOptions)==null?void 0:u.meta))&&Ae(this,bt,Kt).call(this,{type:"fetch",meta:(f=i.fetchOptions)==null?void 0:f.meta});const a=p=>{var d,x,b,g;Rl(p)&&p.silent||Ae(this,bt,Kt).call(this,{type:"error",error:p}),Rl(p)||((x=(d=T(this,ct).config).onError)==null||x.call(d,p,this),(g=(b=T(this,ct).config).onSettled)==null||g.call(b,this.state.data,p,this)),this.scheduleGc()};return W(this,De,kv({initialPromise:n==null?void 0:n.initialPromise,fn:i.fetchFn,abort:r.abort.bind(r),onSuccess:p=>{var d,x,b,g;if(p===void 0){a(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(p)}catch(S){a(S);return}(x=(d=T(this,ct).config).onSuccess)==null||x.call(d,p,this),(g=(b=T(this,ct).config).onSettled)==null||g.call(b,p,this.state.error,this),this.scheduleGc()},onError:a,onFail:(p,d)=>{Ae(this,bt,Kt).call(this,{type:"failed",failureCount:p,error:d})},onPause:()=>{Ae(this,bt,Kt).call(this,{type:"pause"})},onContinue:()=>{Ae(this,bt,Kt).call(this,{type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0})),T(this,De).start()}},ro=new WeakMap,oo=new WeakMap,ct=new WeakMap,De=new WeakMap,Ns=new WeakMap,rr=new WeakMap,bt=new WeakSet,Kt=function(t){const n=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...UE(r.data,this.options),fetchMeta:t.meta??null};case"success":return{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const o=t.error;return Rl(o)&&o.revert&&T(this,oo)?{...T(this,oo),fetchStatus:"idle"}:{...r,error:o,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:o,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=n(this.state),Ue.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),T(this,ct).notify({query:this,type:"updated",action:t})})},Mp);function UE(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:bv(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function BE(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,n=t!==void 0,r=n?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}var Mt,Lp,VE=(Lp=class extends Ba{constructor(t={}){super();q(this,Mt);this.config=t,W(this,Mt,new Map)}build(t,n,r){const o=n.queryKey,s=n.queryHash??ud(o,n);let i=this.get(s);return i||(i=new $E({cache:this,queryKey:o,queryHash:s,options:t.defaultQueryOptions(n),state:r,defaultOptions:t.getQueryDefaults(o)}),this.add(i)),i}add(t){T(this,Mt).has(t.queryHash)||(T(this,Mt).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const n=T(this,Mt).get(t.queryHash);n&&(t.destroy(),n===t&&T(this,Mt).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){Ue.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return T(this,Mt).get(t)}getAll(){return[...T(this,Mt).values()]}find(t){const n={exact:!0,...t};return this.getAll().find(r=>op(n,r))}findAll(t={}){const n=this.getAll();return Object.keys(t).length>0?n.filter(r=>op(t,r)):n}notify(t){Ue.batch(()=>{this.listeners.forEach(n=>{n(t)})})}onFocus(){Ue.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){Ue.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},Mt=new WeakMap,Lp),Lt,Fe,or,zt,mn,zp,WE=(zp=class extends Cv{constructor(t){super();q(this,zt);q(this,Lt);q(this,Fe);q(this,or);this.mutationId=t.mutationId,W(this,Fe,t.mutationCache),W(this,Lt,[]),this.state=t.state||HE(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){T(this,Lt).includes(t)||(T(this,Lt).push(t),this.clearGcTimeout(),T(this,Fe).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){W(this,Lt,T(this,Lt).filter(n=>n!==t)),this.scheduleGc(),T(this,Fe).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){T(this,Lt).length||(this.state.status==="pending"?this.scheduleGc():T(this,Fe).remove(this))}continue(){var t;return((t=T(this,or))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var o,s,i,a,c,u,f,p,d,x,b,g,S,v,m,y,w,E,C,k;W(this,or,kv({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(j,O)=>{Ae(this,zt,mn).call(this,{type:"failed",failureCount:j,error:O})},onPause:()=>{Ae(this,zt,mn).call(this,{type:"pause"})},onContinue:()=>{Ae(this,zt,mn).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>T(this,Fe).canRun(this)}));const n=this.state.status==="pending",r=!T(this,or).canStart();try{if(!n){Ae(this,zt,mn).call(this,{type:"pending",variables:t,isPaused:r}),await((s=(o=T(this,Fe).config).onMutate)==null?void 0:s.call(o,t,this));const O=await((a=(i=this.options).onMutate)==null?void 0:a.call(i,t));O!==this.state.context&&Ae(this,zt,mn).call(this,{type:"pending",context:O,variables:t,isPaused:r})}const j=await T(this,or).start();return await((u=(c=T(this,Fe).config).onSuccess)==null?void 0:u.call(c,j,t,this.state.context,this)),await((p=(f=this.options).onSuccess)==null?void 0:p.call(f,j,t,this.state.context)),await((x=(d=T(this,Fe).config).onSettled)==null?void 0:x.call(d,j,null,this.state.variables,this.state.context,this)),await((g=(b=this.options).onSettled)==null?void 0:g.call(b,j,null,t,this.state.context)),Ae(this,zt,mn).call(this,{type:"success",data:j}),j}catch(j){try{throw await((v=(S=T(this,Fe).config).onError)==null?void 0:v.call(S,j,t,this.state.context,this)),await((y=(m=this.options).onError)==null?void 0:y.call(m,j,t,this.state.context)),await((E=(w=T(this,Fe).config).onSettled)==null?void 0:E.call(w,void 0,j,this.state.variables,this.state.context,this)),await((k=(C=this.options).onSettled)==null?void 0:k.call(C,void 0,j,t,this.state.context)),j}finally{Ae(this,zt,mn).call(this,{type:"error",error:j})}}finally{T(this,Fe).runNext(this)}}},Lt=new WeakMap,Fe=new WeakMap,or=new WeakMap,zt=new WeakSet,mn=function(t){const n=r=>{switch(t.type){case"failed":return{...r,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...r,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=n(this.state),Ue.batch(()=>{T(this,Lt).forEach(r=>{r.onMutationUpdate(t)}),T(this,Fe).notify({mutation:this,type:"updated",action:t})})},zp);function HE(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var Ze,js,Fp,KE=(Fp=class extends Ba{constructor(t={}){super();q(this,Ze);q(this,js);this.config=t,W(this,Ze,new Map),W(this,js,Date.now())}build(t,n,r){const o=new WE({mutationCache:this,mutationId:++$s(this,js)._,options:t.defaultMutationOptions(n),state:r});return this.add(o),o}add(t){const n=ui(t),r=T(this,Ze).get(n)??[];r.push(t),T(this,Ze).set(n,r),this.notify({type:"added",mutation:t})}remove(t){var r;const n=ui(t);if(T(this,Ze).has(n)){const o=(r=T(this,Ze).get(n))==null?void 0:r.filter(s=>s!==t);o&&(o.length===0?T(this,Ze).delete(n):T(this,Ze).set(n,o))}this.notify({type:"removed",mutation:t})}canRun(t){var r;const n=(r=T(this,Ze).get(ui(t)))==null?void 0:r.find(o=>o.state.status==="pending");return!n||n===t}runNext(t){var r;const n=(r=T(this,Ze).get(ui(t)))==null?void 0:r.find(o=>o!==t&&o.state.isPaused);return(n==null?void 0:n.continue())??Promise.resolve()}clear(){Ue.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}getAll(){return[...T(this,Ze).values()].flat()}find(t){const n={exact:!0,...t};return this.getAll().find(r=>sp(n,r))}findAll(t={}){return this.getAll().filter(n=>sp(t,n))}notify(t){Ue.batch(()=>{this.listeners.forEach(n=>{n(t)})})}resumePausedMutations(){const t=this.getAll().filter(n=>n.state.isPaused);return Ue.batch(()=>Promise.all(t.map(n=>n.continue().catch(St))))}},Ze=new WeakMap,js=new WeakMap,Fp);function ui(e){var t;return((t=e.options.scope)==null?void 0:t.id)??String(e.mutationId)}function lp(e){return{onFetch:(t,n)=>{var f,p,d,x,b;const r=t.options,o=(d=(p=(f=t.fetchOptions)==null?void 0:f.meta)==null?void 0:p.fetchMore)==null?void 0:d.direction,s=((x=t.state.data)==null?void 0:x.pages)||[],i=((b=t.state.data)==null?void 0:b.pageParams)||[];let a={pages:[],pageParams:[]},c=0;const u=async()=>{let g=!1;const S=y=>{Object.defineProperty(y,"signal",{enumerable:!0,get:()=>(t.signal.aborted?g=!0:t.signal.addEventListener("abort",()=>{g=!0}),t.signal)})},v=wv(t.options,t.fetchOptions),m=async(y,w,E)=>{if(g)return Promise.reject();if(w==null&&y.pages.length)return Promise.resolve(y);const C={queryKey:t.queryKey,pageParam:w,direction:E?"backward":"forward",meta:t.options.meta};S(C);const k=await v(C),{maxPages:j}=t.options,O=E?DE:OE;return{pages:O(y.pages,k,j),pageParams:O(y.pageParams,w,j)}};if(o&&s.length){const y=o==="backward",w=y?QE:cp,E={pages:s,pageParams:i},C=w(r,E);a=await m(E,C,y)}else{const y=e??s.length;do{const w=c===0?i[0]??r.initialPageParam:cp(r,a);if(c>0&&w==null)break;a=await m(a,w),c++}while(c<y)}return a};t.options.persister?t.fetchFn=()=>{var g,S;return(S=(g=t.options).persister)==null?void 0:S.call(g,u,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n)}:t.fetchFn=u}}}function cp(e,{pages:t,pageParams:n}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,n[r],n):void 0}function QE(e,{pages:t,pageParams:n}){var r;return t.length>0?(r=e.getPreviousPageParam)==null?void 0:r.call(e,t[0],t,n[0],n):void 0}var me,En,kn,so,io,Cn,ao,lo,$p,GE=($p=class{constructor(e={}){q(this,me);q(this,En);q(this,kn);q(this,so);q(this,io);q(this,Cn);q(this,ao);q(this,lo);W(this,me,e.queryCache||new VE),W(this,En,e.mutationCache||new KE),W(this,kn,e.defaultOptions||{}),W(this,so,new Map),W(this,io,new Map),W(this,Cn,0)}mount(){$s(this,Cn)._++,T(this,Cn)===1&&(W(this,ao,Sv.subscribe(async e=>{e&&(await this.resumePausedMutations(),T(this,me).onFocus())})),W(this,lo,aa.subscribe(async e=>{e&&(await this.resumePausedMutations(),T(this,me).onOnline())})))}unmount(){var e,t;$s(this,Cn)._--,T(this,Cn)===0&&((e=T(this,ao))==null||e.call(this),W(this,ao,void 0),(t=T(this,lo))==null||t.call(this),W(this,lo,void 0))}isFetching(e){return T(this,me).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return T(this,En).findAll({...e,status:"pending"}).length}getQueryData(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=T(this,me).get(t.queryHash))==null?void 0:n.state.data}ensureQueryData(e){const t=this.getQueryData(e.queryKey);if(t===void 0)return this.fetchQuery(e);{const n=this.defaultQueryOptions(e),r=T(this,me).build(this,n);return e.revalidateIfStale&&r.isStaleByTime(rp(n.staleTime,r))&&this.prefetchQuery(n),Promise.resolve(t)}}getQueriesData(e){return T(this,me).findAll(e).map(({queryKey:t,state:n})=>{const r=n.data;return[t,r]})}setQueryData(e,t,n){const r=this.defaultQueryOptions({queryKey:e}),o=T(this,me).get(r.queryHash),s=o==null?void 0:o.state.data,i=jE(t,s);if(i!==void 0)return T(this,me).build(this,r).setData(i,{...n,manual:!0})}setQueriesData(e,t,n){return Ue.batch(()=>T(this,me).findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,n)]))}getQueryState(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=T(this,me).get(t.queryHash))==null?void 0:n.state}removeQueries(e){const t=T(this,me);Ue.batch(()=>{t.findAll(e).forEach(n=>{t.remove(n)})})}resetQueries(e,t){const n=T(this,me),r={type:"active",...e};return Ue.batch(()=>(n.findAll(e).forEach(o=>{o.reset()}),this.refetchQueries(r,t)))}cancelQueries(e={},t={}){const n={revert:!0,...t},r=Ue.batch(()=>T(this,me).findAll(e).map(o=>o.cancel(n)));return Promise.all(r).then(St).catch(St)}invalidateQueries(e={},t={}){return Ue.batch(()=>{if(T(this,me).findAll(e).forEach(r=>{r.invalidate()}),e.refetchType==="none")return Promise.resolve();const n={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(n,t)})}refetchQueries(e={},t){const n={...t,cancelRefetch:(t==null?void 0:t.cancelRefetch)??!0},r=Ue.batch(()=>T(this,me).findAll(e).filter(o=>!o.isDisabled()).map(o=>{let s=o.fetch(void 0,n);return n.throwOnError||(s=s.catch(St)),o.state.fetchStatus==="paused"?Promise.resolve():s}));return Promise.all(r).then(St)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const n=T(this,me).build(this,t);return n.isStaleByTime(rp(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(St).catch(St)}fetchInfiniteQuery(e){return e.behavior=lp(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(St).catch(St)}ensureInfiniteQueryData(e){return e.behavior=lp(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return aa.isOnline()?T(this,En).resumePausedMutations():Promise.resolve()}getQueryCache(){return T(this,me)}getMutationCache(){return T(this,En)}getDefaultOptions(){return T(this,kn)}setDefaultOptions(e){W(this,kn,e)}setQueryDefaults(e,t){T(this,so).set(bs(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...T(this,so).values()];let n={};return t.forEach(r=>{Es(e,r.queryKey)&&(n={...n,...r.defaultOptions})}),n}setMutationDefaults(e,t){T(this,io).set(bs(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...T(this,io).values()];let n={};return t.forEach(r=>{Es(e,r.mutationKey)&&(n={...n,...r.defaultOptions})}),n}defaultQueryOptions(e){if(e._defaulted)return e;const t={...T(this,kn).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=ud(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.enabled!==!0&&t.queryFn===dd&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...T(this,kn).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){T(this,me).clear(),T(this,En).clear()}},me=new WeakMap,En=new WeakMap,kn=new WeakMap,so=new WeakMap,io=new WeakMap,Cn=new WeakMap,ao=new WeakMap,lo=new WeakMap,$p),YE=h.createContext(void 0),XE=({client:e,children:t})=>(h.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),l.jsx(YE.Provider,{value:e,children:t}));/**
 * @remix-run/router v1.20.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ks(){return ks=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ks.apply(this,arguments)}var Tn;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Tn||(Tn={}));const up="popstate";function qE(e){e===void 0&&(e={});function t(r,o){let{pathname:s,search:i,hash:a}=r.location;return Hc("",{pathname:s,search:i,hash:a},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function n(r,o){return typeof o=="string"?o:la(o)}return JE(t,n,null,e)}function we(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Nv(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function ZE(){return Math.random().toString(36).substr(2,8)}function dp(e,t){return{usr:e.state,key:e.key,idx:t}}function Hc(e,t,n,r){return n===void 0&&(n=null),ks({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?To(t):t,{state:n,key:t&&t.key||r||ZE()})}function la(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function To(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function JE(e,t,n,r){r===void 0&&(r={});let{window:o=document.defaultView,v5Compat:s=!1}=r,i=o.history,a=Tn.Pop,c=null,u=f();u==null&&(u=0,i.replaceState(ks({},i.state,{idx:u}),""));function f(){return(i.state||{idx:null}).idx}function p(){a=Tn.Pop;let S=f(),v=S==null?null:S-u;u=S,c&&c({action:a,location:g.location,delta:v})}function d(S,v){a=Tn.Push;let m=Hc(g.location,S,v);u=f()+1;let y=dp(m,u),w=g.createHref(m);try{i.pushState(y,"",w)}catch(E){if(E instanceof DOMException&&E.name==="DataCloneError")throw E;o.location.assign(w)}s&&c&&c({action:a,location:g.location,delta:1})}function x(S,v){a=Tn.Replace;let m=Hc(g.location,S,v);u=f();let y=dp(m,u),w=g.createHref(m);i.replaceState(y,"",w),s&&c&&c({action:a,location:g.location,delta:0})}function b(S){let v=o.location.origin!=="null"?o.location.origin:o.location.href,m=typeof S=="string"?S:la(S);return m=m.replace(/ $/,"%20"),we(v,"No window.location.(origin|href) available to create URL for href: "+m),new URL(m,v)}let g={get action(){return a},get location(){return e(o,i)},listen(S){if(c)throw new Error("A history only accepts one active listener");return o.addEventListener(up,p),c=S,()=>{o.removeEventListener(up,p),c=null}},createHref(S){return t(o,S)},createURL:b,encodeLocation(S){let v=b(S);return{pathname:v.pathname,search:v.search,hash:v.hash}},push:d,replace:x,go(S){return i.go(S)}};return g}var fp;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(fp||(fp={}));function ek(e,t,n){return n===void 0&&(n="/"),tk(e,t,n,!1)}function tk(e,t,n,r){let o=typeof t=="string"?To(t):t,s=fd(o.pathname||"/",n);if(s==null)return null;let i=jv(e);nk(i);let a=null;for(let c=0;a==null&&c<i.length;++c){let u=pk(s);a=dk(i[c],u,r)}return a}function jv(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let o=(s,i,a)=>{let c={relativePath:a===void 0?s.path||"":a,caseSensitive:s.caseSensitive===!0,childrenIndex:i,route:s};c.relativePath.startsWith("/")&&(we(c.relativePath.startsWith(r),'Absolute route path "'+c.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),c.relativePath=c.relativePath.slice(r.length));let u=Ln([r,c.relativePath]),f=n.concat(c);s.children&&s.children.length>0&&(we(s.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),jv(s.children,t,f,u)),!(s.path==null&&!s.index)&&t.push({path:u,score:ck(u,s.index),routesMeta:f})};return e.forEach((s,i)=>{var a;if(s.path===""||!((a=s.path)!=null&&a.includes("?")))o(s,i);else for(let c of Tv(s.path))o(s,i,c)}),t}function Tv(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,o=n.endsWith("?"),s=n.replace(/\?$/,"");if(r.length===0)return o?[s,""]:[s];let i=Tv(r.join("/")),a=[];return a.push(...i.map(c=>c===""?s:[s,c].join("/"))),o&&a.push(...i),a.map(c=>e.startsWith("/")&&c===""?"/":c)}function nk(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:uk(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const rk=/^:[\w-]+$/,ok=3,sk=2,ik=1,ak=10,lk=-2,pp=e=>e==="*";function ck(e,t){let n=e.split("/"),r=n.length;return n.some(pp)&&(r+=lk),t&&(r+=sk),n.filter(o=>!pp(o)).reduce((o,s)=>o+(rk.test(s)?ok:s===""?ik:ak),r)}function uk(e,t){return e.length===t.length&&e.slice(0,-1).every((r,o)=>r===t[o])?e[e.length-1]-t[t.length-1]:0}function dk(e,t,n){let{routesMeta:r}=e,o={},s="/",i=[];for(let a=0;a<r.length;++a){let c=r[a],u=a===r.length-1,f=s==="/"?t:t.slice(s.length)||"/",p=hp({path:c.relativePath,caseSensitive:c.caseSensitive,end:u},f),d=c.route;if(!p&&u&&n&&!r[r.length-1].route.index&&(p=hp({path:c.relativePath,caseSensitive:c.caseSensitive,end:!1},f)),!p)return null;Object.assign(o,p.params),i.push({params:o,pathname:Ln([s,p.pathname]),pathnameBase:vk(Ln([s,p.pathnameBase])),route:d}),p.pathnameBase!=="/"&&(s=Ln([s,p.pathnameBase]))}return i}function hp(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=fk(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let s=o[0],i=s.replace(/(.)\/+$/,"$1"),a=o.slice(1);return{params:r.reduce((u,f,p)=>{let{paramName:d,isOptional:x}=f;if(d==="*"){let g=a[p]||"";i=s.slice(0,s.length-g.length).replace(/(.)\/+$/,"$1")}const b=a[p];return x&&!b?u[d]=void 0:u[d]=(b||"").replace(/%2F/g,"/"),u},{}),pathname:s,pathnameBase:i,pattern:e}}function fk(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Nv(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,a,c)=>(r.push({paramName:a,isOptional:c!=null}),c?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),o+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":e!==""&&e!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),r]}function pk(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Nv(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function fd(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function hk(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:o=""}=typeof e=="string"?To(e):e;return{pathname:n?n.startsWith("/")?n:mk(n,t):t,search:yk(r),hash:xk(o)}}function mk(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(o=>{o===".."?n.length>1&&n.pop():o!=="."&&n.push(o)}),n.length>1?n.join("/"):"/"}function Al(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function gk(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function Pv(e,t){let n=gk(e);return t?n.map((r,o)=>o===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function Rv(e,t,n,r){r===void 0&&(r=!1);let o;typeof e=="string"?o=To(e):(o=ks({},e),we(!o.pathname||!o.pathname.includes("?"),Al("?","pathname","search",o)),we(!o.pathname||!o.pathname.includes("#"),Al("#","pathname","hash",o)),we(!o.search||!o.search.includes("#"),Al("#","search","hash",o)));let s=e===""||o.pathname==="",i=s?"/":o.pathname,a;if(i==null)a=n;else{let p=t.length-1;if(!r&&i.startsWith("..")){let d=i.split("/");for(;d[0]==="..";)d.shift(),p-=1;o.pathname=d.join("/")}a=p>=0?t[p]:"/"}let c=hk(o,a),u=i&&i!=="/"&&i.endsWith("/"),f=(s||i===".")&&n.endsWith("/");return!c.pathname.endsWith("/")&&(u||f)&&(c.pathname+="/"),c}const Ln=e=>e.join("/").replace(/\/\/+/g,"/"),vk=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),yk=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,xk=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function wk(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Av=["post","put","patch","delete"];new Set(Av);const Sk=["get",...Av];new Set(Sk);/**
 * React Router v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Cs(){return Cs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Cs.apply(this,arguments)}const pd=h.createContext(null),bk=h.createContext(null),xr=h.createContext(null),Wa=h.createContext(null),Gn=h.createContext({outlet:null,matches:[],isDataRoute:!1}),_v=h.createContext(null);function Ek(e,t){let{relative:n}=t===void 0?{}:t;Is()||we(!1);let{basename:r,navigator:o}=h.useContext(xr),{hash:s,pathname:i,search:a}=Iv(e,{relative:n}),c=i;return r!=="/"&&(c=i==="/"?r:Ln([r,i])),o.createHref({pathname:c,search:a,hash:s})}function Is(){return h.useContext(Wa)!=null}function Po(){return Is()||we(!1),h.useContext(Wa).location}function Ov(e){h.useContext(xr).static||h.useLayoutEffect(e)}function Rt(){let{isDataRoute:e}=h.useContext(Gn);return e?Mk():kk()}function kk(){Is()||we(!1);let e=h.useContext(pd),{basename:t,future:n,navigator:r}=h.useContext(xr),{matches:o}=h.useContext(Gn),{pathname:s}=Po(),i=JSON.stringify(Pv(o,n.v7_relativeSplatPath)),a=h.useRef(!1);return Ov(()=>{a.current=!0}),h.useCallback(function(u,f){if(f===void 0&&(f={}),!a.current)return;if(typeof u=="number"){r.go(u);return}let p=Rv(u,JSON.parse(i),s,f.relative==="path");e==null&&t!=="/"&&(p.pathname=p.pathname==="/"?t:Ln([t,p.pathname])),(f.replace?r.replace:r.push)(p,f.state,f)},[t,r,i,s,e])}function Dv(){let{matches:e}=h.useContext(Gn),t=e[e.length-1];return t?t.params:{}}function Iv(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=h.useContext(xr),{matches:o}=h.useContext(Gn),{pathname:s}=Po(),i=JSON.stringify(Pv(o,r.v7_relativeSplatPath));return h.useMemo(()=>Rv(e,JSON.parse(i),s,n==="path"),[e,i,s,n])}function Ck(e,t){return Nk(e,t)}function Nk(e,t,n,r){Is()||we(!1);let{navigator:o}=h.useContext(xr),{matches:s}=h.useContext(Gn),i=s[s.length-1],a=i?i.params:{};i&&i.pathname;let c=i?i.pathnameBase:"/";i&&i.route;let u=Po(),f;if(t){var p;let S=typeof t=="string"?To(t):t;c==="/"||(p=S.pathname)!=null&&p.startsWith(c)||we(!1),f=S}else f=u;let d=f.pathname||"/",x=d;if(c!=="/"){let S=c.replace(/^\//,"").split("/");x="/"+d.replace(/^\//,"").split("/").slice(S.length).join("/")}let b=ek(e,{pathname:x}),g=Ak(b&&b.map(S=>Object.assign({},S,{params:Object.assign({},a,S.params),pathname:Ln([c,o.encodeLocation?o.encodeLocation(S.pathname).pathname:S.pathname]),pathnameBase:S.pathnameBase==="/"?c:Ln([c,o.encodeLocation?o.encodeLocation(S.pathnameBase).pathname:S.pathnameBase])})),s,n,r);return t&&g?h.createElement(Wa.Provider,{value:{location:Cs({pathname:"/",search:"",hash:"",state:null,key:"default"},f),navigationType:Tn.Pop}},g):g}function jk(){let e=Ik(),t=wk(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return h.createElement(h.Fragment,null,h.createElement("h2",null,"Unexpected Application Error!"),h.createElement("h3",{style:{fontStyle:"italic"}},t),n?h.createElement("pre",{style:o},n):null,null)}const Tk=h.createElement(jk,null);class Pk extends h.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?h.createElement(Gn.Provider,{value:this.props.routeContext},h.createElement(_v.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Rk(e){let{routeContext:t,match:n,children:r}=e,o=h.useContext(pd);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),h.createElement(Gn.Provider,{value:t},r)}function Ak(e,t,n,r){var o;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var s;if(!n)return null;if(n.errors)e=n.matches;else if((s=r)!=null&&s.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let i=e,a=(o=n)==null?void 0:o.errors;if(a!=null){let f=i.findIndex(p=>p.route.id&&(a==null?void 0:a[p.route.id])!==void 0);f>=0||we(!1),i=i.slice(0,Math.min(i.length,f+1))}let c=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let f=0;f<i.length;f++){let p=i[f];if((p.route.HydrateFallback||p.route.hydrateFallbackElement)&&(u=f),p.route.id){let{loaderData:d,errors:x}=n,b=p.route.loader&&d[p.route.id]===void 0&&(!x||x[p.route.id]===void 0);if(p.route.lazy||b){c=!0,u>=0?i=i.slice(0,u+1):i=[i[0]];break}}}return i.reduceRight((f,p,d)=>{let x,b=!1,g=null,S=null;n&&(x=a&&p.route.id?a[p.route.id]:void 0,g=p.route.errorElement||Tk,c&&(u<0&&d===0?(b=!0,S=null):u===d&&(b=!0,S=p.route.hydrateFallbackElement||null)));let v=t.concat(i.slice(0,d+1)),m=()=>{let y;return x?y=g:b?y=S:p.route.Component?y=h.createElement(p.route.Component,null):p.route.element?y=p.route.element:y=f,h.createElement(Rk,{match:p,routeContext:{outlet:f,matches:v,isDataRoute:n!=null},children:y})};return n&&(p.route.ErrorBoundary||p.route.errorElement||d===0)?h.createElement(Pk,{location:n.location,revalidation:n.revalidation,component:g,error:x,children:m(),routeContext:{outlet:null,matches:v,isDataRoute:!0}}):m()},null)}var Mv=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Mv||{}),ca=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(ca||{});function _k(e){let t=h.useContext(pd);return t||we(!1),t}function Ok(e){let t=h.useContext(bk);return t||we(!1),t}function Dk(e){let t=h.useContext(Gn);return t||we(!1),t}function Lv(e){let t=Dk(),n=t.matches[t.matches.length-1];return n.route.id||we(!1),n.route.id}function Ik(){var e;let t=h.useContext(_v),n=Ok(ca.UseRouteError),r=Lv(ca.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function Mk(){let{router:e}=_k(Mv.UseNavigateStable),t=Lv(ca.UseNavigateStable),n=h.useRef(!1);return Ov(()=>{n.current=!0}),h.useCallback(function(o,s){s===void 0&&(s={}),n.current&&(typeof o=="number"?e.navigate(o):e.navigate(o,Cs({fromRouteId:t},s)))},[e,t])}function He(e){we(!1)}function Lk(e){let{basename:t="/",children:n=null,location:r,navigationType:o=Tn.Pop,navigator:s,static:i=!1,future:a}=e;Is()&&we(!1);let c=t.replace(/^\/*/,"/"),u=h.useMemo(()=>({basename:c,navigator:s,static:i,future:Cs({v7_relativeSplatPath:!1},a)}),[c,a,s,i]);typeof r=="string"&&(r=To(r));let{pathname:f="/",search:p="",hash:d="",state:x=null,key:b="default"}=r,g=h.useMemo(()=>{let S=fd(f,c);return S==null?null:{location:{pathname:S,search:p,hash:d,state:x,key:b},navigationType:o}},[c,f,p,d,x,b,o]);return g==null?null:h.createElement(xr.Provider,{value:u},h.createElement(Wa.Provider,{children:n,value:g}))}function zk(e){let{children:t,location:n}=e;return Ck(Kc(t),n)}new Promise(()=>{});function Kc(e,t){t===void 0&&(t=[]);let n=[];return h.Children.forEach(e,(r,o)=>{if(!h.isValidElement(r))return;let s=[...t,o];if(r.type===h.Fragment){n.push.apply(n,Kc(r.props.children,s));return}r.type!==He&&we(!1),!r.props.index||!r.props.children||we(!1);let i={id:r.props.id||s.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(i.children=Kc(r.props.children,s)),n.push(i)}),n}/**
 * React Router DOM v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Qc(){return Qc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Qc.apply(this,arguments)}function Fk(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,s;for(s=0;s<r.length;s++)o=r[s],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function $k(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Uk(e,t){return e.button===0&&(!t||t==="_self")&&!$k(e)}function Gc(e){return e===void 0&&(e=""),new URLSearchParams(typeof e=="string"||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce((t,n)=>{let r=e[n];return t.concat(Array.isArray(r)?r.map(o=>[n,o]):[[n,r]])},[]))}function Bk(e,t){let n=Gc(e);return t&&t.forEach((r,o)=>{n.has(o)||t.getAll(o).forEach(s=>{n.append(o,s)})}),n}const Vk=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],Wk="6";try{window.__reactRouterVersion=Wk}catch{}const Hk="startTransition",mp=Zp[Hk];function Kk(e){let{basename:t,children:n,future:r,window:o}=e,s=h.useRef();s.current==null&&(s.current=qE({window:o,v5Compat:!0}));let i=s.current,[a,c]=h.useState({action:i.action,location:i.location}),{v7_startTransition:u}=r||{},f=h.useCallback(p=>{u&&mp?mp(()=>c(p)):c(p)},[c,u]);return h.useLayoutEffect(()=>i.listen(f),[i,f]),h.createElement(Lk,{basename:t,children:n,location:a.location,navigationType:a.action,navigator:i,future:r})}const Qk=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Gk=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,gp=h.forwardRef(function(t,n){let{onClick:r,relative:o,reloadDocument:s,replace:i,state:a,target:c,to:u,preventScrollReset:f,viewTransition:p}=t,d=Fk(t,Vk),{basename:x}=h.useContext(xr),b,g=!1;if(typeof u=="string"&&Gk.test(u)&&(b=u,Qk))try{let y=new URL(window.location.href),w=u.startsWith("//")?new URL(y.protocol+u):new URL(u),E=fd(w.pathname,x);w.origin===y.origin&&E!=null?u=E+w.search+w.hash:g=!0}catch{}let S=Ek(u,{relative:o}),v=Yk(u,{replace:i,state:a,target:c,preventScrollReset:f,relative:o,viewTransition:p});function m(y){r&&r(y),y.defaultPrevented||v(y)}return h.createElement("a",Qc({},d,{href:b||S,onClick:g||s?r:m,ref:n,target:c}))});var vp;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(vp||(vp={}));var yp;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(yp||(yp={}));function Yk(e,t){let{target:n,replace:r,state:o,preventScrollReset:s,relative:i,viewTransition:a}=t===void 0?{}:t,c=Rt(),u=Po(),f=Iv(e,{relative:i});return h.useCallback(p=>{if(Uk(p,n)){p.preventDefault();let d=r!==void 0?r:la(u)===la(f);c(e,{replace:d,state:o,preventScrollReset:s,relative:i,viewTransition:a})}},[u,c,f,r,o,n,e,s,i,a])}function wr(e){let t=h.useRef(Gc(e)),n=h.useRef(!1),r=Po(),o=h.useMemo(()=>Bk(r.search,n.current?null:t.current),[r.search]),s=Rt(),i=h.useCallback((a,c)=>{const u=Gc(typeof a=="function"?a(o):a);n.current=!0,s("?"+u,c)},[s,o]);return[o,i]}const hd=_a("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),ne=h.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...o},s)=>{const i=r?vo:"button";return l.jsx(i,{className:ce(hd({variant:t,size:n,className:e})),ref:s,...o})});ne.displayName="Button";const zv=()=>{const e=Rt(),[t,n]=h.useState(!1);h.useEffect(()=>{const s=localStorage.getItem("sessionToken");n(!!s)},[]);const r=()=>{e("/login")},o=()=>{localStorage.removeItem("sessionToken"),localStorage.removeItem("urlToken"),n(!1),e("/")};return l.jsx("header",{className:"fixed top-0 w-full bg-background/80 backdrop-blur-md border-b border-border z-50",children:l.jsxs("div",{className:"container mx-auto px-4 h-16 flex items-center justify-between",children:[l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx(je,{className:"h-10 w-10 text-primary"}),l.jsx("span",{className:"text-2xl font-bold text-foreground",children:"Nodex Solutions"})]}),l.jsxs("nav",{className:"hidden md:flex items-center space-x-8",children:[l.jsx("a",{href:"#features",className:"text-muted-foreground hover:text-foreground transition-colors",children:"Recursos"}),l.jsx("a",{href:"#how-it-works",className:"text-muted-foreground hover:text-foreground transition-colors",children:"Como Funciona"}),l.jsx("a",{href:"#pricing",className:"text-muted-foreground hover:text-foreground transition-colors",children:"Preços"})]}),l.jsxs("div",{className:"flex items-center space-x-4",children:[t?l.jsxs(l.Fragment,{children:[l.jsxs(ne,{variant:"outline",onClick:()=>{const s=localStorage.getItem("urlToken");e(s?`/dashboard/${s}`:"/dashboard")},className:"border-[#5865F2] text-[#5865F2] hover:bg-[#5865F2] hover:text-white transition-all duration-200",children:[l.jsx(D1,{className:"mr-2 h-4 w-4"}),l.jsx("span",{children:"Dashboard"})]}),l.jsxs(ne,{variant:"ghost",onClick:o,className:"text-muted-foreground hover:text-foreground",children:[l.jsx(_g,{className:"mr-2 h-4 w-4"}),l.jsx("span",{children:"Sair"})]})]}):l.jsxs("button",{onClick:r,className:"sc-bd79e608-0 gRsUgy",style:{color:"#0C0B0C",fontSize:"18.6667px",fontFamily:"Montserrat, sans-serif",backgroundColor:"#00FFFF",padding:"10px 20px",border:"none",borderRadius:"4px",cursor:"pointer",display:"flex",alignItems:"center",gap:"8px",transition:"all 0.2s ease"},onMouseEnter:s=>{s.currentTarget.style.backgroundColor="#00E6E6"},onMouseLeave:s=>{s.currentTarget.style.backgroundColor="#00FFFF"},children:[l.jsx("svg",{stroke:"currentColor",fill:"currentColor",strokeWidth:"0",viewBox:"0 0 640 512",height:"1em",width:"1em",xmlns:"http://www.w3.org/2000/svg",children:l.jsx("path",{d:"M524.531,69.836a1.5,1.5,0,0,0-.764-.7A485.065,485.065,0,0,0,404.081,32.03a1.816,1.816,0,0,0-1.923.91,337.461,337.461,0,0,0-14.9,30.6,447.848,447.848,0,0,0-134.426,0,309.541,309.541,0,0,0-15.135-30.6,1.89,1.89,0,0,0-1.924-.91A483.689,483.689,0,0,0,116.085,69.137a1.712,1.712,0,0,0-.788.676C39.068,183.651,18.186,294.69,28.43,404.354a2.016,2.016,0,0,0,.765,1.375A487.666,487.666,0,0,0,176.02,479.918a1.9,1.9,0,0,0,2.063-.676A348.2,348.2,0,0,0,208.12,430.4a1.86,1.86,0,0,0-1.019-2.588,321.173,321.173,0,0,1-45.868-21.853,1.885,1.885,0,0,1-.185-3.126c3.082-2.309,6.166-4.711,9.109-7.137a1.819,1.819,0,0,1,1.9-.256c96.229,43.917,200.41,43.917,295.5,0a1.812,1.812,0,0,1,1.924.233c2.944,2.426,6.027,4.851,9.132,7.16a1.884,1.884,0,0,1-.162,3.126,301.407,301.407,0,0,1-45.89,21.83,1.875,1.875,0,0,0-1,2.611,391.055,391.055,0,0,0,30.014,48.815,1.864,1.864,0,0,0,2.063.7A486.048,486.048,0,0,0,610.7,405.729a1.882,1.882,0,0,0,.765-1.352C623.729,277.594,590.933,167.465,524.531,69.836ZM222.491,337.58c-28.972,0-52.844-26.587-52.844-59.239S193.056,219.1,222.491,219.1c29.665,0,53.306,26.82,52.843,59.239C275.334,310.993,251.924,337.58,222.491,337.58Zm195.38,0c-28.971,0-52.843-26.587-52.843-59.239S388.437,219.1,417.871,219.1c29.667,0,53.307,26.82,52.844,59.239C470.715,310.993,447.538,337.58,417.871,337.58Z"})}),"Login"]}),l.jsx(ne,{variant:"ghost",size:"icon",className:"md:hidden",children:l.jsx(P1,{className:"h-5 w-5"})})]})]})})},Xk=()=>{const e=Rt(),t=()=>{e("/login")};return l.jsx("section",{className:"pt-32 pb-20 px-4",children:l.jsx("div",{className:"container mx-auto text-center",children:l.jsxs("div",{className:"max-w-3xl mx-auto",children:[l.jsx("h1",{className:"text-6xl md:text-7xl lg:text-8xl font-bold text-foreground mb-6 leading-tight tracking-tight",children:"Automatize vendas no Discord"}),l.jsx("p",{className:"text-xl text-muted-foreground mb-12 max-w-2xl mx-auto leading-relaxed",children:"Bot completo para processar pagamentos, gerenciar clientes e entregar produtos automaticamente."}),l.jsx("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:l.jsxs(ne,{size:"lg",className:"text-base px-8 py-6",onClick:t,children:[l.jsx(je,{className:"mr-2 h-5 w-5"}),"Começar Agora",l.jsx(E1,{className:"ml-2 h-4 w-4"})]})})]})})})},qk=()=>{const e=[{icon:Dg,title:"Vendas automatizadas",description:"Carrinho, checkout e entrega automática de produtos digitais"},{icon:Ur,title:"Pagamentos integrados",description:"PIX, cartão e outras formas de pagamento com segurança"},{icon:k1,title:"Analytics em tempo real",description:"Métricas de vendas, conversão e performance detalhadas"},{icon:Og,title:"Sistema anti-fraude",description:"Proteção avançada contra chargebacks e tentativas de fraude"}];return l.jsx("section",{id:"features",className:"py-20 px-4",children:l.jsxs("div",{className:"container mx-auto",children:[l.jsxs("div",{className:"text-center mb-16",children:[l.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-foreground mb-4",children:"Recursos essenciais"}),l.jsx("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"Tudo que você precisa para automatizar vendas no Discord"})]}),l.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-5xl mx-auto",children:e.map((t,n)=>l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"inline-flex items-center justify-center w-12 h-12 bg-muted rounded-lg mb-4",children:l.jsx(t.icon,{className:"h-6 w-6 text-foreground"})}),l.jsx("h3",{className:"text-lg font-semibold text-foreground mb-2",children:t.title}),l.jsx("p",{className:"text-sm text-muted-foreground leading-relaxed",children:t.description})]},n))})]})})},Zk=()=>{const e=[{number:"01",title:"Adicione ao Discord",description:"Autorize as permissões necessárias"},{number:"02",title:"Configure produtos",description:"Defina preços e métodos de pagamento"},{number:"03",title:"Comece a vender",description:"Bot gerencia todo o processo automaticamente"}];return l.jsx("section",{id:"how-it-works",className:"py-20 px-4 bg-muted/20",children:l.jsxs("div",{className:"container mx-auto",children:[l.jsxs("div",{className:"text-center mb-16",children:[l.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-foreground mb-4",children:"Como funciona"}),l.jsx("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"Configure em minutos e comece a vender automaticamente"})]}),l.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-12 max-w-4xl mx-auto",children:e.map((t,n)=>l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"text-3xl font-bold text-muted-foreground mb-4",children:t.number}),l.jsx("h3",{className:"text-xl font-semibold text-foreground mb-3",children:t.title}),l.jsx("p",{className:"text-muted-foreground",children:t.description})]},n))})]})})},Jk="modulepreload",eC=function(e){return"/"+e},xp={},Yc=function(t,n,r){let o=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const i=document.querySelector("meta[property=csp-nonce]"),a=(i==null?void 0:i.nonce)||(i==null?void 0:i.getAttribute("nonce"));o=Promise.allSettled(n.map(c=>{if(c=eC(c),c in xp)return;xp[c]=!0;const u=c.endsWith(".css"),f=u?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${c}"]${f}`))return;const p=document.createElement("link");if(p.rel=u?"stylesheet":Jk,u||(p.as="script"),p.crossOrigin="",p.href=c,a&&p.setAttribute("nonce",a),document.head.appendChild(p),u)return new Promise((d,x)=>{p.addEventListener("load",d),p.addEventListener("error",()=>x(new Error(`Unable to preload CSS for ${c}`)))})}))}function s(i){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=i,window.dispatchEvent(a),!a.defaultPrevented)throw i}return o.then(i=>{for(const a of i||[])a.status==="rejected"&&s(a.reason);return t().catch(s)})},tC=()=>{const[e,t]=h.useState(null),n=async o=>{try{t(o),console.log(`🔄 Iniciando processo para plano: ${o}`);const s=localStorage.getItem("sessionToken");if(console.log("🔑 SessionToken encontrado:",!!s),!s){console.log(`🚀 Redirecionando para login Discord com plano: ${o}`),localStorage.setItem("selectedPlan",o);const a="1383995563342827570",c=encodeURIComponent(`${window.location.origin}/plan-callback`),u=encodeURIComponent("identify guilds.join guilds"),f=`https://discord.com/api/oauth2/authorize?client_id=${a}&redirect_uri=${c}&response_type=code&scope=${u}`;console.log("🔗 URL de autenticação:",f),window.location.href=f;return}const i=o==="basic"?"price_1RdYzeRIzvHRKrKIhNsADY81":"price_1RdZ0ORIzvHRKrKIt3O227mH";console.log(`� Iniciando checkout para plano ${o} com priceId: ${i}`);try{const a=await fetch("/api/stripe/create-checkout-session",{method:"POST",headers:{"Content-Type":"application/json","x-session-token":s},body:JSON.stringify({priceId:i,plan:o,successUrl:`${window.location.origin}/success`,cancelUrl:`${window.location.origin}/pricing`})});if(!a.ok)throw new Error("Erro ao criar sessão de checkout");const{sessionId:c}=await a.json(),u=await Yc(()=>import("./index-CtFyKYG2.js"),[]).then(f=>f.loadStripe("pk_test_51RdYpaRIzvHRKrKI5lpipw0j0NqSDi7VP4yn30XKkFt9yOICAeEeWCuHhwm3d8qb5mWmE0J4fn6pBOPAzNNOmu4U00lLhT8skQ"));u&&await u.redirectToCheckout({sessionId:c})}catch(a){console.error("Erro ao processar pagamento:",a),ft.error("Erro ao processar pagamento")}}catch(s){console.error("Erro ao processar plano:",s),ft.error(s instanceof Error?s.message:"Erro ao processar pagamento")}finally{t(null)}},r=[{name:"Basic",price:"R$ 19.97",period:"/mês",description:"Plano essencial para começar",features:["Vendas via Pix/Cartão/Crypto","Sistema de ticket integrado","Defesa Anti-Fraude (Para Mercado Pago)","Avatar/Nome/Status/Cores customizáveis"],notIncluded:["Logs, moderação e boas vindas","Rastreador de convites (Invite Tracker)","Auth e Backup avançado (eCloud)"],buttonText:"Adquira agora",featured:!1,planType:"basic"},{name:"Pro",price:"R$ 39.90",period:"/mês",description:"Para vendedores profissionais",features:["Tudo do plano Basic","Logs, moderação e boas vindas","Rastreador de convites (Invite Tracker)","Auth e Backup avançado (eCloud)","Vendas ilimitadas","Produtos ilimitados","Suporte prioritário 24/7","Analytics avançado"],buttonText:"Assinar Pro",featured:!0,planType:"pro"}];return l.jsx("section",{id:"pricing",className:"py-20 px-4",children:l.jsxs("div",{className:"container mx-auto",children:[l.jsxs("div",{className:"text-center mb-16",children:[l.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-foreground mb-4",children:"Preços simples"}),l.jsx("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"Comece grátis e escale conforme cresce"})]}),l.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto",children:r.map((o,s)=>l.jsxs("div",{className:`p-8 rounded-lg border ${o.featured?"border-primary bg-card":"border-border bg-card/50"}`,children:[l.jsxs("div",{className:"text-center mb-8",children:[l.jsx("h3",{className:"text-xl font-semibold text-foreground mb-2",children:o.name}),l.jsx("p",{className:"text-muted-foreground text-sm mb-4",children:o.description}),l.jsxs("div",{className:"mb-6",children:[l.jsx("span",{className:"text-4xl font-bold text-foreground",children:o.price}),o.period&&l.jsx("span",{className:"text-muted-foreground",children:o.period})]})]}),l.jsxs("ul",{className:"space-y-3 mb-8",children:[o.features.map((i,a)=>l.jsxs("li",{className:"flex items-center space-x-3",children:[l.jsx(C1,{className:"h-4 w-4 text-green-600 flex-shrink-0"}),l.jsx("span",{className:"text-sm text-muted-foreground",children:i})]},a)),o.notIncluded&&o.notIncluded.map((i,a)=>l.jsxs("li",{className:"flex items-center space-x-3",children:[l.jsx("span",{className:"h-4 w-4 text-red-500 flex-shrink-0",children:"✕"}),l.jsx("span",{className:"text-sm text-muted-foreground opacity-60",children:i})]},`not-${a}`))]}),l.jsx(ne,{className:`w-full ${o.featured?"":"bg-primary text-primary-foreground hover:bg-primary/90"}`,variant:(o.featured,"default"),size:"lg",onClick:()=>n(o.planType),disabled:e!==null,children:e===o.planType?"Processando...":o.buttonText})]},s))})]})})},nC=()=>l.jsx("footer",{className:"bg-card border-t border-border py-16 px-4",children:l.jsxs("div",{className:"container mx-auto",children:[l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx(je,{className:"h-6 w-6 text-primary"}),l.jsx("span",{className:"text-lg font-bold text-foreground",children:"SalesBot"})]}),l.jsx("p",{className:"text-sm text-muted-foreground max-w-sm",children:"Automatize vendas no Discord de forma profissional."})]}),l.jsxs("div",{children:[l.jsx("h4",{className:"font-semibold text-foreground mb-4",children:"Produto"}),l.jsxs("ul",{className:"space-y-2",children:[l.jsx("li",{children:l.jsx("a",{href:"#features",className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:"Recursos"})}),l.jsx("li",{children:l.jsx("a",{href:"#pricing",className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:"Preços"})}),l.jsx("li",{children:l.jsx("a",{href:"#",className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:"Documentação"})})]})]}),l.jsxs("div",{children:[l.jsx("h4",{className:"font-semibold text-foreground mb-4",children:"Suporte"}),l.jsxs("ul",{className:"space-y-2",children:[l.jsx("li",{children:l.jsx("a",{href:"#",className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:"Central de ajuda"})}),l.jsx("li",{children:l.jsx("a",{href:"#",className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:"Contato"})}),l.jsx("li",{children:l.jsx("a",{href:"#",className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:"Status"})})]})]}),l.jsxs("div",{children:[l.jsx("h4",{className:"font-semibold text-foreground mb-4",children:"Legal"}),l.jsxs("ul",{className:"space-y-2",children:[l.jsx("li",{children:l.jsx("a",{href:"#",className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:"Termos"})}),l.jsx("li",{children:l.jsx("a",{href:"#",className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:"Privacidade"})})]})]})]}),l.jsx("div",{className:"border-t border-border mt-12 pt-8 text-center",children:l.jsx("p",{className:"text-sm text-muted-foreground",children:"© 2024 SalesBot. Todos os direitos reservados."})})]})}),rC=({children:e})=>{const t=Rt();return h.useEffect(()=>{const n=()=>{const o=localStorage.getItem("sessionToken"),s=localStorage.getItem("urlToken");if(o&&s){t(`/dashboard/${s}`,{replace:!0});return}if(o&&!s){r(o);return}},r=async o=>{try{const s=await fetch(`/api/user-full?token=${o}`);if(s.ok){const i=await s.json();if(i.urlToken){localStorage.setItem("urlToken",i.urlToken),t(`/dashboard/${i.urlToken}`,{replace:!0});return}}localStorage.removeItem("sessionToken")}catch(s){console.error("Erro ao buscar urlToken:",s),localStorage.removeItem("sessionToken")}};window.location.pathname==="/"&&n()},[t]),l.jsx(l.Fragment,{children:e})},oC=()=>l.jsx(rC,{children:l.jsxs("div",{className:"dark min-h-screen bg-background",children:[l.jsx(zv,{}),l.jsx(Xk,{}),l.jsx(qk,{}),l.jsx(Zk,{}),l.jsx(tC,{}),l.jsx(nC,{})]})}),Ie=h.forwardRef(({className:e,...t},n)=>l.jsx("div",{ref:n,className:ce("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));Ie.displayName="Card";const pr=h.forwardRef(({className:e,...t},n)=>l.jsx("div",{ref:n,className:ce("flex flex-col space-y-1.5 p-6",e),...t}));pr.displayName="CardHeader";const hr=h.forwardRef(({className:e,...t},n)=>l.jsx("h3",{ref:n,className:ce("text-2xl font-semibold leading-none tracking-tight",e),...t}));hr.displayName="CardTitle";const mr=h.forwardRef(({className:e,...t},n)=>l.jsx("p",{ref:n,className:ce("text-sm text-muted-foreground",e),...t}));mr.displayName="CardDescription";const Me=h.forwardRef(({className:e,...t},n)=>l.jsx("div",{ref:n,className:ce("p-6 pt-0",e),...t}));Me.displayName="CardContent";const sC=h.forwardRef(({className:e,...t},n)=>l.jsx("div",{ref:n,className:ce("flex items-center p-6 pt-0",e),...t}));sC.displayName="CardFooter";const iC=()=>{const e=()=>{const t="1383995563342827570",n=encodeURIComponent("http://localhost:3003/callback"),r=encodeURIComponent("identify guilds.join guilds"),o=`https://discord.com/api/oauth2/authorize?client_id=${t}&redirect_uri=${n}&response_type=code&scope=${r}`;window.location.href=o};return l.jsx("div",{className:"dark min-h-screen bg-background flex items-center justify-center p-4",children:l.jsx("div",{className:"w-full max-w-md",children:l.jsxs(Ie,{className:"border-border bg-card",children:[l.jsxs(pr,{className:"text-center space-y-4",children:[l.jsx("div",{className:"mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center",children:l.jsx(je,{className:"w-8 h-8 text-primary"})}),l.jsx(hr,{className:"text-2xl font-bold text-foreground",children:"Entrar com Discord"}),l.jsx(mr,{className:"text-muted-foreground",children:"Faça login para configurar seu bot de vendas personalizado"})]}),l.jsxs(Me,{className:"space-y-6",children:[l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{className:"flex items-center gap-3 text-sm text-muted-foreground",children:[l.jsx(Og,{className:"w-4 h-4 text-green-500"}),l.jsx("span",{children:"Login seguro via Discord OAuth"})]}),l.jsxs("div",{className:"flex items-center gap-3 text-sm text-muted-foreground",children:[l.jsx(L1,{className:"w-4 h-4 text-blue-500"}),l.jsx("span",{children:"Configuração automática do bot"})]}),l.jsxs("div",{className:"flex items-center gap-3 text-sm text-muted-foreground",children:[l.jsx(je,{className:"w-4 h-4 text-purple-500"}),l.jsx("span",{children:"Sistema de vendas completo"})]})]}),l.jsxs(ne,{onClick:e,className:"w-full bg-[#5865F2] hover:bg-[#4752C4] text-white",size:"lg",children:[l.jsx(je,{className:"w-4 h-4 mr-2"}),"Continuar com Discord"]}),l.jsx("p",{className:"text-xs text-muted-foreground text-center",children:"Ao continuar, você concorda com nossos termos de serviço e política de privacidade."})]})]})})})},aC=()=>{const[e]=wr(),[t,n]=h.useState(null),[r,o]=h.useState(!0);h.useEffect(()=>{(async()=>{try{const a=e.get("session_id");if(a){const c=await fetch(`/api/stripe/session-info?session_id=${a}`,{headers:{"x-session-token":localStorage.getItem("sessionToken")||""}});if(c.ok){const u=await c.json();n(u.licenseKey)}}}catch(a){console.error("Erro ao buscar informações da licença:",a)}finally{o(!1)}})()},[e]);const s=()=>{t&&(navigator.clipboard.writeText(t),ft.success("Chave de licença copiada!"))};return l.jsxs("div",{className:"dark min-h-screen bg-background",children:[l.jsx(zv,{}),l.jsx("div",{className:"container mx-auto px-4 py-20",children:l.jsxs("div",{className:"max-w-md mx-auto text-center",children:[l.jsx(Oa,{className:"h-16 w-16 text-green-600 mx-auto mb-6"}),l.jsx("h1",{className:"text-3xl font-bold text-foreground mb-4",children:"Pagamento Realizado com Sucesso!"}),l.jsx("p",{className:"text-muted-foreground mb-8",children:"Sua licença foi ativada e você já pode começar a usar o bot."}),r?l.jsxs("div",{className:"mb-8",children:[l.jsx("div",{className:"animate-pulse bg-muted h-4 rounded mb-2"}),l.jsx("div",{className:"animate-pulse bg-muted h-4 rounded w-3/4 mx-auto"})]}):t?l.jsxs("div",{className:"mb-8 p-4 bg-muted rounded-lg",children:[l.jsx("p",{className:"text-sm text-muted-foreground mb-2",children:"Sua chave de licença:"}),l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx("code",{className:"flex-1 bg-background px-3 py-2 rounded text-sm font-mono",children:t}),l.jsx(ne,{size:"sm",variant:"outline",onClick:s,children:l.jsx(Rg,{className:"h-4 w-4"})})]})]}):null,l.jsxs("div",{className:"space-y-4",children:[l.jsx(ne,{asChild:!0,className:"w-full",children:l.jsx(gp,{to:localStorage.getItem("urlToken")?`/dashboard/${localStorage.getItem("urlToken")}`:"/dashboard",children:"Ir para Dashboard"})}),l.jsx(ne,{variant:"outline",asChild:!0,className:"w-full",children:l.jsxs(gp,{to:"/",className:"flex items-center justify-center space-x-2",children:[l.jsx("span",{children:"Voltar ao Início"}),l.jsx(Da,{className:"h-4 w-4"})]})})]}),l.jsxs("div",{className:"mt-8 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg",children:[l.jsx("h3",{className:"font-semibold text-foreground mb-2",children:"Próximos passos:"}),l.jsxs("ol",{className:"text-sm text-muted-foreground text-left space-y-1",children:[l.jsx("li",{children:"1. Acesse o dashboard para configurar seu bot"}),l.jsx("li",{children:"2. Configure os canais e produtos"}),l.jsx("li",{children:"3. Comece a vender!"})]})]})]})})]})},Xc=h.forwardRef(({className:e,type:t,...n},r)=>l.jsx("input",{type:t,className:ce("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...n}));Xc.displayName="Input";var lC="Label",Fv=h.forwardRef((e,t)=>l.jsx(ye.label,{...e,ref:t,onMouseDown:n=>{var o;n.target.closest("button, input, select, textarea")||((o=e.onMouseDown)==null||o.call(e,n),!n.defaultPrevented&&n.detail>1&&n.preventDefault())}}));Fv.displayName=lC;var $v=Fv;const cC=_a("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),qc=h.forwardRef(({className:e,...t},n)=>l.jsx($v,{ref:n,className:ce(cC(),e),...t}));qc.displayName=$v.displayName;const uC=()=>{const[e,t]=h.useState(""),[n,r]=h.useState(""),[o,s]=h.useState(!1),[i,a]=h.useState(""),[c,u]=h.useState(""),f=Rt(),[p]=wr(),d=p.get("username")||"Usuário",x=g=>/^\d{17,19}$/.test(g),b=async g=>{if(g.preventDefault(),a(""),u(""),s(!0),!x(e)){a("Client ID deve ter entre 17-19 dígitos"),s(!1);return}if(n.length<50){a("Token parece estar inválido (muito curto)"),s(!1);return}try{const v=await(await fetch("/api/configure-bot",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({clientId:e,token:n,username:d,userId:p.get("userId")})})).json();v.success?(u("Aplicação configurada com sucesso!"),setTimeout(async()=>{const m=localStorage.getItem("sessionToken");if(m)try{const y=await fetch(`/api/user-info?token=${m}`);if(y.ok){const w=await y.json(),E=await fetch(`/api/user-full?token=${m}`);if(E.ok){const C=await E.json();C.urlToken?f(`/dashboard/${C.urlToken}?token=${m}`):f(`/dashboard?token=${m}`)}else f(`/dashboard?token=${m}`)}else f("/dashboard")}catch{f("/dashboard")}else f("/dashboard")},2e3)):a(v.message||"Erro ao configurar o bot")}catch{a("Erro de conexão. Tente novamente.")}finally{s(!1)}};return l.jsx("div",{className:"dark min-h-screen bg-background flex items-center justify-center p-4",children:l.jsx("div",{className:"w-full max-w-2xl",children:l.jsxs(Ie,{className:"border-border bg-card",children:[l.jsxs(pr,{className:"text-center space-y-4",children:[l.jsx("div",{className:"mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center",children:l.jsx(je,{className:"w-8 h-8 text-primary"})}),l.jsx(hr,{className:"text-2xl font-bold text-foreground",children:"🎉 Configurar seu Bot Dedicado"}),l.jsxs(mr,{className:"text-muted-foreground",children:["Olá ",l.jsx("strong",{children:d}),"! Configure seu bot personalizado para vendas"]})]}),l.jsxs(Me,{className:"space-y-6",children:[l.jsxs("div",{className:"bg-blue-500/10 border border-blue-500/20 rounded-lg p-4",children:[l.jsxs("div",{className:"flex items-center gap-2 text-blue-600 font-medium mb-2",children:[l.jsx(je,{className:"w-4 h-4"}),"Tutorial rápido"]}),l.jsxs("div",{className:"space-y-2 text-sm text-blue-600/80",children:[l.jsxs("p",{children:[l.jsx("strong",{children:"1."})," Acesse: ",l.jsxs("a",{href:"https://discord.com/developers/applications",target:"_blank",rel:"noopener noreferrer",className:"underline inline-flex items-center gap-1",children:["Discord Developer Portal ",l.jsx(Da,{className:"w-3 h-3"})]})]}),l.jsxs("p",{children:[l.jsx("strong",{children:"2."}),' Clique em "New Application" e dê um nome']}),l.jsxs("p",{children:[l.jsx("strong",{children:"3."}),' Vá em "OAuth2" → "General" e copie o ',l.jsx("strong",{children:"Client ID"})]}),l.jsxs("p",{children:[l.jsx("strong",{children:"4."}),' Vá em "Bot" → "Token" e copie o ',l.jsx("strong",{children:"Token"})]}),l.jsxs("p",{children:[l.jsx("strong",{children:"5."})," Cole os dados abaixo"]})]})]}),l.jsxs("form",{onSubmit:b,className:"space-y-4",children:[l.jsxs("div",{className:"space-y-2",children:[l.jsx(qc,{htmlFor:"clientId",children:"🤖 Client ID"}),l.jsx(Xc,{id:"clientId",type:"text",placeholder:"Ex: 1234567890123456789",value:e,onChange:g=>t(g.target.value),required:!0,className:"font-mono"}),l.jsx("p",{className:"text-xs text-muted-foreground",children:"Número de 17-19 dígitos encontrado em OAuth2 → General"})]}),l.jsxs("div",{className:"space-y-2",children:[l.jsx(qc,{htmlFor:"token",children:"🔑 Token do Bot"}),l.jsx(Xc,{id:"token",type:"password",placeholder:"Ex: MTIzNDU2Nzg5MDEyMzQ1Njc4OQ.GhIjKl...",value:n,onChange:g=>r(g.target.value),required:!0,className:"font-mono"}),l.jsx("p",{className:"text-xs text-muted-foreground",children:"Token encontrado em Bot → Token (mantenha em segredo!)"})]}),i&&l.jsxs("div",{className:"flex items-center gap-2 text-red-600 bg-red-500/10 border border-red-500/20 rounded-lg p-3",children:[l.jsx(N1,{className:"w-4 h-4"}),l.jsx("span",{className:"text-sm",children:i})]}),c&&l.jsxs("div",{className:"flex items-center gap-2 text-green-600 bg-green-500/10 border border-green-500/20 rounded-lg p-3",children:[l.jsx(Oa,{className:"w-4 h-4"}),l.jsx("span",{className:"text-sm",children:c})]}),l.jsxs("div",{className:"flex gap-3",children:[l.jsx(ne,{type:"button",variant:"outline",onClick:()=>f(`/dashboard?username=${d}&userId=${p.get("userId")}`),className:"flex-1",children:"← Voltar ao Dashboard"}),l.jsx(ne,{type:"submit",disabled:o,className:"flex-1",children:o?l.jsxs(l.Fragment,{children:[l.jsx(Ag,{className:"w-4 h-4 mr-2 animate-spin"}),"Configurando..."]}):l.jsxs(l.Fragment,{children:[l.jsx(je,{className:"w-4 h-4 mr-2"}),"Confirmar"]})})]})]}),l.jsxs("div",{className:"bg-muted/50 rounded-lg p-4 space-y-2",children:[l.jsx("h3",{className:"font-medium text-foreground",children:"📋 Seu bot terá:"}),l.jsxs("ul",{className:"text-sm text-muted-foreground space-y-1",children:[l.jsxs("li",{children:["• ",l.jsx("strong",{children:"/vendas"})," - Painel principal de vendas"]}),l.jsxs("li",{children:["• ",l.jsx("strong",{children:"/produtos"})," - Lista de produtos disponíveis"]}),l.jsxs("li",{children:["• ",l.jsx("strong",{children:"/comprar [produto]"})," - Processo de compra"]}),l.jsxs("li",{children:["• ",l.jsx("strong",{children:"/suporte"})," - Informações de contato"]})]})]})]})]})})})},dC=()=>{const[e,t]=h.useState("loading"),[n,r]=h.useState(null),o=Rt(),[s]=wr(),i=s.get("username")||"Usuário",a=s.get("clientId")||"",c=`https://discord.com/api/oauth2/authorize?client_id=${a}&permissions=2048&scope=bot`;h.useEffect(()=>{const f=setTimeout(()=>{t("active"),r({tag:"SeuBot#1234",servers:0,uptime:"0 minutos"})},2e3);return()=>clearTimeout(f)},[]);const u=()=>{navigator.clipboard.writeText(c)};return l.jsx("div",{className:"dark min-h-screen bg-background flex items-center justify-center p-4",children:l.jsx("div",{className:"w-full max-w-2xl",children:l.jsxs(Ie,{className:"border-border bg-card",children:[l.jsxs(pr,{className:"text-center space-y-4",children:[l.jsx("div",{className:"mx-auto w-16 h-16 bg-green-500/10 rounded-full flex items-center justify-center",children:l.jsx(Oa,{className:"w-8 h-8 text-green-500"})}),l.jsx(hr,{className:"text-2xl font-bold text-foreground",children:"🎉 Bot Ativo e Funcionando!"}),l.jsxs(mr,{className:"text-muted-foreground",children:["Parabéns ",l.jsx("strong",{children:i}),"! Seu bot de vendas está online"]})]}),l.jsxs(Me,{className:"space-y-6",children:[l.jsxs("div",{className:"space-y-3",children:[l.jsx("h3",{className:"text-lg font-medium text-foreground",children:"Aplicações de dunel:"}),l.jsxs("div",{className:"bg-gray-800 border border-gray-700 rounded-lg p-4 space-y-3",children:[l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{className:"flex items-center gap-3",children:[l.jsx("div",{className:"w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center",children:l.jsx(je,{className:"w-6 h-6 text-gray-400"})}),l.jsxs("div",{children:[l.jsx("h4",{className:"font-medium text-white",children:"Vendas do Dune"}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),l.jsx("span",{className:"text-sm text-green-400",children:"Online"})]})]})]}),l.jsx(je,{className:"w-6 h-6 text-gray-400"})]}),l.jsxs("div",{className:"space-y-1 text-sm text-gray-300",children:[l.jsxs("div",{children:["ID: ",a]}),l.jsxs("div",{children:["Plano: ",l.jsx("span",{className:"text-green-400",children:"FREE"})]}),l.jsxs("div",{children:["Tarifa: ",l.jsx("span",{className:"text-yellow-400",children:"3"})]}),l.jsxs("div",{children:["Cluster: ",l.jsx("span",{className:"text-blue-400",children:"CLF004"})]}),l.jsxs("div",{children:["Última venda: ",new Date().toLocaleDateString("pt-BR")]})]}),l.jsx("div",{className:"pt-2 border-t border-gray-700",children:l.jsx("button",{className:"text-sm text-blue-400 hover:text-blue-300 flex items-center gap-1",children:"+ Adicionar ao seu servidor"})}),l.jsxs("div",{className:"pt-2 border-t border-gray-700 space-y-2",children:[l.jsx("button",{className:"text-sm text-gray-400 hover:text-gray-300 flex items-center gap-1",children:"🔄 Resgatar plano"}),l.jsx("button",{className:"text-sm text-gray-400 hover:text-gray-300 flex items-center gap-1",children:"✏️ Editar dados"})]}),l.jsxs("div",{className:"flex gap-2 pt-2",children:[l.jsx("button",{className:"w-8 h-8 bg-green-600 hover:bg-green-700 rounded flex items-center justify-center",children:"▶️"}),l.jsx("button",{className:"w-8 h-8 bg-blue-600 hover:bg-blue-700 rounded flex items-center justify-center",children:"🔄"}),l.jsx("button",{className:"w-8 h-8 bg-yellow-600 hover:bg-yellow-700 rounded flex items-center justify-center",children:"⏸️"}),l.jsx("button",{className:"w-8 h-8 bg-red-600 hover:bg-red-700 rounded flex items-center justify-center",children:"🗑️"})]})]}),l.jsx("button",{className:"w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg flex items-center justify-center gap-2 font-medium",children:"Cadastrar uma nova aplicação"})]}),l.jsxs("div",{className:"bg-blue-500/10 border border-blue-500/20 rounded-lg p-4",children:[l.jsxs("div",{className:"flex items-center gap-2 text-blue-600 font-medium mb-2",children:[l.jsx(je,{className:"w-4 h-4"}),"Link para adicionar ao servidor:"]}),l.jsxs("div",{className:"flex gap-2",children:[l.jsx("input",{type:"text",value:c,readOnly:!0,className:"flex-1 px-3 py-2 bg-background border border-border rounded-md text-sm font-mono"}),l.jsx(ne,{size:"sm",variant:"outline",onClick:u,children:l.jsx(Rg,{className:"w-4 h-4"})})]})]}),l.jsxs("div",{className:"flex gap-3",children:[l.jsx(ne,{variant:"outline",onClick:()=>o("/"),className:"flex-1",children:"🏠 Voltar ao Início"}),l.jsxs(ne,{onClick:()=>window.open(c,"_blank"),className:"flex-1",children:[l.jsx(Da,{className:"w-4 h-4 mr-2"}),"Adicionar ao Servidor"]})]})]})]})})})};var _l="focusScope.autoFocusOnMount",Ol="focusScope.autoFocusOnUnmount",wp={bubbles:!1,cancelable:!0},fC="FocusScope",Uv=h.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:s,...i}=e,[a,c]=h.useState(null),u=vt(o),f=vt(s),p=h.useRef(null),d=Re(t,g=>c(g)),x=h.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;h.useEffect(()=>{if(r){let g=function(y){if(x.paused||!a)return;const w=y.target;a.contains(w)?p.current=w:gn(p.current,{select:!0})},S=function(y){if(x.paused||!a)return;const w=y.relatedTarget;w!==null&&(a.contains(w)||gn(p.current,{select:!0}))},v=function(y){if(document.activeElement===document.body)for(const E of y)E.removedNodes.length>0&&gn(a)};document.addEventListener("focusin",g),document.addEventListener("focusout",S);const m=new MutationObserver(v);return a&&m.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",g),document.removeEventListener("focusout",S),m.disconnect()}}},[r,a,x.paused]),h.useEffect(()=>{if(a){bp.add(x);const g=document.activeElement;if(!a.contains(g)){const v=new CustomEvent(_l,wp);a.addEventListener(_l,u),a.dispatchEvent(v),v.defaultPrevented||(pC(yC(Bv(a)),{select:!0}),document.activeElement===g&&gn(a))}return()=>{a.removeEventListener(_l,u),setTimeout(()=>{const v=new CustomEvent(Ol,wp);a.addEventListener(Ol,f),a.dispatchEvent(v),v.defaultPrevented||gn(g??document.body,{select:!0}),a.removeEventListener(Ol,f),bp.remove(x)},0)}}},[a,u,f,x]);const b=h.useCallback(g=>{if(!n&&!r||x.paused)return;const S=g.key==="Tab"&&!g.altKey&&!g.ctrlKey&&!g.metaKey,v=document.activeElement;if(S&&v){const m=g.currentTarget,[y,w]=hC(m);y&&w?!g.shiftKey&&v===w?(g.preventDefault(),n&&gn(y,{select:!0})):g.shiftKey&&v===y&&(g.preventDefault(),n&&gn(w,{select:!0})):v===m&&g.preventDefault()}},[n,r,x.paused]);return l.jsx(ye.div,{tabIndex:-1,...i,ref:d,onKeyDown:b})});Uv.displayName=fC;function pC(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(gn(r,{select:t}),document.activeElement!==n)return}function hC(e){const t=Bv(e),n=Sp(t,e),r=Sp(t.reverse(),e);return[n,r]}function Bv(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Sp(e,t){for(const n of e)if(!mC(n,{upTo:t}))return n}function mC(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function gC(e){return e instanceof HTMLInputElement&&"select"in e}function gn(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&gC(e)&&t&&e.select()}}var bp=vC();function vC(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=Ep(e,t),e.unshift(t)},remove(t){var n;e=Ep(e,t),(n=e[0])==null||n.resume()}}}function Ep(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function yC(e){return e.filter(t=>t.tagName!=="A")}var Dl=0;function xC(){h.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??kp()),document.body.insertAdjacentElement("beforeend",e[1]??kp()),Dl++,()=>{Dl===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Dl--}},[])}function kp(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var $t=function(){return $t=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(t[s]=n[s])}return t},$t.apply(this,arguments)};function Vv(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function wC(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,s;r<o;r++)(s||!(r in t))&&(s||(s=Array.prototype.slice.call(t,0,r)),s[r]=t[r]);return e.concat(s||Array.prototype.slice.call(t))}var Ai="right-scroll-bar-position",_i="width-before-scroll-bar",SC="with-scroll-bars-hidden",bC="--removed-body-scroll-bar-size";function Il(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function EC(e,t){var n=h.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var kC=typeof window<"u"?h.useLayoutEffect:h.useEffect,Cp=new WeakMap;function CC(e,t){var n=EC(null,function(r){return e.forEach(function(o){return Il(o,r)})});return kC(function(){var r=Cp.get(n);if(r){var o=new Set(r),s=new Set(e),i=n.current;o.forEach(function(a){s.has(a)||Il(a,null)}),s.forEach(function(a){o.has(a)||Il(a,i)})}Cp.set(n,e)},[e]),n}function NC(e){return e}function jC(e,t){t===void 0&&(t=NC);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(s){var i=t(s,r);return n.push(i),function(){n=n.filter(function(a){return a!==i})}},assignSyncMedium:function(s){for(r=!0;n.length;){var i=n;n=[],i.forEach(s)}n={push:function(a){return s(a)},filter:function(){return n}}},assignMedium:function(s){r=!0;var i=[];if(n.length){var a=n;n=[],a.forEach(s),i=n}var c=function(){var f=i;i=[],f.forEach(s)},u=function(){return Promise.resolve().then(c)};u(),n={push:function(f){i.push(f),u()},filter:function(f){return i=i.filter(f),n}}}};return o}function TC(e){e===void 0&&(e={});var t=jC(null);return t.options=$t({async:!0,ssr:!1},e),t}var Wv=function(e){var t=e.sideCar,n=Vv(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return h.createElement(r,$t({},n))};Wv.isSideCarExport=!0;function PC(e,t){return e.useMedium(t),Wv}var Hv=TC(),Ml=function(){},Ha=h.forwardRef(function(e,t){var n=h.useRef(null),r=h.useState({onScrollCapture:Ml,onWheelCapture:Ml,onTouchMoveCapture:Ml}),o=r[0],s=r[1],i=e.forwardProps,a=e.children,c=e.className,u=e.removeScrollBar,f=e.enabled,p=e.shards,d=e.sideCar,x=e.noIsolation,b=e.inert,g=e.allowPinchZoom,S=e.as,v=S===void 0?"div":S,m=e.gapMode,y=Vv(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),w=d,E=CC([n,t]),C=$t($t({},y),o);return h.createElement(h.Fragment,null,f&&h.createElement(w,{sideCar:Hv,removeScrollBar:u,shards:p,noIsolation:x,inert:b,setCallbacks:s,allowPinchZoom:!!g,lockRef:n,gapMode:m}),i?h.cloneElement(h.Children.only(a),$t($t({},C),{ref:E})):h.createElement(v,$t({},C,{className:c,ref:E}),a))});Ha.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Ha.classNames={fullWidth:_i,zeroRight:Ai};var RC=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function AC(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=RC();return t&&e.setAttribute("nonce",t),e}function _C(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function OC(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var DC=function(){var e=0,t=null;return{add:function(n){e==0&&(t=AC())&&(_C(t,n),OC(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},IC=function(){var e=DC();return function(t,n){h.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Kv=function(){var e=IC(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},MC={left:0,top:0,right:0,gap:0},Ll=function(e){return parseInt(e||"",10)||0},LC=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[Ll(n),Ll(r),Ll(o)]},zC=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return MC;var t=LC(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},FC=Kv(),Xr="data-scroll-locked",$C=function(e,t,n,r){var o=e.left,s=e.top,i=e.right,a=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(SC,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(a,"px ").concat(r,`;
  }
  body[`).concat(Xr,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(s,`px;
    padding-right: `).concat(i,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(a,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Ai,` {
    right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(_i,` {
    margin-right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(Ai," .").concat(Ai,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(_i," .").concat(_i,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(Xr,`] {
    `).concat(bC,": ").concat(a,`px;
  }
`)},Np=function(){var e=parseInt(document.body.getAttribute(Xr)||"0",10);return isFinite(e)?e:0},UC=function(){h.useEffect(function(){return document.body.setAttribute(Xr,(Np()+1).toString()),function(){var e=Np()-1;e<=0?document.body.removeAttribute(Xr):document.body.setAttribute(Xr,e.toString())}},[])},BC=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;UC();var s=h.useMemo(function(){return zC(o)},[o]);return h.createElement(FC,{styles:$C(s,!t,o,n?"":"!important")})},Zc=!1;if(typeof window<"u")try{var di=Object.defineProperty({},"passive",{get:function(){return Zc=!0,!0}});window.addEventListener("test",di,di),window.removeEventListener("test",di,di)}catch{Zc=!1}var Nr=Zc?{passive:!1}:!1,VC=function(e){return e.tagName==="TEXTAREA"},Qv=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!VC(e)&&n[t]==="visible")},WC=function(e){return Qv(e,"overflowY")},HC=function(e){return Qv(e,"overflowX")},jp=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=Gv(e,r);if(o){var s=Yv(e,r),i=s[1],a=s[2];if(i>a)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},KC=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},QC=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},Gv=function(e,t){return e==="v"?WC(t):HC(t)},Yv=function(e,t){return e==="v"?KC(t):QC(t)},GC=function(e,t){return e==="h"&&t==="rtl"?-1:1},YC=function(e,t,n,r,o){var s=GC(e,window.getComputedStyle(t).direction),i=s*r,a=n.target,c=t.contains(a),u=!1,f=i>0,p=0,d=0;do{var x=Yv(e,a),b=x[0],g=x[1],S=x[2],v=g-S-s*b;(b||v)&&Gv(e,a)&&(p+=v,d+=b),a instanceof ShadowRoot?a=a.host:a=a.parentNode}while(!c&&a!==document.body||c&&(t.contains(a)||t===a));return(f&&(Math.abs(p)<1||!o)||!f&&(Math.abs(d)<1||!o))&&(u=!0),u},fi=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Tp=function(e){return[e.deltaX,e.deltaY]},Pp=function(e){return e&&"current"in e?e.current:e},XC=function(e,t){return e[0]===t[0]&&e[1]===t[1]},qC=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},ZC=0,jr=[];function JC(e){var t=h.useRef([]),n=h.useRef([0,0]),r=h.useRef(),o=h.useState(ZC++)[0],s=h.useState(Kv)[0],i=h.useRef(e);h.useEffect(function(){i.current=e},[e]),h.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var g=wC([e.lockRef.current],(e.shards||[]).map(Pp),!0).filter(Boolean);return g.forEach(function(S){return S.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),g.forEach(function(S){return S.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var a=h.useCallback(function(g,S){if("touches"in g&&g.touches.length===2||g.type==="wheel"&&g.ctrlKey)return!i.current.allowPinchZoom;var v=fi(g),m=n.current,y="deltaX"in g?g.deltaX:m[0]-v[0],w="deltaY"in g?g.deltaY:m[1]-v[1],E,C=g.target,k=Math.abs(y)>Math.abs(w)?"h":"v";if("touches"in g&&k==="h"&&C.type==="range")return!1;var j=jp(k,C);if(!j)return!0;if(j?E=k:(E=k==="v"?"h":"v",j=jp(k,C)),!j)return!1;if(!r.current&&"changedTouches"in g&&(y||w)&&(r.current=E),!E)return!0;var O=r.current||E;return YC(O,S,g,O==="h"?y:w,!0)},[]),c=h.useCallback(function(g){var S=g;if(!(!jr.length||jr[jr.length-1]!==s)){var v="deltaY"in S?Tp(S):fi(S),m=t.current.filter(function(E){return E.name===S.type&&(E.target===S.target||S.target===E.shadowParent)&&XC(E.delta,v)})[0];if(m&&m.should){S.cancelable&&S.preventDefault();return}if(!m){var y=(i.current.shards||[]).map(Pp).filter(Boolean).filter(function(E){return E.contains(S.target)}),w=y.length>0?a(S,y[0]):!i.current.noIsolation;w&&S.cancelable&&S.preventDefault()}}},[]),u=h.useCallback(function(g,S,v,m){var y={name:g,delta:S,target:v,should:m,shadowParent:e2(v)};t.current.push(y),setTimeout(function(){t.current=t.current.filter(function(w){return w!==y})},1)},[]),f=h.useCallback(function(g){n.current=fi(g),r.current=void 0},[]),p=h.useCallback(function(g){u(g.type,Tp(g),g.target,a(g,e.lockRef.current))},[]),d=h.useCallback(function(g){u(g.type,fi(g),g.target,a(g,e.lockRef.current))},[]);h.useEffect(function(){return jr.push(s),e.setCallbacks({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:d}),document.addEventListener("wheel",c,Nr),document.addEventListener("touchmove",c,Nr),document.addEventListener("touchstart",f,Nr),function(){jr=jr.filter(function(g){return g!==s}),document.removeEventListener("wheel",c,Nr),document.removeEventListener("touchmove",c,Nr),document.removeEventListener("touchstart",f,Nr)}},[]);var x=e.removeScrollBar,b=e.inert;return h.createElement(h.Fragment,null,b?h.createElement(s,{styles:qC(o)}):null,x?h.createElement(BC,{gapMode:e.gapMode}):null)}function e2(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const t2=PC(Hv,JC);var Xv=h.forwardRef(function(e,t){return h.createElement(Ha,$t({},e,{ref:t,sideCar:t2}))});Xv.classNames=Ha.classNames;var n2=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Tr=new WeakMap,pi=new WeakMap,hi={},zl=0,qv=function(e){return e&&(e.host||qv(e.parentNode))},r2=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=qv(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},o2=function(e,t,n,r){var o=r2(t,Array.isArray(e)?e:[e]);hi[n]||(hi[n]=new WeakMap);var s=hi[n],i=[],a=new Set,c=new Set(o),u=function(p){!p||a.has(p)||(a.add(p),u(p.parentNode))};o.forEach(u);var f=function(p){!p||c.has(p)||Array.prototype.forEach.call(p.children,function(d){if(a.has(d))f(d);else try{var x=d.getAttribute(r),b=x!==null&&x!=="false",g=(Tr.get(d)||0)+1,S=(s.get(d)||0)+1;Tr.set(d,g),s.set(d,S),i.push(d),g===1&&b&&pi.set(d,!0),S===1&&d.setAttribute(n,"true"),b||d.setAttribute(r,"true")}catch(v){console.error("aria-hidden: cannot operate on ",d,v)}})};return f(t),a.clear(),zl++,function(){i.forEach(function(p){var d=Tr.get(p)-1,x=s.get(p)-1;Tr.set(p,d),s.set(p,x),d||(pi.has(p)||p.removeAttribute(r),pi.delete(p)),x||p.removeAttribute(n)}),zl--,zl||(Tr=new WeakMap,Tr=new WeakMap,pi=new WeakMap,hi={})}},s2=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=n2(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),o2(r,o,n,"aria-hidden")):function(){return null}},md="Dialog",[Zv,Jv]=ja(md),[i2,At]=Zv(md),e0=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:s,modal:i=!0}=e,a=h.useRef(null),c=h.useRef(null),[u=!1,f]=Xu({prop:r,defaultProp:o,onChange:s});return l.jsx(i2,{scope:t,triggerRef:a,contentRef:c,contentId:Pi(),titleId:Pi(),descriptionId:Pi(),open:u,onOpenChange:f,onOpenToggle:h.useCallback(()=>f(p=>!p),[f]),modal:i,children:n})};e0.displayName=md;var t0="DialogTrigger",n0=h.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=At(t0,n),s=Re(t,o.triggerRef);return l.jsx(ye.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":yd(o.open),...r,ref:s,onClick:ae(e.onClick,o.onOpenToggle)})});n0.displayName=t0;var gd="DialogPortal",[a2,r0]=Zv(gd,{forceMount:void 0}),o0=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,s=At(gd,t);return l.jsx(a2,{scope:t,forceMount:n,children:h.Children.map(r,i=>l.jsx(ko,{present:n||s.open,children:l.jsx(Yu,{asChild:!0,container:o,children:i})}))})};o0.displayName=gd;var ua="DialogOverlay",s0=h.forwardRef((e,t)=>{const n=r0(ua,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,s=At(ua,e.__scopeDialog);return s.modal?l.jsx(ko,{present:r||s.open,children:l.jsx(l2,{...o,ref:t})}):null});s0.displayName=ua;var l2=h.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=At(ua,n);return l.jsx(Xv,{as:vo,allowPinchZoom:!0,shards:[o.contentRef],children:l.jsx(ye.div,{"data-state":yd(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),gr="DialogContent",i0=h.forwardRef((e,t)=>{const n=r0(gr,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,s=At(gr,e.__scopeDialog);return l.jsx(ko,{present:r||s.open,children:s.modal?l.jsx(c2,{...o,ref:t}):l.jsx(u2,{...o,ref:t})})});i0.displayName=gr;var c2=h.forwardRef((e,t)=>{const n=At(gr,e.__scopeDialog),r=h.useRef(null),o=Re(t,n.contentRef,r);return h.useEffect(()=>{const s=r.current;if(s)return s2(s)},[]),l.jsx(a0,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:ae(e.onCloseAutoFocus,s=>{var i;s.preventDefault(),(i=n.triggerRef.current)==null||i.focus()}),onPointerDownOutside:ae(e.onPointerDownOutside,s=>{const i=s.detail.originalEvent,a=i.button===0&&i.ctrlKey===!0;(i.button===2||a)&&s.preventDefault()}),onFocusOutside:ae(e.onFocusOutside,s=>s.preventDefault())})}),u2=h.forwardRef((e,t)=>{const n=At(gr,e.__scopeDialog),r=h.useRef(!1),o=h.useRef(!1);return l.jsx(a0,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{var i,a;(i=e.onCloseAutoFocus)==null||i.call(e,s),s.defaultPrevented||(r.current||(a=n.triggerRef.current)==null||a.focus(),s.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:s=>{var c,u;(c=e.onInteractOutside)==null||c.call(e,s),s.defaultPrevented||(r.current=!0,s.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const i=s.target;((u=n.triggerRef.current)==null?void 0:u.contains(i))&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&o.current&&s.preventDefault()}})}),a0=h.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:s,...i}=e,a=At(gr,n),c=h.useRef(null),u=Re(t,c);return xC(),l.jsxs(l.Fragment,{children:[l.jsx(Uv,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:s,children:l.jsx(Ta,{role:"dialog",id:a.contentId,"aria-describedby":a.descriptionId,"aria-labelledby":a.titleId,"data-state":yd(a.open),...i,ref:u,onDismiss:()=>a.onOpenChange(!1)})}),l.jsxs(l.Fragment,{children:[l.jsx(f2,{titleId:a.titleId}),l.jsx(h2,{contentRef:c,descriptionId:a.descriptionId})]})]})}),vd="DialogTitle",l0=h.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=At(vd,n);return l.jsx(ye.h2,{id:o.titleId,...r,ref:t})});l0.displayName=vd;var c0="DialogDescription",u0=h.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=At(c0,n);return l.jsx(ye.p,{id:o.descriptionId,...r,ref:t})});u0.displayName=c0;var d0="DialogClose",f0=h.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=At(d0,n);return l.jsx(ye.button,{type:"button",...r,ref:t,onClick:ae(e.onClick,()=>o.onOpenChange(!1))})});f0.displayName=d0;function yd(e){return e?"open":"closed"}var p0="DialogTitleWarning",[d2,h0]=Mw(p0,{contentName:gr,titleName:vd,docsSlug:"dialog"}),f2=({titleId:e})=>{const t=h0(p0),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return h.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},p2="DialogDescriptionWarning",h2=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${h0(p2).contentName}}.`;return h.useEffect(()=>{var s;const o=(s=e.current)==null?void 0:s.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},m2=e0,g2=n0,v2=o0,y2=s0,x2=i0,w2=l0,S2=u0,m0=f0,g0="AlertDialog",[b2,Y2]=ja(g0,[Jv]),sn=Jv(),v0=e=>{const{__scopeAlertDialog:t,...n}=e,r=sn(t);return l.jsx(m2,{...r,...n,modal:!0})};v0.displayName=g0;var E2="AlertDialogTrigger",y0=h.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=sn(n);return l.jsx(g2,{...o,...r,ref:t})});y0.displayName=E2;var k2="AlertDialogPortal",x0=e=>{const{__scopeAlertDialog:t,...n}=e,r=sn(t);return l.jsx(v2,{...r,...n})};x0.displayName=k2;var C2="AlertDialogOverlay",w0=h.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=sn(n);return l.jsx(y2,{...o,...r,ref:t})});w0.displayName=C2;var qr="AlertDialogContent",[N2,j2]=b2(qr),S0=h.forwardRef((e,t)=>{const{__scopeAlertDialog:n,children:r,...o}=e,s=sn(n),i=h.useRef(null),a=Re(t,i),c=h.useRef(null);return l.jsx(d2,{contentName:qr,titleName:b0,docsSlug:"alert-dialog",children:l.jsx(N2,{scope:n,cancelRef:c,children:l.jsxs(x2,{role:"alertdialog",...s,...o,ref:a,onOpenAutoFocus:ae(o.onOpenAutoFocus,u=>{var f;u.preventDefault(),(f=c.current)==null||f.focus({preventScroll:!0})}),onPointerDownOutside:u=>u.preventDefault(),onInteractOutside:u=>u.preventDefault(),children:[l.jsx(Gu,{children:r}),l.jsx(P2,{contentRef:i})]})})})});S0.displayName=qr;var b0="AlertDialogTitle",E0=h.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=sn(n);return l.jsx(w2,{...o,...r,ref:t})});E0.displayName=b0;var k0="AlertDialogDescription",C0=h.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=sn(n);return l.jsx(S2,{...o,...r,ref:t})});C0.displayName=k0;var T2="AlertDialogAction",N0=h.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=sn(n);return l.jsx(m0,{...o,...r,ref:t})});N0.displayName=T2;var j0="AlertDialogCancel",T0=h.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,{cancelRef:o}=j2(j0,n),s=sn(n),i=Re(t,o);return l.jsx(m0,{...s,...r,ref:i})});T0.displayName=j0;var P2=({contentRef:e})=>{const t=`\`${qr}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${qr}\` by passing a \`${k0}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${qr}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return h.useEffect(()=>{var r;document.getElementById((r=e.current)==null?void 0:r.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null},R2=v0,A2=y0,_2=x0,P0=w0,R0=S0,A0=N0,_0=T0,O0=E0,D0=C0;const O2=R2,D2=A2,I2=_2,I0=h.forwardRef(({className:e,...t},n)=>l.jsx(P0,{className:ce("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:n}));I0.displayName=P0.displayName;const M0=h.forwardRef(({className:e,...t},n)=>l.jsxs(I2,{children:[l.jsx(I0,{}),l.jsx(R0,{ref:n,className:ce("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t})]}));M0.displayName=R0.displayName;const L0=({className:e,...t})=>l.jsx("div",{className:ce("flex flex-col space-y-2 text-center sm:text-left",e),...t});L0.displayName="AlertDialogHeader";const z0=({className:e,...t})=>l.jsx("div",{className:ce("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});z0.displayName="AlertDialogFooter";const F0=h.forwardRef(({className:e,...t},n)=>l.jsx(O0,{ref:n,className:ce("text-lg font-semibold",e),...t}));F0.displayName=O0.displayName;const $0=h.forwardRef(({className:e,...t},n)=>l.jsx(D0,{ref:n,className:ce("text-sm text-muted-foreground",e),...t}));$0.displayName=D0.displayName;const U0=h.forwardRef(({className:e,...t},n)=>l.jsx(A0,{ref:n,className:ce(hd(),e),...t}));U0.displayName=A0.displayName;const B0=h.forwardRef(({className:e,...t},n)=>l.jsx(_0,{ref:n,className:ce(hd({variant:"outline"}),"mt-2 sm:mt-0",e),...t}));B0.displayName=_0.displayName;const nt={JWT_TOKEN:"jwtToken",SESSION_TOKEN:"sessionToken",URL_TOKEN:"urlToken",TOKEN_EXPIRES_AT:"tokenExpiresAt",USER_DATA:"userData"};function ns(e){if(e.jwtToken){localStorage.setItem(nt.JWT_TOKEN,e.jwtToken);const t=Date.now()+24*60*60*1e3;localStorage.setItem(nt.TOKEN_EXPIRES_AT,t.toString())}e.sessionToken&&localStorage.setItem(nt.SESSION_TOKEN,e.sessionToken),e.urlToken&&localStorage.setItem(nt.URL_TOKEN,e.urlToken)}function wo(){return{jwtToken:localStorage.getItem(nt.JWT_TOKEN)||void 0,sessionToken:localStorage.getItem(nt.SESSION_TOKEN)||void 0,urlToken:localStorage.getItem(nt.URL_TOKEN)||void 0,expiresAt:parseInt(localStorage.getItem(nt.TOKEN_EXPIRES_AT)||"0")||void 0}}function M2(){const e=wo();if(!e.jwtToken||!e.expiresAt)return!1;const t=Date.now()+5*60*1e3;return e.expiresAt>t}function L2(){const e=wo();if(!e.expiresAt)return!0;const t=Date.now()+60*60*1e3;return e.expiresAt<t}function V0(){localStorage.removeItem(nt.JWT_TOKEN),localStorage.removeItem(nt.SESSION_TOKEN),localStorage.removeItem(nt.URL_TOKEN),localStorage.removeItem(nt.TOKEN_EXPIRES_AT),localStorage.removeItem(nt.USER_DATA)}async function Zr(e,t={}){const n=wo(),r=new Headers(t.headers);if(n.jwtToken&&r.set("Authorization",`Bearer ${n.jwtToken}`),n.sessionToken&&r.set("X-Session-Token",n.sessionToken),!n.jwtToken&&!n.sessionToken){const s=new URLSearchParams(window.location.search).get("token");s&&r.set("X-Session-Token",s)}return fetch(e,{...t,headers:r})}async function z2(){try{const e=await Zr("/api/refresh-token",{method:"POST"});if(e.ok){const t=await e.json();if(t.success&&t.token)return ns({jwtToken:t.token}),!0}return!1}catch(e){return console.error("Erro ao renovar token:",e),!1}}async function Jc(){return M2()||L2()&&await z2()?!0:(V0(),!1)}function Jr(){V0(),window.location.href="/login"}const Rp=()=>{const[e,t]=h.useState([]),[n,r]=h.useState(!0),[o,s]=h.useState(null),[i,a]=h.useState(null),[c,u]=h.useState(new Set),f=Rt(),[p]=wr(),{urlToken:d}=Dv();h.useEffect(()=>{(async()=>{const E=p.get("token"),C=p.get("jwt");if(console.log("Dashboard useEffect - sessionToken da URL:",E),console.log("Dashboard useEffect - jwtToken da URL:",C?"JWT_PRESENT":"NO_JWT"),console.log("Dashboard useEffect - urlToken da URL:",d),E||C)ns({sessionToken:E||void 0,jwtToken:C||void 0,urlToken:d||void 0}),console.log("Tokens salvos no localStorage"),await Jc()?(await b(),d?f(`/dashboard/${d}`,{replace:!0}):await x()):(console.log("Falha na autenticação, redirecionando para login"),f("/login"));else{const k=wo();if(console.log("Tokens do localStorage:",{hasJWT:!!k.jwtToken,hasSession:!!k.sessionToken,hasUrl:!!k.urlToken}),k.jwtToken||k.sessionToken)if(await Jc()){if(k.urlToken&&!d){f(`/dashboard/${k.urlToken}`,{replace:!0});return}await b()}else console.log("Autenticação inválida, redirecionando para login"),f("/login");else console.log("Nenhum token encontrado, redirecionando para login"),f("/login")}})()},[p,f,d]);const x=async()=>{try{const w=await Zr("/api/user-full");if(w.ok){const E=await w.json();if(E.urlToken){ns({urlToken:E.urlToken}),f(`/dashboard/${E.urlToken}`,{replace:!0});return}}}catch(w){console.error("Erro ao buscar dados completos:",w)}},b=async()=>{try{console.log("Carregando informações do usuário...");const w=await Zr("/api/user-info");if(console.log("Resposta da API user-info:",w.status,w.statusText),w.ok){const E=await w.json();console.log("Dados do usuário recebidos:",E),s(E);const C=await Zr("/api/user-full");if(C.ok){const k=await C.json();if(a(k),k.urlToken){const j=wo();j.urlToken||ns({...j,urlToken:k.urlToken})}}g(E.id)}else console.log("Token inválido, fazendo logout"),Jr()}catch(w){console.error("Erro ao verificar autenticação:",w),Jr()}},g=async w=>{try{console.log("Carregando bots para usuário:",w);const E=await fetch(`/api/user-bots/${w}`);if(console.log("Resposta da API user-bots:",E.status,E.statusText),E.ok){const C=await E.json();console.log("Dados dos bots recebidos:",C),t(C.bots||[])}else console.error("Erro ao carregar bots - resposta não OK:",E.status)}catch(E){console.error("Erro ao carregar bots:",E)}finally{r(!1)}},S=async(w,E)=>{const k={start:{loading:"Iniciando bot...",success:"Bot iniciado com sucesso!",error:"Erro ao iniciar bot"},stop:{loading:"Parando bot...",success:"Bot parado com sucesso!",error:"Erro ao parar bot"},restart:{loading:"Reiniciando bot...",success:"Bot reiniciado com sucesso!",error:"Erro ao reiniciar bot"},delete:{loading:"Removendo bot...",success:"Bot removido com sucesso!",error:"Erro ao remover bot"}}[E];t(O=>O.map(_=>_.id===w?{..._,status:E==="start"?"starting":E==="stop"?"stopping":E==="restart"?"restarting":_.status}:_)),E==="restart"&&u(O=>new Set([...O,w]));const j=ft.loading(k.loading);try{const O=await fetch("/api/bot-action",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({botId:w,action:E})}),_=await O.json();O.ok&&_.success?(ft.success(k.success,{id:j}),E==="restart"&&setTimeout(()=>{u(F=>{const M=new Set(F);return M.delete(w),M})},2e3),setTimeout(()=>{o&&g(o.id)},E==="restart"?3e3:1500)):(ft.error(_.message||k.error,{id:j}),o&&g(o.id),E==="restart"&&u(F=>{const M=new Set(F);return M.delete(w),M}))}catch(O){console.error("Erro ao executar ação:",O),ft.error(k.error,{id:j}),o&&g(o.id),E==="restart"&&u(_=>{const F=new Set(_);return F.delete(w),F})}},v=w=>{switch(w){case"online":return"text-green-400";case"offline":return"text-gray-400";case"error":return"text-red-400";case"starting":return"text-yellow-400";case"stopping":return"text-orange-400";case"restarting":return"text-blue-400";default:return"text-gray-400"}},m=w=>{switch(w){case"online":return"bg-green-500";case"offline":return"bg-gray-500";case"error":return"bg-red-500";case"starting":return"bg-yellow-500 animate-pulse";case"stopping":return"bg-orange-500 animate-pulse";case"restarting":return"bg-blue-500 animate-pulse";default:return"bg-gray-500"}},y=w=>{switch(w){case"online":return"Online";case"offline":return"Offline";case"error":return"Erro";case"starting":return"Iniciando...";case"stopping":return"Parando...";case"restarting":return"Reiniciando...";default:return"Desconhecido"}};return n?l.jsx("div",{className:"dark min-h-screen bg-background flex items-center justify-center",children:l.jsxs("div",{className:"text-center",children:[l.jsx(je,{className:"w-12 h-12 text-primary mx-auto mb-4 animate-pulse"}),l.jsx("p",{className:"text-muted-foreground",children:"Carregando dashboard..."})]})}):l.jsx(yv,{children:l.jsx("div",{className:"dark min-h-screen bg-background",children:l.jsxs("div",{className:"container mx-auto px-4 py-8",children:[l.jsxs("div",{className:"flex items-center justify-between mb-8",children:[l.jsxs("div",{children:[l.jsx("h1",{className:"text-3xl font-bold text-foreground",children:"Dashboard"}),l.jsxs("p",{className:"text-muted-foreground",children:["Bem-vindo, ",l.jsx("strong",{children:(o==null?void 0:o.username)||"Usuário"}),"! Gerencie seus bots de vendas"]})]}),l.jsxs("div",{className:"flex gap-3",children:[l.jsxs(ne,{variant:"outline",onClick:()=>{i!=null&&i.urlToken?f(`/extrato/${i.urlToken}`):f("/extrato")},className:"border-green-600 text-green-600 hover:bg-green-600 hover:text-white transition-all duration-200",children:[l.jsx(Ur,{className:"mr-2 h-4 w-4"}),"Extrato Nodex Pay"]}),l.jsx(ne,{variant:"ghost",onClick:()=>{s(null),a(null),t([]),Jr()},className:"text-gray-400 hover:text-red-400 hover:bg-transparent transition-colors duration-200",children:l.jsx(_g,{className:"w-4 h-4"})}),l.jsxs(ne,{onClick:()=>{const w=i!=null&&i.urlToken?`/configure?username=${o==null?void 0:o.username}&userId=${o==null?void 0:o.id}&urlToken=${i.urlToken}`:`/configure?username=${o==null?void 0:o.username}&userId=${o==null?void 0:o.id}`;f(w)},className:"bg-[#5865F2] hover:bg-[#4752C4]",children:[l.jsx(Bf,{className:"mr-2 h-4 w-4"}),"Criar Novo Bot"]})]})]}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[l.jsx(Ie,{className:"border-border bg-card",children:l.jsx(Me,{className:"p-6",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("p",{className:"text-sm text-muted-foreground",children:"Total de Bots"}),l.jsx("p",{className:"text-2xl font-bold text-foreground",children:e.length})]}),l.jsx(je,{className:"w-8 h-8 text-primary"})]})})}),l.jsx(Ie,{className:"border-border bg-card",children:l.jsx(Me,{className:"p-6",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("p",{className:"text-sm text-muted-foreground",children:"Bots Online"}),l.jsx("p",{className:"text-2xl font-bold text-green-400",children:e.filter(w=>w.status==="online").length})]}),l.jsx(Uf,{className:"w-8 h-8 text-green-400"})]})})}),l.jsx(Ie,{className:"border-border bg-card",children:l.jsx(Me,{className:"p-6",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("p",{className:"text-sm text-muted-foreground",children:"Total Servidores"}),l.jsx("p",{className:"text-2xl font-bold text-blue-400",children:e.reduce((w,E)=>w+E.servers,0)})]}),l.jsx(I1,{className:"w-8 h-8 text-blue-400"})]})})}),l.jsx(Ie,{className:"border-border bg-card",children:l.jsx(Me,{className:"p-6",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("p",{className:"text-sm text-muted-foreground",children:"Total Vendas"}),l.jsx("p",{className:"text-2xl font-bold text-yellow-400",children:e.reduce((w,E)=>w+E.sales,0)})]}),l.jsx(Dg,{className:"w-8 h-8 text-yellow-400"})]})})})]}),l.jsxs(Ie,{className:"border-border bg-card",children:[l.jsxs(pr,{children:[l.jsxs(hr,{className:"flex items-center gap-2",children:[l.jsx(je,{className:"w-5 h-5"}),"Aplicações de ",(o==null?void 0:o.username)||"usuário"]}),l.jsx(mr,{children:"Gerencie todos os seus bots de vendas em um só lugar"})]}),l.jsx(Me,{className:"space-y-4",children:e.length===0?l.jsxs("div",{className:"text-center py-12",children:[l.jsx(je,{className:"w-16 h-16 text-muted-foreground mx-auto mb-4"}),l.jsx("h3",{className:"text-lg font-medium text-foreground mb-2",children:"Nenhuma aplicação configurada"}),l.jsx("p",{className:"text-muted-foreground mb-4",children:"Crie sua primeira aplicação de vendas para começar"}),l.jsxs(ne,{onClick:()=>{const w=i!=null&&i.urlToken?`/configure?username=${o==null?void 0:o.username}&userId=${o==null?void 0:o.id}&urlToken=${i.urlToken}`:`/configure?username=${o==null?void 0:o.username}&userId=${o==null?void 0:o.id}`;f(w)},className:"bg-green-600 hover:bg-green-700 text-white",children:[l.jsx(Bf,{className:"mr-2 h-4 w-4"}),"Cadastrar uma nova aplicação"]})]}):e.map(w=>l.jsxs("div",{className:"bg-gray-800 border border-gray-700 rounded-lg p-4",children:[l.jsxs("div",{className:"flex items-center justify-between mb-4",children:[l.jsxs("div",{className:"flex items-center gap-3",children:[l.jsx("div",{className:"w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center",children:l.jsx(je,{className:"w-6 h-6 text-gray-400"})}),l.jsxs("div",{children:[l.jsx("h4",{className:"font-medium text-white",children:w.name}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx("div",{className:`w-2 h-2 rounded-full ${m(w.status)}`}),l.jsx("span",{className:`text-sm ${v(w.status)}`,children:y(w.status)})]})]})]}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsxs(li,{children:[l.jsx(ci,{asChild:!0,children:l.jsx(ne,{size:"sm",onClick:()=>S(w.id,w.status==="online"?"stop":"start"),className:w.status==="online"?"bg-red-600 hover:bg-red-700 text-white transition-all duration-200":"bg-green-600 hover:bg-green-700 text-white transition-all duration-200",children:w.status==="online"?l.jsx(R1,{className:"w-4 h-4"}):l.jsx(Uf,{className:"w-4 h-4"})})}),l.jsx(Ho,{children:w.status==="online"?"Parar bot":"Iniciar bot"})]}),l.jsxs(li,{children:[l.jsx(ci,{asChild:!0,children:l.jsx(ne,{size:"sm",onClick:()=>S(w.id,"restart"),className:"bg-blue-600 hover:bg-blue-700 text-white transition-all duration-200",children:l.jsx(A1,{className:`w-4 h-4 ${c.has(w.id)?"animate-spin-slow":""}`})})}),l.jsx(Ho,{children:"Reiniciar bot"})]}),l.jsxs(li,{children:[l.jsx(ci,{asChild:!0,children:l.jsx(ne,{size:"sm",onClick:()=>window.open(`https://discord.com/api/oauth2/authorize?client_id=${w.clientId}&permissions=2048&scope=bot`,"_blank"),className:"bg-gray-600 hover:bg-gray-700 text-white transition-all duration-200",children:l.jsx(Da,{className:"w-4 h-4"})})}),l.jsx(Ho,{children:"Convidar para servidor"})]}),l.jsxs(O2,{children:[l.jsxs(li,{children:[l.jsx(ci,{asChild:!0,children:l.jsx(D2,{asChild:!0,children:l.jsx(ne,{size:"sm",className:"bg-red-600 hover:bg-red-700 text-white transition-all duration-200",children:l.jsx(Vf,{className:"w-4 h-4"})})})}),l.jsx(Ho,{children:"Remover aplicação"})]}),l.jsxs(M0,{className:"bg-gray-900 border-gray-700",children:[l.jsxs(L0,{children:[l.jsxs(F0,{className:"flex items-center gap-2 text-red-400",children:[l.jsx(O1,{className:"w-5 h-5"}),"Confirmar Exclusão da Aplicação"]}),l.jsxs($0,{className:"text-gray-300 space-y-2",children:[l.jsxs("p",{children:["Você está prestes a ",l.jsx("strong",{className:"text-red-400",children:"excluir permanentemente"})," a aplicação ",l.jsxs("strong",{className:"text-white",children:['"',w.name,'"']}),"."]}),l.jsx("p",{className:"text-sm text-gray-400",children:"Esta ação irá:"}),l.jsxs("ul",{className:"text-sm text-gray-400 list-disc list-inside space-y-1 ml-4",children:[l.jsx("li",{children:"Desconectar o bot de todos os servidores"}),l.jsx("li",{children:"Remover todas as configurações e dados"}),l.jsx("li",{children:"Cancelar todas as vendas em andamento"}),l.jsx("li",{children:"Apagar o histórico de transações"})]}),l.jsx("p",{className:"text-sm font-medium text-yellow-400 mt-3",children:"⚠️ Esta ação não pode ser desfeita."})]})]}),l.jsxs(z0,{className:"gap-2",children:[l.jsx(B0,{className:"bg-gray-700 hover:bg-gray-600 text-white border-gray-600",children:"Cancelar"}),l.jsxs(U0,{onClick:()=>S(w.id,"delete"),className:"bg-red-600 hover:bg-red-700 text-white",children:[l.jsx(Vf,{className:"w-4 h-4 mr-2"}),"Excluir Aplicação"]})]})]})]})]})]}),l.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 text-sm text-gray-300",children:[l.jsxs("div",{children:[l.jsx("span",{className:"text-gray-400",children:"ID:"})," ",w.clientId.slice(0,8),"..."]}),l.jsxs("div",{children:[l.jsx("span",{className:"text-gray-400",children:"Plano:"})," ",l.jsx("span",{className:"text-green-400",children:w.plan})]}),l.jsxs("div",{children:[l.jsx("span",{className:"text-gray-400",children:"Servidores:"})," ",l.jsx("span",{className:"text-blue-400",children:w.servers})]}),l.jsxs("div",{children:[l.jsx("span",{className:"text-gray-400",children:"Vendas:"})," ",l.jsx("span",{className:"text-yellow-400",children:w.sales})]}),l.jsxs("div",{children:[l.jsx("span",{className:"text-gray-400",children:"Última venda:"})," ",w.lastSale]})]})]},w.id))})]})]})})})},F2=_a("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function $2({className:e,variant:t,...n}){return l.jsx("div",{className:ce(F2({variant:t}),e),...n})}const Ap=()=>{const[e,t]=h.useState(null),[n,r]=h.useState(""),[o,s]=h.useState(!0),[i,a]=h.useState(null),c=Rt(),{urlToken:u}=Dv(),[f]=wr();h.useEffect(()=>{(async()=>{const w=f.get("token"),E=f.get("jwt");if((w||E)&&ns({sessionToken:w||void 0,jwtToken:E||void 0,urlToken:u||void 0}),await Jc()){await p();const k=wo();if(k.urlToken&&!u){c(`/extrato/${k.urlToken}`,{replace:!0});return}}else console.log("Autenticação inválida, redirecionando para login"),c("/login")})()},[f,c,u]);const p=async()=>{try{const y=await Zr("/api/user-info");if(y.ok){const w=await y.json();a(w),d(w.id)}else console.log("Token inválido, fazendo logout"),Jr()}catch(y){console.error("Erro ao verificar autenticação:",y),Jr()}},d=async y=>{try{const w=await Zr(`/api/extrato/${y}`);if(w.ok){const E=await w.json();t(E),E.servers.length>0&&r(E.servers[0].id)}else w.status===401?Jr():ft.error("Erro ao carregar dados do extrato")}catch(w){console.error("Erro ao carregar extrato:",w),ft.error("Erro ao carregar dados do extrato")}finally{s(!1)}},x=async y=>{const w=e==null?void 0:e.servers.find(E=>E.id===y);if(!w||w.balance<=0){ft.error("Saldo insuficiente para saque");return}ft.loading("Processando solicitação de saque..."),setTimeout(()=>{ft.success("Solicitação de saque enviada com sucesso!"),i&&d(i.id)},2e3)},b=y=>{switch(y){case"completed":return l.jsx(Oa,{className:"w-4 h-4 text-green-500"});case"pending":return l.jsx(Nl,{className:"w-4 h-4 text-yellow-500"});case"failed":return l.jsx(j1,{className:"w-4 h-4 text-red-500"});default:return l.jsx(Nl,{className:"w-4 h-4 text-gray-500"})}},g=y=>{switch(y){case"completed":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"failed":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},S=y=>new Intl.NumberFormat("pt-BR",{style:"currency",currency:"BRL"}).format(y),v=y=>new Date(y).toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"});if(o)return l.jsx("div",{className:"dark min-h-screen bg-background flex items-center justify-center",children:l.jsxs("div",{className:"text-center",children:[l.jsx(Ur,{className:"w-12 h-12 text-primary mx-auto mb-4 animate-pulse"}),l.jsx("p",{className:"text-muted-foreground",children:"Carregando extrato..."})]})});const m=e==null?void 0:e.servers.find(y=>y.id===n);return l.jsx("div",{className:"dark min-h-screen bg-background",children:l.jsxs("div",{className:"container mx-auto px-4 py-8",children:[l.jsx("div",{className:"flex items-center justify-between mb-8",children:l.jsxs("div",{className:"flex items-center gap-4",children:[l.jsxs(ne,{variant:"ghost",onClick:()=>{c(u?`/dashboard/${u}`:"/dashboard")},className:"text-gray-400 hover:text-white",children:[l.jsx(b1,{className:"w-4 h-4 mr-2"}),"Voltar ao Dashboard"]}),l.jsxs("div",{children:[l.jsx("h1",{className:"text-3xl font-bold text-foreground",children:"Extrato Nodex Pay"}),l.jsx("p",{className:"text-muted-foreground",children:"Gerencie seus saques e visualize o histórico de transações"})]})]})}),e&&e.servers.length>0&&l.jsxs(Ie,{className:"border-border bg-card mb-6",children:[l.jsxs(pr,{children:[l.jsx(hr,{children:"Selecionar Servidor"}),l.jsx(mr,{children:"Escolha o servidor para visualizar o extrato"})]}),l.jsx(Me,{children:l.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:e.servers.map(y=>l.jsx(Ie,{className:`cursor-pointer transition-all duration-200 ${n===y.id?"border-primary bg-primary/10":"border-border hover:border-primary/50"}`,onClick:()=>r(y.id),children:l.jsxs(Me,{className:"p-4",children:[l.jsx("h3",{className:"font-medium text-foreground mb-2",children:y.name}),l.jsxs("div",{className:"space-y-1 text-sm",children:[l.jsxs("div",{className:"flex justify-between",children:[l.jsx("span",{className:"text-muted-foreground",children:"Saldo:"}),l.jsx("span",{className:"text-green-400 font-medium",children:S(y.balance)})]}),l.jsxs("div",{className:"flex justify-between",children:[l.jsx("span",{className:"text-muted-foreground",children:"Pendente:"}),l.jsx("span",{className:"text-yellow-400 font-medium",children:S(y.pendingWithdrawals)})]})]})]})},y.id))})})]}),m&&l.jsxs(l.Fragment,{children:[l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[l.jsx(Ie,{className:"border-border bg-card",children:l.jsxs(Me,{className:"p-6",children:[l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("p",{className:"text-sm text-muted-foreground",children:"Saldo Disponível"}),l.jsx("p",{className:"text-2xl font-bold text-green-400",children:S(m.balance)})]}),l.jsx(Wf,{className:"w-8 h-8 text-green-400"})]}),l.jsxs(ne,{onClick:()=>x(m.id),disabled:m.balance<=0,className:"w-full mt-4 bg-green-600 hover:bg-green-700",children:[l.jsx(T1,{className:"w-4 h-4 mr-2"}),"Solicitar Saque"]})]})}),l.jsx(Ie,{className:"border-border bg-card",children:l.jsx(Me,{className:"p-6",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("p",{className:"text-sm text-muted-foreground",children:"Saques Pendentes"}),l.jsx("p",{className:"text-2xl font-bold text-yellow-400",children:S(m.pendingWithdrawals)})]}),l.jsx(Nl,{className:"w-8 h-8 text-yellow-400"})]})})}),l.jsx(Ie,{className:"border-border bg-card",children:l.jsx(Me,{className:"p-6",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("p",{className:"text-sm text-muted-foreground",children:"Total Transações"}),l.jsx("p",{className:"text-2xl font-bold text-blue-400",children:m.transactions.length})]}),l.jsx(Ur,{className:"w-8 h-8 text-blue-400"})]})})})]}),l.jsxs(Ie,{className:"border-border bg-card",children:[l.jsxs(pr,{children:[l.jsx(hr,{children:"Histórico de Transações"}),l.jsxs(mr,{children:["Últimas movimentações do servidor ",m.name]})]}),l.jsx(Me,{children:m.transactions.length===0?l.jsxs("div",{className:"text-center py-8",children:[l.jsx(Ur,{className:"w-16 h-16 text-muted-foreground mx-auto mb-4"}),l.jsx("h3",{className:"text-lg font-medium text-foreground mb-2",children:"Nenhuma transação encontrada"}),l.jsx("p",{className:"text-muted-foreground",children:"As transações aparecerão aqui quando houver atividade"})]}):l.jsx("div",{className:"space-y-4",children:m.transactions.map(y=>l.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-800 border border-gray-700 rounded-lg",children:[l.jsxs("div",{className:"flex items-center gap-4",children:[l.jsx("div",{className:"w-10 h-10 bg-gray-700 rounded-lg flex items-center justify-center",children:y.type==="sale"?l.jsx(Wf,{className:"w-5 h-5 text-green-400"}):l.jsx(_1,{className:"w-5 h-5 text-red-400"})}),l.jsxs("div",{children:[l.jsx("p",{className:"font-medium text-white",children:y.description}),l.jsx("p",{className:"text-sm text-gray-400",children:v(y.date)})]})]}),l.jsx("div",{className:"flex items-center gap-4",children:l.jsxs("div",{className:"text-right",children:[l.jsxs("p",{className:`font-medium ${y.amount>0?"text-green-400":"text-red-400"}`,children:[y.amount>0?"+":"",S(y.amount)]}),l.jsxs("div",{className:"flex items-center gap-2",children:[b(y.status),l.jsx($2,{className:g(y.status),children:y.status==="completed"?"Concluído":y.status==="pending"?"Pendente":"Falhou"})]})]})})]},y.id))})})]})]}),e&&e.servers.length===0&&l.jsx(Ie,{className:"border-border bg-card",children:l.jsxs(Me,{className:"p-12 text-center",children:[l.jsx(Ur,{className:"w-16 h-16 text-muted-foreground mx-auto mb-4"}),l.jsx("h3",{className:"text-lg font-medium text-foreground mb-2",children:"Nenhum servidor encontrado"}),l.jsx("p",{className:"text-muted-foreground mb-4",children:"Você precisa ter pelo menos um bot ativo para visualizar extratos"}),l.jsx(ne,{onClick:()=>{c(u?`/dashboard/${u}`:"/dashboard")},className:"bg-primary hover:bg-primary/90",children:"Voltar ao Dashboard"})]})})]})})},U2=()=>{const e=Po();return h.useEffect(()=>{console.error("404 Error: User attempted to access non-existent route:",e.pathname)},[e.pathname]),l.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:l.jsxs("div",{className:"text-center",children:[l.jsx("h1",{className:"text-4xl font-bold mb-4",children:"404"}),l.jsx("p",{className:"text-xl text-gray-600 mb-4",children:"Oops! Page not found"}),l.jsx("a",{href:"/",className:"text-blue-500 hover:text-blue-700 underline",children:"Return to Home"})]})})},_p=()=>{const[e]=wr(),t=Rt(),[n,r]=h.useState("loading"),[o,s]=h.useState("Processando login...");return h.useEffect(()=>{(async()=>{try{const a=e.get("error"),c=e.get("token"),u=e.get("urlToken");if(a)throw new Error(`Erro de autorização: ${a}`);s("Processando autenticação...");let f;if(c&&u)f={sessionToken:c,urlToken:u},localStorage.setItem("sessionToken",c),localStorage.setItem("urlToken",u);else throw new Error("Tokens de autenticação não encontrados");r("success");const p=localStorage.getItem("selectedPlan");if(s(p?"Login realizado! Processando plano...":"Login realizado com sucesso!"),p){if(s("Processando plano selecionado..."),localStorage.removeItem("selectedPlan"),p==="basic")try{const d=await fetch("/api/stripe/create-checkout-session",{method:"POST",headers:{"Content-Type":"application/json","x-session-token":f.sessionToken},body:JSON.stringify({priceId:"price_1RdYzeRIzvHRKrKIhNsADY81",plan:"basic",successUrl:`${window.location.origin}/success`,cancelUrl:`${window.location.origin}/pricing`})});if(d.ok){const{sessionId:x}=await d.json(),b=await Yc(()=>import("./index-CtFyKYG2.js"),[]).then(g=>g.loadStripe("pk_test_51RdYpaRIzvHRKrKI5lpipw0j0NqSDi7VP4yn30XKkFt9yOICAeEeWCuHhwm3d8qb5mWmE0J4fn6pBOPAzNNOmu4U00lLhT8skQ"));if(b){await b.redirectToCheckout({sessionId:x});return}}}catch(d){console.error("Erro ao processar pagamento Basic:",d)}else if(p==="pro")try{const d=await fetch("/api/stripe/create-checkout-session",{method:"POST",headers:{"Content-Type":"application/json","x-session-token":f.sessionToken},body:JSON.stringify({priceId:"price_1RdZ0ORIzvHRKrKIt3O227mH",plan:"pro",successUrl:`${window.location.origin}/success`,cancelUrl:`${window.location.origin}/pricing`})});if(d.ok){const{sessionId:x}=await d.json(),b=await Yc(()=>import("./index-CtFyKYG2.js"),[]).then(g=>g.loadStripe("pk_test_51RdYpaRIzvHRKrKI5lpipw0j0NqSDi7VP4yn30XKkFt9yOICAeEeWCuHhwm3d8qb5mWmE0J4fn6pBOPAzNNOmu4U00lLhT8skQ"));if(b){await b.redirectToCheckout({sessionId:x});return}}}catch(d){console.error("Erro ao processar pagamento Pro:",d)}}else{setTimeout(()=>{f.urlToken?t(`/dashboard/${f.urlToken}`):t("/dashboard")},2e3);return}}catch(a){console.error("Erro no callback:",a),r("error"),s(a instanceof Error?a.message:"Erro desconhecido"),setTimeout(()=>{t("/")},3e3)}})()},[e,t]),l.jsx("div",{className:"dark min-h-screen bg-background flex items-center justify-center",children:l.jsxs("div",{className:"text-center space-y-4",children:[n==="loading"&&l.jsxs(l.Fragment,{children:[l.jsx(Ag,{className:"h-8 w-8 animate-spin mx-auto text-primary"}),l.jsx("h2",{className:"text-xl font-semibold text-foreground",children:"Processando login"}),l.jsx("p",{className:"text-muted-foreground",children:"Aguarde enquanto validamos sua conta Discord..."})]}),n==="success"&&l.jsxs(l.Fragment,{children:[l.jsx("div",{className:"h-8 w-8 mx-auto bg-green-600 rounded-full flex items-center justify-center",children:l.jsx("svg",{className:"h-5 w-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),l.jsx("h2",{className:"text-xl font-semibold text-foreground",children:"Login realizado com sucesso!"}),l.jsx("p",{className:"text-muted-foreground",children:o})]}),n==="error"&&l.jsxs(l.Fragment,{children:[l.jsx("div",{className:"h-8 w-8 mx-auto bg-red-600 rounded-full flex items-center justify-center",children:l.jsx("svg",{className:"h-5 w-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),l.jsx("h2",{className:"text-xl font-semibold text-foreground",children:"Erro no login"}),l.jsx("p",{className:"text-muted-foreground",children:o}),l.jsx("p",{className:"text-sm text-muted-foreground",children:"Redirecionando para a página inicial..."})]})]})})},B2=()=>{const[e]=wr(),t=Rt(),[n,r]=h.useState("Processando pagamento...");return h.useEffect(()=>{(async()=>{try{const s=e.get("error"),i=e.get("token"),a=e.get("urlToken");if(s)throw new Error(`Erro de autorização: ${s}`);if(!i||!a)throw new Error("Tokens de autenticação não encontrados");localStorage.setItem("sessionToken",i),localStorage.setItem("urlToken",a);const c=localStorage.getItem("selectedPlan");if(!c)throw new Error("Plano não encontrado");r(`Processando plano ${c.toUpperCase()}...`);const u=c==="basic"?"price_1RdYzeRIzvHRKrKIhNsADY81":"price_1RdZ0ORIzvHRKrKIt3O227mH";console.log(`🛒 Criando checkout para plano: ${c}, priceId: ${u}`);const f=await fetch("/api/stripe/create-checkout-session",{method:"POST",headers:{"Content-Type":"application/json","x-session-token":i},body:JSON.stringify({priceId:u,plan:c})});if(!f.ok){const d=await f.text();throw new Error(`Erro ao criar sessão de pagamento: ${d}`)}const{url:p}=await f.json();if(!p)throw new Error("URL de pagamento não recebida");console.log(`✅ Redirecionando para Stripe: ${p}`),localStorage.removeItem("selectedPlan"),window.location.href=p}catch(s){console.error("❌ Erro no processamento:",s),r(`Erro: ${s instanceof Error?s.message:"Erro desconhecido"}`),setTimeout(()=>{t("/")},3e3)}})()},[e,t]),l.jsx("div",{className:"min-h-screen bg-[#0A0A0A] flex items-center justify-center",children:l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-400 mx-auto mb-4"}),l.jsx("p",{className:"text-white text-lg",children:n})]})})},V2=new GE,W2=()=>l.jsx(XE,{client:V2,children:l.jsxs(yv,{children:[l.jsx(xS,{}),l.jsx(GS,{}),l.jsx(Kk,{children:l.jsxs(zk,{children:[l.jsx(He,{path:"/",element:l.jsx(oC,{})}),l.jsx(He,{path:"/login",element:l.jsx(iC,{})}),l.jsx(He,{path:"/success",element:l.jsx(aC,{})}),l.jsx(He,{path:"/auth/callback",element:l.jsx(_p,{})}),l.jsx(He,{path:"/callback",element:l.jsx(_p,{})}),l.jsx(He,{path:"/plan-callback",element:l.jsx(B2,{})}),l.jsx(He,{path:"/configure",element:l.jsx(uC,{})}),l.jsx(He,{path:"/bot-active",element:l.jsx(dC,{})}),l.jsx(He,{path:"/dashboard",element:l.jsx(Rp,{})}),l.jsx(He,{path:"/dashboard/:urlToken",element:l.jsx(Rp,{})}),l.jsx(He,{path:"/extrato",element:l.jsx(Ap,{})}),l.jsx(He,{path:"/extrato/:urlToken",element:l.jsx(Ap,{})}),l.jsx(He,{path:"*",element:l.jsx(U2,{})})]})})]})});ng(document.getElementById("root")).render(l.jsx(W2,{}));
