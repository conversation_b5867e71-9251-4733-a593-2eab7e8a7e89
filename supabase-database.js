const { createClient } = require('@supabase/supabase-js');

class SupabaseDatabase {
    constructor() {
        // Configuração do Supabase
        this.supabaseUrl = process.env.SUPABASE_URL;
        this.supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
        
        if (!this.supabaseUrl || !this.supabaseKey) {
            throw new Error('Configurações do Supabase não encontradas no .env');
        }
        
        this.supabase = createClient(this.supabaseUrl, this.supabaseKey);
        console.log('✅ Supabase Database inicializado');
        
        // Manter compatibilidade com o sistema antigo
        this.users = new Map();
        this.userBots = new Map();
        this.pendingConfigurations = new Map();
        
        // Carregar dados do Supabase para memória (cache)
        this.loadFromSupabase();
    }

    async loadFromSupabase() {
        try {
            const { data: users, error } = await this.supabase
                .from('discord_users')
                .select('*');
                
            if (error) {
                console.error('❌ Erro ao carregar usuários do Supabase:', error);
                return;
            }
            
            // Carregar para o Map para compatibilidade
            users.forEach(user => {
                this.users.set(user.discord_id, user);
            });
            
            console.log(`📂 Carregados ${users.length} usuários do Supabase`);
        } catch (error) {
            console.error('❌ Erro ao conectar com Supabase:', error);
        }
    }

    async saveUser(userId, userData) {
        try {
            // Preparar dados para o Supabase
            const supabaseData = {
                id: userId,
                discord_id: userId,
                username: userData.username || '',
                discriminator: userData.discriminator || '0',
                avatar: userData.avatar || null,
                email: userData.email || null,
                access_token: userData.accessToken || null,
                jwt_token: userData.jwtToken || null,
                session_token: userData.sessionToken || null,
                url_token: userData.urlToken || null,
                client_id: userData.clientId || null,
                bot_token: userData.token || null,
                bot_active: userData.botActive || false,
                bot_started_at: userData.botStartedAt || null,
                bot_stopped_at: userData.botStoppedAt || null,
                login_count: userData.loginCount || 0,
                updated_at: new Date().toISOString(),
                last_login: userData.lastLogin || new Date().toISOString()
            };

            // Salvar no Supabase
            const { data, error } = await this.supabase
                .from('discord_users')
                .upsert(supabaseData, { 
                    onConflict: 'discord_id',
                    returning: 'minimal'
                });

            if (error) {
                console.error('❌ Erro ao salvar usuário no Supabase:', error);
                return false;
            }

            // Atualizar cache local
            this.users.set(userId, userData);
            
            console.log(`💾 Usuário ${userId} salvo no Supabase`);
            return true;
        } catch (error) {
            console.error('❌ Erro ao salvar usuário:', error);
            return false;
        }
    }

    getUser(userId) {
        return this.users.get(userId) || null;
    }

    async deleteUser(userId) {
        try {
            const { error } = await this.supabase
                .from('discord_users')
                .delete()
                .eq('discord_id', userId);

            if (error) {
                console.error('❌ Erro ao deletar usuário do Supabase:', error);
                return false;
            }

            // Remover do cache local
            this.users.delete(userId);
            
            console.log(`🗑️ Usuário ${userId} deletado do Supabase`);
            return true;
        } catch (error) {
            console.error('❌ Erro ao deletar usuário:', error);
            return false;
        }
    }

    getStats() {
        return {
            totalUsers: this.users.size,
            activeUsers: Array.from(this.users.values()).filter(user => user.botActive).length,
            totalBots: this.userBots.size,
            pendingConfigs: this.pendingConfigurations.size
        };
    }

    // Métodos de compatibilidade com o sistema antigo
    savePendingConfiguration(userId, configData) {
        this.pendingConfigurations.set(userId, configData);
        console.log(`📋 Configuração pendente salva para usuário ${userId}`);
    }

    getPendingConfiguration(userId) {
        return this.pendingConfigurations.get(userId) || null;
    }

    deletePendingConfiguration(userId) {
        this.pendingConfigurations.delete(userId);
        console.log(`🗑️ Configuração pendente removida para usuário ${userId}`);
    }

    // Método para sincronizar dados locais com Supabase
    async syncToSupabase() {
        console.log('🔄 Sincronizando dados locais com Supabase...');
        
        for (const [userId, userData] of this.users) {
            await this.saveUser(userId, userData);
        }
        
        console.log('✅ Sincronização concluída');
    }
}

module.exports = SupabaseDatabase;
