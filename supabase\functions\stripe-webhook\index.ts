import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, stripe-signature',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Verificar se é POST
    if (req.method !== 'POST') {
      return new Response('Method not allowed', { 
        status: 405, 
        headers: corsHeaders 
      })
    }

    // Obter variáveis de ambiente
    const stripeWebhookSecret = Deno.env.get('STRIPE_WEBHOOK_SECRET')
    const supabaseUrl = Deno.env.get('SUPABASE_URL')
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')

    if (!stripeWebhookSecret || !supabaseUrl || !supabaseServiceKey) {
      console.error('Missing environment variables')
      return new Response('Server configuration error', { 
        status: 500, 
        headers: corsHeaders 
      })
    }

    // Inicializar Supabase
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Obter signature do Stripe
    const signature = req.headers.get('stripe-signature')
    if (!signature) {
      return new Response('Missing stripe signature', { 
        status: 400, 
        headers: corsHeaders 
      })
    }

    // Obter body da requisição
    const body = await req.text()

    // Importar Stripe
    const { Stripe } = await import('https://esm.sh/stripe@12.0.0?target=deno')
    const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY'), {
      apiVersion: '2022-11-15',
    })

    // Verificar webhook signature
    let event
    try {
      event = stripe.webhooks.constructEvent(body, signature, stripeWebhookSecret)
    } catch (err) {
      console.error('Webhook signature verification failed:', err.message)
      return new Response('Webhook signature verification failed', { 
        status: 400, 
        headers: corsHeaders 
      })
    }

    console.log('Received event:', event.type)

    // Processar evento de checkout completado
    if (event.type === 'checkout.session.completed') {
      const session = event.data.object

      console.log('Processing checkout session:', session.id)

      // Mapear Price IDs para planos
      const productMapping = {
        'price_1RdYzeRIzvHRKrKIhNsADY81': 'basic',  // Basic
        'price_1RdZ0ORIzvHRKrKIt3O227mH': 'pro'     // Pro
      }

      // Obter informações da sessão - buscar price_id de várias formas
      let priceId = null

      // Tentar obter do line_items
      if (session.line_items?.data?.[0]?.price?.id) {
        priceId = session.line_items.data[0].price.id
      }
      // Tentar obter do metadata
      else if (session.metadata?.price_id) {
        priceId = session.metadata.price_id
      }
      // Tentar expandir line_items se necessário
      else {
        try {
          const expandedSession = await stripe.checkout.sessions.retrieve(session.id, {
            expand: ['line_items']
          })
          priceId = expandedSession.line_items?.data?.[0]?.price?.id
        } catch (err) {
          console.error('Error expanding session:', err)
        }
      }

      if (!priceId) {
        console.error('Price ID not found in session:', session.id)
        return new Response('Price ID not found', {
          status: 400,
          headers: corsHeaders
        })
      }

      const plan = productMapping[priceId]
      if (!plan) {
        console.error('Unknown price ID:', priceId)
        return new Response('Unknown product', { 
          status: 400, 
          headers: corsHeaders 
        })
      }

      // Gerar chave de licença
      function generateLicenseKey() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
        const part1 = Array.from({length: 4}, () => chars[Math.floor(Math.random() * chars.length)]).join('')
        const part2 = Array.from({length: 4}, () => chars[Math.floor(Math.random() * chars.length)]).join('')
        const part3 = Array.from({length: 4}, () => chars[Math.floor(Math.random() * chars.length)]).join('')
        const part4 = Array.from({length: 4}, () => chars[Math.floor(Math.random() * chars.length)]).join('')
        return `NODEX-${part1}-${part2}-${part3}-${part4}`
      }

      // Calcular expiração (30 dias)
      const expiresAt = new Date()
      expiresAt.setDate(expiresAt.getDate() + 30)

      // Obter nome do plano
      const planNames = {
        'basic': 'Plano Basic',
        'pro': 'Plano Pro'
      }

      // Criar licença no banco
      const licenseData = {
        key: generateLicenseKey(),
        customer_email: session.customer_details?.email || session.customer_email,
        plan: plan,
        plan_name: planNames[plan],
        stripe_session_id: session.id,
        stripe_customer_id: session.customer,
        expires_at: expiresAt.toISOString()
      }

      console.log('Creating license:', licenseData)

      const { data: license, error } = await supabase
        .from('licenses')
        .insert([licenseData])
        .select()
        .single()

      if (error) {
        console.error('Error creating license:', error)
        return new Response('Database error', { 
          status: 500, 
          headers: corsHeaders 
        })
      }

      console.log('License created successfully:', license.key)

      return new Response(JSON.stringify({ 
        success: true, 
        licenseKey: license.key 
      }), {
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        }
      })
    }

    // Outros tipos de evento
    console.log('Unhandled event type:', event.type)
    return new Response(JSON.stringify({ received: true }), {
      headers: { 
        ...corsHeaders, 
        'Content-Type': 'application/json' 
      }
    })

  } catch (error) {
    console.error('Error processing webhook:', error)
    return new Response('Internal server error', { 
      status: 500, 
      headers: corsHeaders 
    })
  }
})
