import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Bot, CheckCircle, AlertCircle, Loader2, ExternalLink } from "lucide-react";
import { useNavigate, useSearchParams } from "react-router-dom";

const Configure = () => {
  const [clientId, setClientId] = useState("");
  const [token, setToken] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  
  const username = searchParams.get('username') || '<PERSON>u<PERSON><PERSON>';

  const validateClientId = (id: string) => {
    return /^\d{17,19}$/.test(id);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setSuccess("");
    setIsLoading(true);

    // Validar Client ID
    if (!validateClientId(clientId)) {
      setError("Client ID deve ter entre 17-19 dígitos");
      setIsLoading(false);
      return;
    }

    // Validar Token
    if (token.length < 50) {
      setError("Token parece estar inválido (muito curto)");
      setIsLoading(false);
      return;
    }

    try {
      // Enviar dados para o backend
      const response = await fetch('/api/configure-bot', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          clientId,
          token,
          username,
          userId: searchParams.get('userId')
        }),
      });

      const result = await response.json();

      if (result.success) {
        setSuccess("Aplicação configurada com sucesso!");
        setTimeout(async () => {
          const sessionToken = localStorage.getItem('sessionToken');
          if (sessionToken) {
            // Buscar urlToken do usuário
            try {
              const userResponse = await fetch(`/api/user-info?token=${sessionToken}`);
              if (userResponse.ok) {
                const userData = await userResponse.json();
                // Buscar dados completos do usuário para pegar urlToken
                const fullUserResponse = await fetch(`/api/user-full?token=${sessionToken}`);
                if (fullUserResponse.ok) {
                  const fullUserData = await fullUserResponse.json();
                  if (fullUserData.urlToken) {
                    navigate(`/dashboard/${fullUserData.urlToken}?token=${sessionToken}`);
                  } else {
                    navigate(`/dashboard?token=${sessionToken}`);
                  }
                } else {
                  navigate(`/dashboard?token=${sessionToken}`);
                }
              } else {
                navigate('/dashboard');
              }
            } catch (error) {
              navigate('/dashboard');
            }
          } else {
            navigate('/dashboard');
          }
        }, 2000);
      } else {
        setError(result.message || "Erro ao configurar o bot");
      }
    } catch (err) {
      setError("Erro de conexão. Tente novamente.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="dark min-h-screen bg-background flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        <Card className="border-border bg-card">
          <CardHeader className="text-center space-y-4">
            <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
              <Bot className="w-8 h-8 text-primary" />
            </div>
            <CardTitle className="text-2xl font-bold text-foreground">
              🎉 Configurar seu Bot Dedicado
            </CardTitle>
            <CardDescription className="text-muted-foreground">
              Olá <strong>{username}</strong>! Configure seu bot personalizado para vendas
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* Tutorial */}
            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
              <div className="flex items-center gap-2 text-blue-600 font-medium mb-2">
                <Bot className="w-4 h-4" />
                Tutorial rápido
              </div>
              <div className="space-y-2 text-sm text-blue-600/80">
                <p><strong>1.</strong> Acesse: <a href="https://discord.com/developers/applications" target="_blank" rel="noopener noreferrer" className="underline inline-flex items-center gap-1">Discord Developer Portal <ExternalLink className="w-3 h-3" /></a></p>
                <p><strong>2.</strong> Clique em "New Application" e dê um nome</p>
                <p><strong>3.</strong> Vá em "OAuth2" → "General" e copie o <strong>Client ID</strong></p>
                <p><strong>4.</strong> Vá em "Bot" → "Token" e copie o <strong>Token</strong></p>
                <p><strong>5.</strong> Cole os dados abaixo</p>
              </div>
            </div>

            {/* Formulário */}
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="clientId">🤖 Client ID</Label>
                <Input
                  id="clientId"
                  type="text"
                  placeholder="Ex: 1234567890123456789"
                  value={clientId}
                  onChange={(e) => setClientId(e.target.value)}
                  required
                  className="font-mono"
                />
                <p className="text-xs text-muted-foreground">
                  Número de 17-19 dígitos encontrado em OAuth2 → General
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="token">🔑 Token do Bot</Label>
                <Input
                  id="token"
                  type="password"
                  placeholder="Ex: MTIzNDU2Nzg5MDEyMzQ1Njc4OQ.GhIjKl..."
                  value={token}
                  onChange={(e) => setToken(e.target.value)}
                  required
                  className="font-mono"
                />
                <p className="text-xs text-muted-foreground">
                  Token encontrado em Bot → Token (mantenha em segredo!)
                </p>
              </div>

              {/* Mensagens */}
              {error && (
                <div className="flex items-center gap-2 text-red-600 bg-red-500/10 border border-red-500/20 rounded-lg p-3">
                  <AlertCircle className="w-4 h-4" />
                  <span className="text-sm">{error}</span>
                </div>
              )}

              {success && (
                <div className="flex items-center gap-2 text-green-600 bg-green-500/10 border border-green-500/20 rounded-lg p-3">
                  <CheckCircle className="w-4 h-4" />
                  <span className="text-sm">{success}</span>
                </div>
              )}

              {/* Botões */}
              <div className="flex gap-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => navigate(`/dashboard?username=${username}&userId=${searchParams.get('userId')}`)}
                  className="flex-1"
                >
                  ← Voltar ao Dashboard
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="flex-1"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Configurando...
                    </>
                  ) : (
                    <>
                      <Bot className="w-4 h-4 mr-2" />
                      Confirmar
                    </>
                  )}
                </Button>
              </div>
            </form>

            {/* Informações adicionais */}
            <div className="bg-muted/50 rounded-lg p-4 space-y-2">
              <h3 className="font-medium text-foreground">📋 Seu bot terá:</h3>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• <strong>/vendas</strong> - Painel principal de vendas</li>
                <li>• <strong>/produtos</strong> - Lista de produtos disponíveis</li>
                <li>• <strong>/comprar [produto]</strong> - Processo de compra</li>
                <li>• <strong>/suporte</strong> - Informações de contato</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Configure;
