import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "lucide-react";
import { useNavigate } from "react-router-dom";

const Hero = () => {
  const navigate = useNavigate();

  const handleGetStarted = () => {
    navigate("/login");
  };

  return <section className="pt-32 pb-20 px-4">
      <div className="container mx-auto text-center">
        <div className="max-w-3xl mx-auto">
          <h1 className="text-6xl md:text-7xl lg:text-8xl font-bold text-foreground mb-6 leading-tight tracking-tight">
            Automatize vendas no Discord
          </h1>

          <p className="text-xl text-muted-foreground mb-12 max-w-2xl mx-auto leading-relaxed">
            Bot completo para processar pagamentos, gerenciar clientes e entregar produtos automaticamente.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              size="lg"
              className="text-base px-8 py-6"
              onClick={handleGetStarted}
            >
              <Bot className="mr-2 h-5 w-5" />
              Começar Agora
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>

          </div>
        </div>
      </div>
    </section>;
};
export default Hero;