const { verifyJWT, isTokenExpiringSoon, generateJWT, safeStringCompare } = require('./jwt-utils');
const SupabaseDatabase = require('./supabase-database');
const database = new SupabaseDatabase();

/**
 * Middleware de autenticação JWT
 * Verifica se o token JWT é válido e adiciona os dados do usuário à requisição
 */
function authenticateJWT(req, res, next) {
    // Tentar pegar token do header Authorization ou query parameter
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1] || req.query.token;

    if (!token) {
        return res.status(401).json({ 
            success: false, 
            message: 'Token de acesso requerido',
            code: 'NO_TOKEN'
        });
    }

    // Verificar token JWT
    const decoded = verifyJWT(token);
    
    if (!decoded) {
        return res.status(401).json({ 
            success: false, 
            message: 'Token inválido ou expirado',
            code: 'INVALID_TOKEN'
        });
    }

    // Verificar se o usuário ainda existe no banco
    const user = database.getUser(decoded.userId);
    if (!user) {
        return res.status(401).json({ 
            success: false, 
            message: 'Usuário não encontrado',
            code: 'USER_NOT_FOUND'
        });
    }

    // Adicionar dados do usuário à requisição
    req.user = {
        id: decoded.userId,
        username: decoded.username,
        discriminator: decoded.discriminator,
        avatar: decoded.avatar,
        fullData: user
    };

    // Verificar se o token está próximo do vencimento e sugerir renovação
    if (isTokenExpiringSoon(token)) {
        res.setHeader('X-Token-Expiring', 'true');
        res.setHeader('X-Token-Refresh-Suggested', 'true');
    }

    next();
}

/**
 * Middleware de autenticação para tokens de sessão (compatibilidade com sistema antigo)
 * Verifica se o token de sessão é válido
 */
function authenticateSession(req, res, next) {
    const token = req.query.token || req.headers['x-session-token'];

    if (!token) {
        return res.status(401).json({ 
            success: false, 
            message: 'Token de sessão requerido',
            code: 'NO_SESSION_TOKEN'
        });
    }

    // Buscar usuário pelo token de sessão (com comparação segura)
    let user = null;
    for (const [userId, userData] of database.users.entries()) {
        if (userData.sessionToken && safeStringCompare(userData.sessionToken, token)) {
            user = userData;
            break;
        }
    }

    if (!user) {
        return res.status(401).json({ 
            success: false, 
            message: 'Token de sessão inválido',
            code: 'INVALID_SESSION_TOKEN'
        });
    }

    // Verificar se o token de sessão não expirou (24 horas)
    const tokenAge = Date.now() - new Date(user.updatedAt || user.createdAt || 0).getTime();
    const maxAge = 24 * 60 * 60 * 1000; // 24 horas em millisegundos

    if (tokenAge > maxAge) {
        return res.status(401).json({ 
            success: false, 
            message: 'Token de sessão expirado',
            code: 'SESSION_EXPIRED'
        });
    }

    // Adicionar dados do usuário à requisição
    req.user = {
        id: user.id,
        username: user.username,
        discriminator: user.discriminator,
        avatar: user.avatar,
        fullData: user
    };

    next();
}

/**
 * Middleware híbrido que tenta JWT primeiro, depois sessão
 */
function authenticateHybrid(req, res, next) {
    const jwtToken = req.headers.authorization?.split(' ')[1] || req.query.jwtToken;
    const sessionToken = req.query.token || req.headers['x-session-token'] || req.headers['X-Session-Token'];

    console.log('🔍 authenticateHybrid - Headers:', {
        authorization: req.headers.authorization ? '[PRESENTE]' : null,
        'x-session-token': req.headers['x-session-token'] ? '[PRESENTE]' : null,
        'X-Session-Token': req.headers['X-Session-Token'] ? '[PRESENTE]' : null
    });
    console.log('🔍 authenticateHybrid - Tokens:', {
        jwtToken: jwtToken ? '[PRESENTE]' : null,
        sessionToken: sessionToken ? '[PRESENTE]' : null
    });

    // Tentar JWT primeiro
    if (jwtToken) {
        const decoded = verifyJWT(jwtToken);
        if (decoded) {
            const user = database.getUser(decoded.userId);
            if (user) {
                req.user = {
                    id: decoded.userId,
                    username: decoded.username,
                    discriminator: decoded.discriminator,
                    avatar: decoded.avatar,
                    fullData: user,
                    authType: 'jwt'
                };
                return next();
            }
        }
    }

    // Fallback para token de sessão
    if (sessionToken) {
        console.log('🔍 Procurando token de sessão no banco...');

        // Recarregar dados do Supabase para garantir que temos os tokens mais recentes
        await database.loadFromSupabase();

        let user = null;
        let foundTokens = [];

        for (const [userId, userData] of database.users.entries()) {
            const tokenMatch = userData.sessionToken ? safeStringCompare(userData.sessionToken, sessionToken) : false;
            foundTokens.push({
                userId,
                hasToken: !!userData.sessionToken,
                tokenMatch,
                savedToken: userData.sessionToken ? userData.sessionToken.substring(0, 10) + '...' : null,
                receivedToken: sessionToken ? sessionToken.substring(0, 10) + '...' : null
            });

            if (userData.sessionToken && tokenMatch) {
                user = userData;
                console.log('✅ Token encontrado para usuário:', userId);
                break;
            }
        }

        console.log('🔍 Tokens encontrados:', foundTokens);

        if (user) {
            const tokenAge = Date.now() - new Date(user.updatedAt || user.createdAt || 0).getTime();
            const maxAge = 24 * 60 * 60 * 1000;

            console.log('🔍 Verificando idade do token:', { tokenAge, maxAge, valid: tokenAge <= maxAge });

            if (tokenAge <= maxAge) {
                console.log('✅ Token válido, autenticando usuário');
                req.user = {
                    id: user.id,
                    username: user.username,
                    discriminator: user.discriminator,
                    avatar: user.avatar,
                    fullData: user,
                    authType: 'session'
                };
                return next();
            } else {
                console.log('❌ Token expirado');
            }
        } else {
            console.log('❌ Token não encontrado no banco');
        }
    }

    // Nenhum método de autenticação válido
    return res.status(401).json({ 
        success: false, 
        message: 'Autenticação requerida',
        code: 'AUTHENTICATION_REQUIRED'
    });
}

/**
 * Middleware opcional - não falha se não houver token
 */
function optionalAuth(req, res, next) {
    const token = req.headers.authorization?.split(' ')[1] || req.query.token;
    
    if (token) {
        const decoded = verifyJWT(token);
        if (decoded) {
            const user = database.getUser(decoded.userId);
            if (user) {
                req.user = {
                    id: decoded.userId,
                    username: decoded.username,
                    discriminator: decoded.discriminator,
                    avatar: decoded.avatar,
                    fullData: user
                };
            }
        }
    }
    
    next();
}

module.exports = {
    authenticateJWT,
    authenticateSession,
    authenticateHybrid,
    optionalAuth
};
