require('dotenv').config();
const SupabaseLicenseManager = require('./supabase-license-manager');

async function testSupabase() {
    console.log('🧪 Testando integração com Supabase...\n');

    try {
        // Inicializar manager
        const licenseManager = new SupabaseLicenseManager();
        console.log('✅ SupabaseLicenseManager inicializado');

        // Teste 1: Criar licença Basic
        console.log('\n📝 Teste 1: Criando licença Basic...');
        const basicLicense = await licenseManager.createLicense(
            '<EMAIL>',
            'basic',
            'test-session-basic-' + Date.now()
        );
        console.log('✅ Licença Basic criada:', basicLicense.key);

        // Teste 2: Criar licença Pro
        console.log('\n📝 Teste 2: Criando licença Pro...');
        const proLicense = await licenseManager.createLicense(
            '<EMAIL>',
            'pro',
            'test-session-pro-' + Date.now()
        );
        console.log('✅ Licença Pro criada:', proLicense.key);

        // Teste 3: Validar licenças
        console.log('\n🔍 Teste 3: Validando licenças...');
        const basicValidation = await licenseManager.validateLicense(basicLicense.key);
        const proValidation = await licenseManager.validateLicense(proLicense.key);
        
        console.log('✅ Validação Basic:', basicValidation.valid ? 'Válida' : 'Inválida');
        console.log('✅ Validação Pro:', proValidation.valid ? 'Válida' : 'Inválida');

        // Teste 4: Ativar licenças
        console.log('\n🔑 Teste 4: Ativando licenças...');
        const basicActivation = await licenseManager.activateLicense(basicLicense.key, 'test-user-123');
        const proActivation = await licenseManager.activateLicense(proLicense.key, 'test-user-456');
        
        console.log('✅ Ativação Basic:', basicActivation.success ? 'Sucesso' : 'Falhou');
        console.log('✅ Ativação Pro:', proActivation.success ? 'Sucesso' : 'Falhou');

        // Teste 5: Buscar usuário
        console.log('\n👤 Teste 5: Buscando usuários...');
        const user1 = await licenseManager.getUserByDiscordId('test-user-123');
        const user2 = await licenseManager.getUserByDiscordId('test-user-456');
        
        console.log('✅ Usuário 1 encontrado:', user1 ? 'Sim' : 'Não');
        console.log('✅ Usuário 2 encontrado:', user2 ? 'Sim' : 'Não');

        // Teste 6: Estatísticas
        console.log('\n📊 Teste 6: Obtendo estatísticas...');
        const stats = await licenseManager.getStats();
        console.log('📈 Estatísticas:');
        console.log(`   Total: ${stats.total}`);
        console.log(`   Ativadas: ${stats.activated}`);
        console.log(`   Pendentes: ${stats.pending}`);
        console.log(`   Basic: ${stats.byPlan.basic}`);
        console.log(`   Pro: ${stats.byPlan.pro}`);

        // Teste 7: Listar licenças
        console.log('\n📋 Teste 7: Listando licenças...');
        const allLicenses = await licenseManager.getAllLicenses();
        console.log(`✅ Total de licenças encontradas: ${allLicenses.length}`);

        // Teste 8: Salvar configuração
        console.log('\n⚙️ Teste 8: Salvando configuração...');
        await licenseManager.saveBotConfig('test-guild-123', 'mercado_pago', {
            access_token: 'test-token',
            public_key: 'test-public-key'
        });
        console.log('✅ Configuração salva');

        // Teste 9: Carregar configuração
        console.log('\n📥 Teste 9: Carregando configuração...');
        const config = await licenseManager.loadBotConfig('test-guild-123', 'mercado_pago');
        console.log('✅ Configuração carregada:', config ? 'Sim' : 'Não');

        console.log('\n🎉 Todos os testes passaram!');
        console.log('\n📋 Próximos passos:');
        console.log('1. Execute o schema SQL no Supabase Dashboard');
        console.log('2. Configure as chaves do Supabase no .env');
        console.log('3. Teste o bot com as chaves criadas');

    } catch (error) {
        console.error('❌ Erro durante o teste:', error.message);
        console.error('Stack:', error.stack);
        
        if (error.message.includes('Configurações do Supabase')) {
            console.log('\n💡 Dica: Configure as variáveis SUPABASE_URL e SUPABASE_SERVICE_KEY no arquivo .env');
        }
    }
}

// Executar teste
testSupabase();
