import { Button } from "@/components/ui/button";
import { Check } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

const Pricing = () => {
  const [loading, setLoading] = useState<string | null>(null);

  const handlePlanClick = async (planType: 'basic' | 'pro') => {
    try {
      setLoading(planType);
      console.log(`🔄 Iniciando processo para plano: ${planType}`);

      // Verificar se está logado (usando o sistema existente)
      const sessionToken = localStorage.getItem('sessionToken');
      console.log(`🔑 SessionToken encontrado:`, !!sessionToken);

      if (!sessionToken) {
        console.log(`🚀 Redirecionando para login Discord com plano: ${planType}`);
        // Redirecionar para login Discord usando o sistema existente
        const clientId = "1383995563342827570";
        const redirectUri = encodeURIComponent(`${window.location.origin}/callback?plan=${planType}`);
        const scope = encodeURIComponent("identify guilds.join guilds");
        const discordAuthUrl = `https://discord.com/api/oauth2/authorize?client_id=${clientId}&redirect_uri=${redirectUri}&response_type=code&scope=${scope}`;
        console.log(`🔗 URL de autenticação:`, discordAuthUrl);
        window.location.href = discordAuthUrl;
        return;
      }

      // Determinar o priceId baseado no plano
      const priceId = planType === 'basic'
        ? 'price_1RdYzeRIzvHRKrKIhNsADY81' // Basic plan
        : 'price_1RdZ0ORIzvHRKrKIt3O227mH'; // Pro plan

      console.log(`� Iniciando checkout para plano ${planType} com priceId: ${priceId}`);

      // Iniciar checkout Stripe para qualquer plano
      try {
        const response = await fetch('/api/stripe/create-checkout-session', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-session-token': sessionToken,
          },
          body: JSON.stringify({
            priceId: priceId,
            plan: planType,
            successUrl: `${window.location.origin}/success`,
            cancelUrl: `${window.location.origin}/pricing`,
          }),
        });

        if (!response.ok) {
          throw new Error('Erro ao criar sessão de checkout');
        }

        const { sessionId } = await response.json();

        // Redirecionar para Stripe Checkout
        const stripe = await import('@stripe/stripe-js').then(m =>
          m.loadStripe('pk_test_51RdYpaRIzvHRKrKI5lpipw0j0NqSDi7VP4yn30XKkFt9yOICAeEeWCuHhwm3d8qb5mWmE0J4fn6pBOPAzNNOmu4U00lLhT8skQ')
        );

        if (stripe) {
          await stripe.redirectToCheckout({ sessionId });
        }
      } catch (error) {
        console.error('Erro ao processar pagamento:', error);
        toast.error('Erro ao processar pagamento');
      }

    } catch (error) {
      console.error('Erro ao processar plano:', error);
      toast.error(error instanceof Error ? error.message : 'Erro ao processar pagamento');
    } finally {
      setLoading(null);
    }
  };

  const plans = [
    {
      name: "Basic",
      price: "R$ 19.97",
      period: "/mês",
      description: "Plano essencial para começar",
      features: [
        "Vendas via Pix/Cartão/Crypto",
        "Sistema de ticket integrado",
        "Defesa Anti-Fraude (Para Mercado Pago)",
        "Avatar/Nome/Status/Cores customizáveis"
      ],
      notIncluded: [
        "Logs, moderação e boas vindas",
        "Rastreador de convites (Invite Tracker)",
        "Auth e Backup avançado (eCloud)"
      ],
      buttonText: "Adquira agora",
      featured: false,
      planType: 'basic' as const
    },
    {
      name: "Pro",
      price: "R$ 39.90",
      period: "/mês",
      description: "Para vendedores profissionais",
      features: [
        "Tudo do plano Basic",
        "Logs, moderação e boas vindas",
        "Rastreador de convites (Invite Tracker)",
        "Auth e Backup avançado (eCloud)",
        "Vendas ilimitadas",
        "Produtos ilimitados",
        "Suporte prioritário 24/7",
        "Analytics avançado"
      ],
      buttonText: "Assinar Pro",
      featured: true,
      planType: 'pro' as const
    }
  ];

  return (
    <section id="pricing" className="py-20 px-4">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            Preços simples
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Comece grátis e escale conforme cresce
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {plans.map((plan, index) => (
            <div 
              key={index} 
              className={`p-8 rounded-lg border ${
                plan.featured 
                  ? 'border-primary bg-card' 
                  : 'border-border bg-card/50'
              }`}
            >
              <div className="text-center mb-8">
                <h3 className="text-xl font-semibold text-foreground mb-2">{plan.name}</h3>
                <p className="text-muted-foreground text-sm mb-4">{plan.description}</p>
                <div className="mb-6">
                  <span className="text-4xl font-bold text-foreground">{plan.price}</span>
                  {plan.period && (
                    <span className="text-muted-foreground">{plan.period}</span>
                  )}
                </div>
              </div>
              
              <ul className="space-y-3 mb-8">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center space-x-3">
                    <Check className="h-4 w-4 text-green-600 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">{feature}</span>
                  </li>
                ))}
                {plan.notIncluded && plan.notIncluded.map((feature, featureIndex) => (
                  <li key={`not-${featureIndex}`} className="flex items-center space-x-3">
                    <span className="h-4 w-4 text-red-500 flex-shrink-0">✕</span>
                    <span className="text-sm text-muted-foreground opacity-60">{feature}</span>
                  </li>
                ))}
              </ul>
              
              <Button
                className="w-full"
                variant={plan.featured ? "default" : "outline"}
                size="lg"
                onClick={() => handlePlanClick(plan.planType)}
                disabled={loading !== null}
              >
                {loading === plan.planType ? 'Processando...' : plan.buttonText}
              </Button>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Pricing;