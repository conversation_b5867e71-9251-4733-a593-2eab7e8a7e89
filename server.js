const express = require('express');
const axios = require('axios');
const path = require('path');
const MainBot = require('./main-bot');
const database = require('./database');
const botManager = require('./bot-manager');
const { generateJWT, verifyJWT, generateUrlToken, generateSessionToken } = require('./jwt-utils');
const { authenticateJWT, authenticateSession, authenticateHybrid } = require('./auth-middleware');
const {
    basicSecurity,
    corsMiddleware,
    validateInput,
    detectInjection,
    securityLogger,
    sanitizeForLog,
    authRateLimit,
    generalRateLimit,
    csrfProtection,
    pathTraversalProtection,
    prototypePollutionProtection
} = require('./security-middleware');
const { raceConditionProtection } = require('./race-condition-protection');
const { secureErrorHandler, createSecureErrors, asyncErrorHandler } = require('./secure-error-handler');
const antiCrashSystem = require('./anticrash-system');

require('dotenv').config();

const app = express();
const mainBot = new MainBot();

// Configuração do Stripe (só inicializar se a chave estiver disponível)
let stripe = null;
if (process.env.STRIPE_SECRET_KEY) {
    stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
    console.log('✅ Stripe configurado');
} else {
    console.log('⚠️ STRIPE_SECRET_KEY não encontrada - funcionalidades de pagamento desabilitadas');
}

// Configuração do Supabase (só inicializar se as chaves estiverem disponíveis)
let supabase = null;
if (process.env.SUPABASE_URL && process.env.SUPABASE_SERVICE_ROLE_KEY) {
    const { createClient } = require('@supabase/supabase-js');
    supabase = createClient(
        process.env.SUPABASE_URL,
        process.env.SUPABASE_SERVICE_ROLE_KEY
    );
    console.log('✅ Supabase configurado');
} else {
    console.log('⚠️ Configurações do Supabase não encontradas - funcionalidades de licença desabilitadas');
}

// Configuração do Discord OAuth2
const DISCORD_CLIENT_ID = process.env.DISCORD_CLIENT_ID;
const DISCORD_CLIENT_SECRET = process.env.DISCORD_CLIENT_SECRET;
const DISCORD_REDIRECT_URI = process.env.DISCORD_REDIRECT_URI || 'http://localhost:3003/callback';

// Verificar se as variáveis de ambiente estão configuradas corretamente
if (!DISCORD_CLIENT_ID || !DISCORD_CLIENT_SECRET) {
    console.error('Erro: Variáveis de ambiente não configuradas corretamente');
    console.error('DISCORD_CLIENT_ID:', DISCORD_CLIENT_ID);
    console.error('DISCORD_CLIENT_SECRET:', DISCORD_CLIENT_SECRET);
    process.exit(1);
}

// Verificar se o client_id é um número válido
const isValidSnowflake = (id) => {
    return typeof id === 'string' && id.length >= 17 && id.length <= 19 && !isNaN(id);
};

if (!isValidSnowflake(DISCORD_CLIENT_ID)) {
    console.error('Erro: Client ID inválido. Deve ser um número de 17 a 19 dígitos.');
    process.exit(1);
}

console.log('Configuração do Discord:');
console.log('Client ID:', DISCORD_CLIENT_ID);
console.log('Redirect URI:', DISCORD_REDIRECT_URI);

// Middlewares de segurança
app.use(basicSecurity);
app.use(corsMiddleware);
app.use(pathTraversalProtection);
app.use(prototypePollutionProtection);
app.use(generalRateLimit);
app.use(validateInput);
app.use(detectInjection);
app.use(csrfProtection);
app.use(securityLogger);

// Servir arquivos estáticos do React build
app.use(express.static(path.join(__dirname, 'discord-sales-oasis-main/dist')));

// Middleware para JSON
app.use(express.json());

// Rotas da API
app.get('/api/stats', (req, res) => {
    const stats = {
        ...database.getStats(),
        ...botManager.getStats()
    };
    res.json(stats);
});

// Rota para configurar bot
app.post('/api/configure-bot', antiCrashSystem.wrapAsync(async (req, res) => {
    try {
        const { clientId, token, username, userId } = req.body;

        console.log(`🔧 Configurando bot para ${username} (ID: ${userId})...`);

        // Validar dados
        if (!botManager.validateClientId(clientId)) {
            return res.json({ success: false, message: 'Client ID inválido' });
        }

        // Validar token
        const isValidToken = await botManager.validateDiscordToken(token);
        if (!isValidToken) {
            return res.json({ success: false, message: 'Token inválido' });
        }

        // Usar userId se disponível, senão usar username
        const userKey = userId || username;

        // Salvar dados do bot no database
        const userData = database.getUser(userKey) || {};
        userData.clientId = clientId;
        userData.token = token;
        userData.username = username;
        userData.id = userId || username;
        database.saveUser(userKey, userData);

        console.log(`💾 Dados salvos para usuário ${userKey}:`, userData);

        // Criar bot do usuário
        const result = await botManager.createUserBot(userKey, clientId, token);

        if (result.success) {
            console.log(`✅ Bot criado para ${username}`);
            res.json({ success: true, message: 'Aplicação configurada com sucesso!' });
        } else {
            console.log(`❌ Falha ao criar bot para ${username}: ${result.message}`);
            res.json({ success: false, message: result.message });
        }
    } catch (error) {
        console.error('Erro ao configurar bot:', error);
        antiCrashSystem.logError(`Erro ao configurar bot: ${error.message}`);
        res.json({ success: false, message: 'Erro interno do servidor' });
    }
}));

// Rota para verificar status do bot
app.get('/api/bot-status', (req, res) => {
    const stats = botManager.getStats();
    res.json({ success: true, bots: stats.botsList });
});

// Rota para parar bot
app.post('/api/stop-bot', antiCrashSystem.wrapAsync(async (req, res) => {
    try {
        // Por simplicidade, vamos parar todos os bots
        // Em produção, você identificaria o usuário específico
        const activeBots = botManager.getAllActiveBots();
        for (const bot of activeBots) {
            await botManager.stopUserBot(bot.userId);
        }
        res.json({ success: true });
    } catch (error) {
        antiCrashSystem.logError(`Erro ao parar bot: ${error.message}`);
        res.json({ success: false, message: 'Erro ao parar bot' });
    }
}));

// Rota para obter informações do usuário logado (com autenticação híbrida)
app.get('/api/user-info', authenticateHybrid, (req, res) => {
    res.json({
        id: req.user.id,
        username: req.user.username,
        discriminator: req.user.discriminator,
        avatar: req.user.avatar,
        authType: req.user.authType || 'session'
    });
});

// Rota para obter dados completos do usuário (incluindo urlToken)
app.get('/api/user-full', authenticateHybrid, (req, res) => {
    res.json({
        id: req.user.id,
        username: req.user.username,
        discriminator: req.user.discriminator,
        avatar: req.user.avatar,
        urlToken: req.user.fullData.urlToken,
        authType: req.user.authType || 'session'
    });
});

// Rota para renovar token JWT (com proteção contra race condition)
app.post('/api/refresh-token',
    authenticateHybrid,
    raceConditionProtection(req => `refresh-token-${req.user?.id || req.ip}`),
    asyncErrorHandler(async (req, res) => {
        // Gerar novo JWT token
        const newJwtToken = generateJWT({
            id: req.user.id,
            username: req.user.username,
            discriminator: req.user.discriminator,
            avatar: req.user.avatar
        });

        // Atualizar token no banco de dados e invalidar token de sessão antigo
        const userData = req.user.fullData;
        userData.jwtToken = newJwtToken;
        userData.sessionToken = generateSessionToken(); // Gerar novo token de sessão
        userData.updatedAt = new Date();
        database.saveUser(req.user.id, userData);

        res.json({
            success: true,
            token: newJwtToken,
            sessionToken: userData.sessionToken,
            expiresIn: '24h'
        });
    }));

// Rota para logout (invalidar tokens)
app.post('/api/logout', authenticateHybrid, (req, res) => {
    try {
        // Invalidar tokens do usuário
        const userData = req.user.fullData;
        userData.sessionToken = null;
        userData.jwtToken = null;
        userData.updatedAt = new Date();
        database.saveUser(req.user.id, userData);

        res.json({
            success: true,
            message: 'Logout realizado com sucesso'
        });
    } catch (error) {
        console.error('Erro ao fazer logout:', error);
        res.status(500).json({
            success: false,
            message: 'Erro interno do servidor'
        });
    }
});

// Rota para obter bots do usuário
app.get('/api/user-bots/:userId', async (req, res) => {
    const { userId } = req.params;

    console.log(`🔍 Buscando bots para usuário: ${userId}`);

    // Obter dados do usuário do database
    const userData = database.getUser(userId);
    const stats = botManager.getStats();

    console.log(`📊 Dados do usuário:`, userData);
    console.log(`📊 Stats do bot manager:`, stats);

    // Criar lista de bots do usuário
    const userBots = [];

    if (userData && userData.clientId) {
        console.log(`✅ Usuário tem bot configurado: ${userData.clientId}`);

        // Buscar informações do bot no Discord
        let botName = `Bot de ${userData.username || 'Usuário'}`;
        let botStatus = 'offline';
        let serverCount = 0;

        try {
            // Verificar se o bot está ativo no botManager primeiro
            const activeBots = botManager.getAllActiveBots ? botManager.getAllActiveBots() : [];
            const activeBot = activeBots.find(bot => bot.userId === userId);

            console.log(`🔍 Bot ativo encontrado:`, activeBot);

            if (activeBot && activeBot.isActive) {
                botStatus = 'online';
                console.log(`✅ Bot está ativo no botManager`);
            } else {
                console.log(`❌ Bot não está ativo no botManager`);
            }

            // Tentar buscar nome do bot e contagem de servidores via API do Discord (se tiver token)
            if (userData.token) {
                try {
                    const axios = require('axios');

                    // Buscar informações do bot
                    const botResponse = await axios.get('https://discord.com/api/v10/users/@me', {
                        headers: {
                            'Authorization': `Bot ${userData.token}`
                        }
                    });
                    botName = botResponse.data.username;

                    // Buscar contagem de servidores
                    try {
                        const guildsResponse = await axios.get('https://discord.com/api/v10/users/@me/guilds', {
                            headers: {
                                'Authorization': `Bot ${userData.token}`
                            }
                        });
                        serverCount = guildsResponse.data.length;

                        // Se conseguiu fazer a requisição e o bot está ativo no manager, está online
                        if (activeBot && activeBot.isActive) {
                            botStatus = 'online';
                        }
                    } catch (guildsError) {
                        console.log(`⚠️ Não foi possível buscar servidores: ${guildsError.message}`);
                        // Se não conseguiu buscar servidores mas o bot está ativo, manter online
                        if (activeBot && activeBot.isActive) {
                            botStatus = 'online';
                        } else {
                            botStatus = 'offline';
                        }
                    }
                } catch (error) {
                    console.log(`⚠️ Não foi possível buscar nome do bot: ${error.message}`);
                    // Se não conseguiu buscar info do bot, verificar se está ativo no manager
                    if (activeBot && activeBot.isActive) {
                        botStatus = 'online';
                    } else {
                        botStatus = 'offline';
                    }
                }
            }
        } catch (error) {
            console.log(`⚠️ Erro ao verificar status do bot: ${error.message}`);
            botStatus = 'offline';
        }

        userBots.push({
            id: userData.clientId,
            name: botName,
            clientId: userData.clientId,
            status: botStatus,
            servers: serverCount,
            sales: 0,
            lastSale: new Date().toLocaleDateString('pt-BR'),
            plan: 'FREE',
            cluster: 'CLF004',
            tariff: 3
        });
    } else {
        console.log(`❌ Usuário não tem bot configurado ou não encontrado`);
    }

    console.log(`📋 Retornando ${userBots.length} bots para o usuário`);
    res.json({ bots: userBots });
});

// Rota para ações do bot (start, stop, pause, delete)
app.post('/api/bot-action', antiCrashSystem.wrapAsync(async (req, res) => {
    try {
        const { botId, action } = req.body;

        console.log(`🔧 Executando ação ${action} no bot ${botId}`);

        // Encontrar dados do usuário pelo clientId
        let userData = null;
        let userKey = null;

        database.users.forEach((data, key) => {
            if (data.clientId === botId) {
                userData = data;
                userKey = key;
            }
        });

        if (!userData) {
            return res.json({ success: false, message: 'Bot não encontrado' });
        }

        switch (action) {
            case 'start':
                console.log(`🚀 Iniciando bot ${botId}...`);
                // Criar/iniciar bot do usuário
                const startResult = await botManager.createUserBot(userKey, userData.clientId, userData.token);
                if (startResult.success) {
                    console.log(`✅ Bot ${botId} iniciado com sucesso`);
                    res.json({ success: true, message: 'Bot iniciado com sucesso!' });
                } else {
                    console.log(`❌ Falha ao iniciar bot ${botId}: ${startResult.message}`);
                    res.json({ success: false, message: startResult.message });
                }
                break;

            case 'stop':
                console.log(`🛑 Parando bot ${botId}...`);
                // Parar bot do usuário
                const stopResult = await botManager.stopUserBot(userKey);
                if (stopResult.success) {
                    console.log(`✅ Bot ${botId} parado com sucesso`);
                    res.json({ success: true, message: 'Bot parado com sucesso!' });
                } else {
                    console.log(`❌ Falha ao parar bot ${botId}: ${stopResult.message}`);
                    res.json({ success: false, message: stopResult.message });
                }
                break;

            case 'pause':
                console.log(`⏸️ Pausando bot ${botId}...`);
                // Por enquanto, pausar = parar (pode implementar lógica específica depois)
                const pauseResult = await botManager.stopUserBot(userKey);
                if (pauseResult.success) {
                    console.log(`✅ Bot ${botId} pausado com sucesso`);
                    res.json({ success: true, message: 'Bot pausado com sucesso!' });
                } else {
                    console.log(`❌ Falha ao pausar bot ${botId}: ${pauseResult.message}`);
                    res.json({ success: false, message: pauseResult.message });
                }
                break;

            case 'restart':
                console.log(`🔄 Reiniciando bot ${botId}...`);
                // Parar primeiro
                const stopForRestart = await botManager.stopUserBot(userKey);
                console.log(`🛑 Resultado do stop para restart:`, stopForRestart);

                // Aguardar um pouco e iniciar novamente
                setTimeout(async () => {
                    const restartResult = await botManager.createUserBot(userKey, userData.clientId, userData.token);
                    console.log(`🔄 Resultado do restart:`, restartResult);
                }, 2000);

                res.json({ success: true, message: 'Bot reiniciado com sucesso!' });
                break;

            case 'delete':
                console.log(`🗑️ Deletando bot ${botId}...`);
                // Parar bot e remover dados
                const deleteStopResult = await botManager.stopUserBot(userKey);
                console.log(`🛑 Resultado do stop para delete:`, deleteStopResult);

                // Remover dados do usuário
                database.users.delete(userKey);
                console.log(`✅ Bot ${botId} deletado com sucesso`);

                res.json({ success: true, message: 'Bot deletado com sucesso!' });
                break;

            default:
                res.json({ success: false, message: 'Ação inválida' });
        }
    } catch (error) {
        console.error('Erro ao executar ação do bot:', error);
        antiCrashSystem.logError(`Erro ao executar ação do bot: ${error.message}`);
        res.json({ success: false, message: 'Erro interno do servidor' });
    }
}));

// Rota de callback do Discord (sem rate limiting muito restritivo)
app.get('/callback', antiCrashSystem.wrapAsync(async (req, res) => {
    const code = req.query.code;
    if (!code) {
        return res.status(400).send('Erro: Código não fornecido');
    }

    try {
        // Trocar código por token de acesso
        const tokenData = new URLSearchParams({
            client_id: DISCORD_CLIENT_ID,
            client_secret: DISCORD_CLIENT_SECRET,
            grant_type: 'authorization_code',
            code,
            redirect_uri: DISCORD_REDIRECT_URI,
            scope: 'identify guilds.join guilds'
        });

        const tokenResponse = await axios.post('https://discord.com/api/v10/oauth2/token', tokenData, {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        });

        // Obter informações do usuário
        const userResponse = await axios.get('https://discord.com/api/v10/users/@me', {
            headers: {
                authorization: `Bearer ${tokenResponse.data.access_token}`
            }
        });

        const userData = {
            id: userResponse.data.id,
            username: userResponse.data.username,
            discriminator: userResponse.data.discriminator,
            avatar: userResponse.data.avatar,
            accessToken: tokenResponse.data.access_token
        };

        console.log(`✅ Usuário autenticado: ${userData.username}#${userData.discriminator}`);

        // Gerar JWT token
        const jwtToken = generateJWT(userData);

        // Gerar token de sessão seguro (compatibilidade)
        const sessionToken = generateSessionToken();

        // Gerar token aleatório para URL (segurança adicional)
        const urlToken = generateUrlToken();

        // Manter dados existentes do usuário se já existir
        const existingUserData = database.getUser(userData.id) || {};

        // Invalidar tokens antigos para evitar session fixation
        if (existingUserData.sessionToken) {
            console.log('🔄 Invalidando token de sessão anterior para segurança');
        }
        if (existingUserData.jwtToken) {
            console.log('🔄 Invalidando JWT anterior para segurança');
        }

        // Mesclar dados mantendo clientId e token se existirem
        const mergedUserData = {
            ...existingUserData,
            id: userData.id,
            username: userData.username,
            discriminator: userData.discriminator,
            avatar: userData.avatar,
            accessToken: userData.accessToken,
            jwtToken: jwtToken,
            sessionToken: sessionToken,
            urlToken: urlToken,
            createdAt: existingUserData.createdAt || new Date(),
            updatedAt: new Date(),
            lastLogin: new Date(),
            loginCount: (existingUserData.loginCount || 0) + 1
        };

        // Salvar dados mesclados
        database.saveUser(userData.id, mergedUserData);

        console.log(`💾 Dados salvos para usuário ${userData.id}:`, sanitizeForLog(mergedUserData));

        // Sempre redirecionar para a página de callback do React
        // O frontend React irá decidir se vai para dashboard ou checkout baseado no localStorage
        res.redirect(`/callback?code=${req.query.code}&token=${sessionToken}&urlToken=${urlToken}`);
    } catch (error) {
        console.error('Erro na autenticação:', error);
        antiCrashSystem.logError(`Erro na autenticação: ${error.message}`);
        res.redirect('/?error=auth_failed');
    }
}));

// Rota para integração com o sistema de autenticação Discord (para APIs do frontend)
app.post('/api/auth/discord', antiCrashSystem.wrapAsync(async (req, res) => {
    try {
        const { user, guilds, access_token } = req.body;

        if (!user || !user.id) {
            return res.status(400).json({ error: 'Dados do usuário inválidos' });
        }

        // Gerar tokens para o usuário
        const urlToken = generateUrlToken();
        const sessionToken = generateSessionToken();

        // Preparar dados do usuário para salvar
        const userData = {
            id: user.id,
            username: user.username,
            discriminator: user.discriminator,
            avatar: user.avatar,
            email: user.email,
            token: access_token,
            urlToken: urlToken,
            sessionToken: sessionToken,
            guilds: guilds || [],
            lastLogin: new Date().toISOString()
        };

        // Verificar se já existe dados para este usuário
        const existingData = database.getUser(user.id);

        // Mesclar dados existentes com novos dados
        const mergedUserData = existingData ? { ...existingData, ...userData } : userData;

        // Salvar dados mesclados
        database.saveUser(user.id, mergedUserData);

        console.log(`💾 Dados salvos via API para usuário ${user.id}:`, sanitizeForLog(mergedUserData));

        // Retornar tokens para o frontend
        res.json({
            sessionToken: sessionToken,
            urlToken: urlToken,
            user: {
                id: user.id,
                username: user.username,
                discriminator: user.discriminator,
                avatar: user.avatar,
                email: user.email
            }
        });

    } catch (error) {
        console.error('Erro na API de autenticação Discord:', error);
        antiCrashSystem.logError(`Erro na API de autenticação Discord: ${error.message}`);
        res.status(500).json({ error: 'Erro interno do servidor' });
    }
}));

// Rota para dashboard sem token - redirecionar para login
app.get('/dashboard', (req, res) => {
    // Se acessar /dashboard diretamente, redirecionar para login
    // O frontend vai lidar com a lógica de redirecionamento baseado no localStorage
    res.sendFile(path.join(__dirname, 'discord-sales-oasis-main/dist/index.html'));
});

// Rota específica para dashboard com token na URL
app.get('/dashboard/:urlToken', (req, res) => {
    const { urlToken } = req.params;

    // Verificar se o urlToken é válido
    let validToken = false;
    database.users.forEach((userData) => {
        if (userData.urlToken === urlToken) {
            validToken = true;
        }
    });

    if (validToken) {
        res.sendFile(path.join(__dirname, 'discord-sales-oasis-main/dist/index.html'));
    } else {
        res.redirect('/login');
    }
});

// Rota para extrato sem token - deixar o frontend lidar
app.get('/extrato', (req, res) => {
    res.sendFile(path.join(__dirname, 'discord-sales-oasis-main/dist/index.html'));
});

// Rota para página de extrato Nodex Pay com token na URL
app.get('/extrato/:urlToken', (req, res) => {
    const { urlToken } = req.params;

    // Verificar se o urlToken é válido
    let validToken = false;
    database.users.forEach((userData) => {
        if (userData.urlToken === urlToken) {
            validToken = true;
        }
    });

    if (validToken) {
        res.sendFile(path.join(__dirname, 'discord-sales-oasis-main/dist/index.html'));
    } else {
        res.redirect('/login');
    }
});

// API para obter dados de extrato do usuário (com autenticação)
app.get('/api/extrato/:userId', authenticateHybrid, (req, res) => {
    const { userId } = req.params;

    // Verificar se o usuário autenticado pode acessar estes dados
    if (req.user.id !== userId) {
        return res.status(403).json({
            error: 'Acesso negado',
            message: 'Você não tem permissão para acessar estes dados'
        });
    }

    // Obter dados do usuário
    const userData = database.getUser(userId);

    if (!userData) {
        return res.status(404).json({ error: 'Usuário não encontrado' });
    }

    // Simular dados de extrato (em produção, buscar de um banco de dados real)
    const extratoData = {
        userId: userId,
        username: userData.username,
        servers: [
            {
                id: userData.clientId || '123456789',
                name: `Servidor de ${userData.username}`,
                balance: Math.floor(Math.random() * 1000) / 100, // Valor aleatório para demonstração
                pendingWithdrawals: Math.floor(Math.random() * 500) / 100,
                lastUpdate: new Date().toISOString(),
                transactions: [
                    {
                        id: '1',
                        type: 'sale',
                        amount: 25.50,
                        description: 'Venda de produto premium',
                        date: new Date(Date.now() - 86400000).toISOString(),
                        status: 'completed'
                    },
                    {
                        id: '2',
                        type: 'withdrawal',
                        amount: -15.00,
                        description: 'Saque via Pix',
                        date: new Date(Date.now() - 172800000).toISOString(),
                        status: 'pending'
                    }
                ]
            }
        ]
    };

    res.json(extratoData);
});

// ==================== ROTAS DE PAGAMENTO E LICENÇAS ====================

// Função para gerar chave de licença
function generateLicenseKey() {
    const crypto = require('crypto');
    const segments = [];
    for (let i = 0; i < 4; i++) {
        const segment = crypto.randomBytes(2).toString('hex').toUpperCase();
        segments.push(segment);
    }
    return `NODEX-${segments.join('-')}`;
}

// Rota para criar licença gratuita
app.post('/api/licenses/create-free', authenticateHybrid, antiCrashSystem.wrapAsync(async (req, res) => {
    try {
        if (!supabase) {
            return res.status(503).json({ error: 'Serviço de licenças não disponível' });
        }
        const userId = req.user.id;
        const userEmail = req.user.email || `${req.user.username}@discord.user`;
        const userName = `${req.user.username}#${req.user.discriminator}`;

        // Verificar se o usuário já tem uma licença básica
        const { data: existingLicense } = await supabase
            .from('licenses')
            .select('license_key')
            .eq('discord_id', userId)
            .eq('plan', 'basic')
            .single();

        if (existingLicense) {
            return res.json({ licenseKey: existingLicense.license_key });
        }

        // Criar nova licença gratuita
        const licenseKey = generateLicenseKey();

        const { data: license, error } = await supabase
            .from('licenses')
            .insert({
                license_key: licenseKey,
                plan: 'basic',
                status: 'active',
                customer_email: userEmail,
                customer_name: userName,
                discord_id: userId,
                max_sales: 50,
                max_products: 3,
                created_at: new Date().toISOString(),
                expires_at: null, // Licença básica não expira
            })
            .select()
            .single();

        if (error) {
            console.error('Erro ao criar licença:', error);
            return res.status(500).json({ error: 'Falha ao criar licença' });
        }

        console.log(`✅ Licença básica criada para ${userName}: ${licenseKey}`);
        res.json({ licenseKey: license.license_key });

    } catch (error) {
        console.error('Erro ao criar licença gratuita:', error);
        antiCrashSystem.logError(`Erro ao criar licença gratuita: ${error.message}`);
        res.status(500).json({ error: 'Erro interno do servidor' });
    }
}));

// Rota para criar sessão de checkout Stripe
app.post('/api/stripe/create-checkout-session', authenticateHybrid, antiCrashSystem.wrapAsync(async (req, res) => {
    try {
        if (!stripe) {
            return res.status(503).json({ error: 'Serviço de pagamento não disponível' });
        }
        const { priceId, plan, successUrl, cancelUrl } = req.body;
        const userId = req.user.id;
        const userEmail = req.user.email || `${req.user.username}@discord.user`;

        if (!priceId || !plan) {
            return res.status(400).json({ error: 'Campos obrigatórios ausentes' });
        }

        // Criar sessão de checkout
        const session = await stripe.checkout.sessions.create({
            payment_method_types: ['card'],
            line_items: [
                {
                    price: priceId,
                    quantity: 1,
                },
            ],
            mode: 'subscription',
            customer_email: userEmail,
            metadata: {
                plan: plan,
                discord_id: userId,
                discord_username: `${req.user.username}#${req.user.discriminator}`,
            },
            success_url: `${successUrl}?session_id={CHECKOUT_SESSION_ID}`,
            cancel_url: cancelUrl,
            allow_promotion_codes: true,
            billing_address_collection: 'required',
        });

        console.log(`💳 Sessão de checkout criada para ${req.user.username}: ${session.id}`);
        res.json({ sessionId: session.id });

    } catch (error) {
        console.error('Erro ao criar sessão de checkout:', error);
        antiCrashSystem.logError(`Erro ao criar sessão de checkout: ${error.message}`);
        res.status(500).json({ error: 'Falha ao criar sessão de checkout' });
    }
}));

// Rota para obter informações da sessão Stripe
app.get('/api/stripe/session-info', authenticateHybrid, antiCrashSystem.wrapAsync(async (req, res) => {
    try {
        if (!supabase) {
            return res.status(503).json({ error: 'Serviço de licenças não disponível' });
        }
        const { session_id } = req.query;

        if (!session_id) {
            return res.status(400).json({ error: 'Session ID é obrigatório' });
        }

        // Buscar licença baseada no session_id do Stripe
        const { data: license, error } = await supabase
            .from('licenses')
            .select('license_key')
            .eq('stripe_session_id', session_id)
            .single();

        if (error || !license) {
            return res.status(404).json({ error: 'Licença não encontrada' });
        }

        res.json({ licenseKey: license.license_key });

    } catch (error) {
        console.error('Erro ao buscar informações da sessão:', error);
        antiCrashSystem.logError(`Erro ao buscar informações da sessão: ${error.message}`);
        res.status(500).json({ error: 'Erro interno do servidor' });
    }
}));

// Webhook do Stripe para processar pagamentos
app.post('/api/stripe/webhook', express.raw({type: 'application/json'}), antiCrashSystem.wrapAsync(async (req, res) => {
    const sig = req.headers['stripe-signature'];
    const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

    let event;

    try {
        event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
    } catch (err) {
        console.error(`Webhook signature verification failed:`, err.message);
        return res.status(400).send(`Webhook Error: ${err.message}`);
    }

    // Processar evento
    if (event.type === 'checkout.session.completed') {
        const session = event.data.object;

        try {
            // Detectar plano baseado no metadata
            const plan = session.metadata.plan || 'pro';
            const licenseKey = generateLicenseKey();

            // Configurar limites baseado no plano
            const planLimits = plan === 'basic'
                ? { max_sales: 1000, max_products: 50 } // Limites do Basic
                : { max_sales: -1, max_products: -1 }; // Ilimitado para Pro

            const { data: license, error } = await supabase
                .from('licenses')
                .insert({
                    license_key: licenseKey,
                    plan: plan,
                    status: 'active',
                    customer_email: session.customer_email,
                    customer_name: session.metadata.discord_username,
                    discord_id: session.metadata.discord_id,
                    stripe_session_id: session.id,
                    stripe_customer_id: session.customer,
                    max_sales: planLimits.max_sales,
                    max_products: planLimits.max_products,
                    created_at: new Date().toISOString(),
                    expires_at: null, // Baseado na assinatura
                })
                .select()
                .single();

            if (error) {
                console.error(`Erro ao criar licença ${plan}:`, error);
            } else {
                console.log(`✅ Licença ${plan} criada: ${licenseKey} para ${session.metadata.discord_username}`);
            }

        } catch (error) {
            console.error('Erro ao processar webhook:', error);
            antiCrashSystem.logError(`Erro ao processar webhook: ${error.message}`);
        }
    }

    res.json({received: true});
}));

// Rota para verificar licença
app.get('/api/licenses/verify/:licenseKey', antiCrashSystem.wrapAsync(async (req, res) => {
    try {
        const { licenseKey } = req.params;

        const { data: license, error } = await supabase
            .from('licenses')
            .select('*')
            .eq('license_key', licenseKey)
            .eq('status', 'active')
            .single();

        if (error || !license) {
            return res.status(404).json({ error: 'Licença não encontrada ou inválida' });
        }

        // Verificar se a licença expirou
        if (license.expires_at && new Date(license.expires_at) < new Date()) {
            return res.status(403).json({ error: 'Licença expirada' });
        }

        res.json({
            valid: true,
            plan: license.plan,
            maxSales: license.max_sales,
            maxProducts: license.max_products,
            createdAt: license.created_at,
            expiresAt: license.expires_at,
        });

    } catch (error) {
        console.error('Erro ao verificar licença:', error);
        antiCrashSystem.logError(`Erro ao verificar licença: ${error.message}`);
        res.status(500).json({ error: 'Erro interno do servidor' });
    }
}));

// ==================== FIM DAS ROTAS DE PAGAMENTO ====================

// Rota catch-all - servir o React app
app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'discord-sales-oasis-main/dist/index.html'));
});

// Handler de erro seguro (deve ser o último middleware)
app.use(secureErrorHandler(process.env.NODE_ENV === 'development'));

// Inicializar o bot principal
const initializeMainBot = antiCrashSystem.wrapAsync(async function() {
    const success = await mainBot.start();
    if (!success) {
        console.error('Falha ao iniciar o bot principal');
        antiCrashSystem.logError('Falha ao iniciar o bot principal');
        // Não fazer exit aqui, deixar o anticrash decidir
        throw new Error('Falha ao iniciar o bot principal');
    }
});

// Inicializar sistema
const startSystem = antiCrashSystem.wrapAsync(async function() {
    try {
        // Iniciar bot principal
        await initializeMainBot();

        // Iniciar servidor web
        const PORT = process.env.PORT || 3003;
        const server = app.listen(PORT, () => {
            console.log(`🚀 Servidor rodando em http://localhost:${PORT}`);
            console.log(`📱 Frontend React disponível em http://localhost:${PORT}`);
            console.log(`🤖 Bot principal ativo e pronto para enviar DMs`);
            console.log(`📊 API de estatísticas em http://localhost:${PORT}/api/stats`);
            console.log(`🛡️ Sistema AntiCrash ativo e monitorando`);
        });

        // Adicionar handler de erro para o servidor
        server.on('error', (error) => {
            antiCrashSystem.logError(`Erro no servidor: ${error.message}`);
        });

    } catch (error) {
        console.error('Erro ao inicializar sistema:', error);
        antiCrashSystem.logError(`Erro ao inicializar sistema: ${error.message}`);
        // Não fazer exit aqui, deixar o anticrash decidir
        throw error;
    }
});

// Iniciar o sistema
startSystem();
