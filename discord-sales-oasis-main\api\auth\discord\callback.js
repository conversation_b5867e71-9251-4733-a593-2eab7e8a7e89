// API para processar callback do Discord OAuth e integrar com sistema existente
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { code } = req.body;

  if (!code) {
    return res.status(400).json({ error: 'Authorization code is required' });
  }

  try {
    // Configurações do Discord OAuth
    const DISCORD_CLIENT_ID = '1383995563342827570'; // Client ID do sistema existente
    const DISCORD_CLIENT_SECRET = process.env.DISCORD_CLIENT_SECRET;
    const REDIRECT_URI = `${req.headers.origin || 'http://localhost:8080'}/callback`;

    if (!DISCORD_CLIENT_SECRET) {
      return res.status(500).json({ error: 'Discord client secret not configured' });
    }

    // Trocar código por token
    const tokenResponse = await fetch('https://discord.com/api/oauth2/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: DISCORD_CLIENT_ID,
        client_secret: DISCORD_CLIENT_SECRET,
        grant_type: 'authorization_code',
        code: code,
        redirect_uri: REDIRECT_URI,
      }),
    });

    if (!tokenResponse.ok) {
      const error = await tokenResponse.text();
      console.error('Discord token error:', error);
      return res.status(400).json({ error: 'Failed to exchange code for token' });
    }

    const tokenData = await tokenResponse.json();

    // Buscar dados do usuário
    const userResponse = await fetch('https://discord.com/api/users/@me', {
      headers: {
        Authorization: `Bearer ${tokenData.access_token}`,
      },
    });

    if (!userResponse.ok) {
      return res.status(400).json({ error: 'Failed to fetch user data' });
    }

    const userData = await userResponse.json();

    // Buscar guilds do usuário
    const guildsResponse = await fetch('https://discord.com/api/users/@me/guilds', {
      headers: {
        Authorization: `Bearer ${tokenData.access_token}`,
      },
    });

    let guilds = [];
    if (guildsResponse.ok) {
      guilds = await guildsResponse.json();
    }

    // Integrar com o sistema existente - fazer chamada para API do bot
    try {
      const botApiResponse = await fetch('http://localhost:3003/api/auth/discord', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user: userData,
          guilds: guilds,
          access_token: tokenData.access_token,
        }),
      });

      let botData = null;
      if (botApiResponse.ok) {
        botData = await botApiResponse.json();
      }

      // Retornar dados de autenticação
      res.json({
        sessionToken: tokenData.access_token, // Usar access_token como sessionToken
        urlToken: botData?.urlToken || null,
        user: {
          id: userData.id,
          username: userData.username,
          discriminator: userData.discriminator,
          avatar: userData.avatar,
          email: userData.email,
        },
        guilds: guilds,
      });

    } catch (botError) {
      console.error('Error integrating with bot API:', botError);
      
      // Mesmo se a integração com o bot falhar, retornar dados básicos
      res.json({
        sessionToken: tokenData.access_token,
        urlToken: null,
        user: {
          id: userData.id,
          username: userData.username,
          discriminator: userData.discriminator,
          avatar: userData.avatar,
          email: userData.email,
        },
        guilds: guilds,
      });
    }

  } catch (error) {
    console.error('Error in Discord callback:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
