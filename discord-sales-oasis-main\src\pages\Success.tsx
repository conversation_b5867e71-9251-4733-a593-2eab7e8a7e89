import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckCircle, Copy, ExternalLink } from "lucide-react";
import { Link, useSearchParams } from "react-router-dom";
import { toast } from "sonner";
import Header from "@/components/Header";

const Success = () => {
  const [searchParams] = useSearchParams();
  const [licenseKey, setLicenseKey] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchLicenseInfo = async () => {
      try {
        const sessionId = searchParams.get('session_id');

        if (sessionId) {
          // Buscar informações da licença baseada na sessão
          const response = await fetch(`/api/stripe/session-info?session_id=${sessionId}`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('sessionToken')}`,
            },
          });

          if (response.ok) {
            const data = await response.json();
            setLicenseKey(data.licenseKey);
          }
        }
      } catch (error) {
        console.error('Erro ao buscar informações da licença:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchLicenseInfo();
  }, [searchParams]);

  const copyLicenseKey = () => {
    if (licenseKey) {
      navigator.clipboard.writeText(licenseKey);
      toast.success('Chave de licença copiada!');
    }
  };

  return (
    <div className="dark min-h-screen bg-background">
      <Header />
      <div className="container mx-auto px-4 py-20">
        <div className="max-w-md mx-auto text-center">
          <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-6" />
          <h1 className="text-3xl font-bold text-foreground mb-4">
            Pagamento Realizado com Sucesso!
          </h1>
          <p className="text-muted-foreground mb-8">
            Sua licença foi ativada e você já pode começar a usar o bot.
          </p>

          {loading ? (
            <div className="mb-8">
              <div className="animate-pulse bg-muted h-4 rounded mb-2"></div>
              <div className="animate-pulse bg-muted h-4 rounded w-3/4 mx-auto"></div>
            </div>
          ) : licenseKey ? (
            <div className="mb-8 p-4 bg-muted rounded-lg">
              <p className="text-sm text-muted-foreground mb-2">Sua chave de licença:</p>
              <div className="flex items-center space-x-2">
                <code className="flex-1 bg-background px-3 py-2 rounded text-sm font-mono">
                  {licenseKey}
                </code>
                <Button size="sm" variant="outline" onClick={copyLicenseKey}>
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ) : null}

          <div className="space-y-4">
            <Button asChild className="w-full">
              <Link to={localStorage.getItem('urlToken') ? `/dashboard/${localStorage.getItem('urlToken')}` : '/dashboard'}>
                Ir para Dashboard
              </Link>
            </Button>
            <Button variant="outline" asChild className="w-full">
              <Link to="/" className="flex items-center justify-center space-x-2">
                <span>Voltar ao Início</span>
                <ExternalLink className="h-4 w-4" />
              </Link>
            </Button>
          </div>

          <div className="mt-8 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
            <h3 className="font-semibold text-foreground mb-2">Próximos passos:</h3>
            <ol className="text-sm text-muted-foreground text-left space-y-1">
              <li>1. Acesse o dashboard para configurar seu bot</li>
              <li>2. Configure os canais e produtos</li>
              <li>3. Comece a vender!</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Success;
