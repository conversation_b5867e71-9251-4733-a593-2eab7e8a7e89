import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';

const AuthCallback = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processando login...');

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const code = searchParams.get('code');
        const error = searchParams.get('error');
        const plan = searchParams.get('plan'); // Capturar parâmetro de plano

        if (error) {
          throw new Error(`Erro de autorização: ${error}`);
        }

        if (!code) {
          throw new Error('Código de autorização não encontrado');
        }

        setMessage('Processando autenticação...');

        // Usar o sistema de autenticação existente
        const response = await fetch('/api/auth/discord/callback', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ code }),
        });

        if (!response.ok) {
          throw new Error('Erro ao processar autenticação');
        }

        const authData = await response.json();

        // Salvar tokens no localStorage (sistema existente)
        localStorage.setItem('sessionToken', authData.sessionToken);
        if (authData.urlToken) {
          localStorage.setItem('urlToken', authData.urlToken);
        }

        setStatus('success');
        setMessage('Login realizado com sucesso!');

        // Se veio de um plano específico, processar automaticamente
        if (plan) {
          setMessage('Processando plano selecionado...');

          if (plan === 'basic') {
            // Criar licença gratuita
            try {
              const licenseResponse = await fetch('/api/licenses/create-free', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': `Bearer ${authData.sessionToken}`,
                },
              });

              if (licenseResponse.ok) {
                const { licenseKey } = await licenseResponse.json();
                toast.success(`Licença criada com sucesso! Chave: ${licenseKey}`);

                // Redirecionar para dashboard
                setTimeout(() => {
                  if (authData.urlToken) {
                    navigate(`/dashboard/${authData.urlToken}`);
                  } else {
                    navigate('/dashboard');
                  }
                }, 2000);
                return;
              }
            } catch (error) {
              console.error('Erro ao criar licença básica:', error);
            }
          } else if (plan === 'pro') {
            // Redirecionar para checkout Stripe
            try {
              const checkoutResponse = await fetch('/api/stripe/create-checkout-session', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': `Bearer ${authData.sessionToken}`,
                },
                body: JSON.stringify({
                  priceId: 'price_1RdZ0ORIzvHRKrKIt3O227mH',
                  plan: 'pro',
                  successUrl: `${window.location.origin}/success`,
                  cancelUrl: `${window.location.origin}/pricing`,
                }),
              });

              if (checkoutResponse.ok) {
                const { sessionId } = await checkoutResponse.json();

                // Redirecionar para Stripe Checkout
                const stripe = await import('@stripe/stripe-js').then(m =>
                  m.loadStripe('pk_test_51RdYpaRIzvHRKrKI5lpipw0j0NqSDi7VP4yn30XKkFt9yOICAeEeWCuHhwm3d8qb5mWmE0J4fn6pBOPAzNNOmu4U00lLhT8skQ')
                );

                if (stripe) {
                  await stripe.redirectToCheckout({ sessionId });
                  return;
                }
              }
            } catch (error) {
              console.error('Erro ao processar pagamento:', error);
            }
          }
        }

        // Redirecionar para dashboard ou pricing após 2 segundos
        setTimeout(() => {
          if (authData.urlToken) {
            navigate(`/dashboard/${authData.urlToken}`);
          } else {
            navigate('/pricing');
          }
        }, 2000);

      } catch (error) {
        console.error('Erro no callback:', error);
        setStatus('error');
        setMessage(error instanceof Error ? error.message : 'Erro desconhecido');

        // Redirecionar para home após 3 segundos em caso de erro
        setTimeout(() => {
          navigate('/');
        }, 3000);
      }
    };

    handleCallback();
  }, [searchParams, navigate]);

  return (
    <div className="dark min-h-screen bg-background flex items-center justify-center">
      <div className="text-center space-y-4">
        {status === 'loading' && (
          <>
            <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
            <h2 className="text-xl font-semibold text-foreground">Processando login</h2>
            <p className="text-muted-foreground">Aguarde enquanto validamos sua conta Discord...</p>
          </>
        )}

        {status === 'success' && (
          <>
            <div className="h-8 w-8 mx-auto bg-green-600 rounded-full flex items-center justify-center">
              <svg className="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-foreground">Login realizado com sucesso!</h2>
            <p className="text-muted-foreground">Redirecionando para a página de planos...</p>
          </>
        )}

        {status === 'error' && (
          <>
            <div className="h-8 w-8 mx-auto bg-red-600 rounded-full flex items-center justify-center">
              <svg className="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-foreground">Erro no login</h2>
            <p className="text-muted-foreground">{message}</p>
            <p className="text-sm text-muted-foreground">Redirecionando para a página inicial...</p>
          </>
        )}
      </div>
    </div>
  );
};

export default AuthCallback;
