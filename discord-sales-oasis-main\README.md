# Discord Sales Bot - Landing Page & License System

Sistema completo de licenciamento para bot de vendas Discord com integração Stripe e Supabase.

## 🚀 Funcionalidades

- **Landing Page Responsiva**: Interface moderna com React + Vite
- **Autenticação Discord OAuth**: Login seguro via Discord
- **Sistema de Licenças**: Planos Starter (gratuito) e Pro (pago)
- **Integração Stripe**: Pagamentos seguros com webhook automático
- **Backend Supabase**: Banco de dados PostgreSQL com Edge Functions
- **Geração Automática de Licenças**: Chaves únicas no formato NODEX-XXXX-XXXX-XXXX-XXXX

## 📋 Pré-requisitos

- Node.js 18+
- Conta Discord Developer
- Conta Stripe
- Projeto Supabase

## ⚙️ Configuração

### 1. Discord OAuth

1. Acesse [Discord Developer Portal](https://discord.com/developers/applications)
2. Crie uma nova aplicação
3. Em OAuth2 → General:
   - Client ID: `1320506893893734400` (já configurado)
   - Adicione redirect URI: `http://localhost:8080/auth/callback`
4. Copie o Client Secret

### 2. Stripe

1. Acesse [Stripe Dashboard](https://dashboard.stripe.com)
2. Copie as chaves:
   - Publishable Key (já configurado no código)
   - Secret Key
3. Configure os Price IDs:
   - Basic: `price_1RdYzeRIzvHRKrKIhNsADY81`
   - Pro: `price_1RdZ0ORIzvHRKrKIt3O227mH`

### 3. Supabase

1. Projeto: `bfpgueewaqjnuuqgmxsr`
2. Execute o schema SQL (já configurado)
3. Configure Edge Function para webhook Stripe
4. Copie as chaves da API

### 4. Variáveis de Ambiente

Crie arquivo `.env` baseado no `.env.example`:

```env
# Discord OAuth
DISCORD_CLIENT_SECRET=your_discord_client_secret

# Stripe
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key

# Supabase
SUPABASE_URL=https://bfpgueewaqjnuuqgmxsr.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

## 🚀 Instalação

```bash
# Instalar dependências
npm install

# Executar em desenvolvimento
npm run dev

# Build para produção
npm run build
```
