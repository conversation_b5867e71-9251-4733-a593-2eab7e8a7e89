import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { toast } from "@/components/ui/sonner";
import { Bot, Plus, Settings, Play, Pause, Trash2, ExternalLink, Users, ShoppingCart, TrendingUp, LogOut, RotateCcw, CreditCard, AlertTriangle } from "lucide-react";
import { useNavigate, useSearchParams, useParams } from "react-router-dom";
import { saveTokens, getTokens, isTokenValid, checkAndRefreshAuth, logout, authenticatedFetch } from "@/utils/auth";

interface BotData {
  id: string;
  name: string;
  clientId: string;
  status: 'online' | 'offline' | 'error';
  servers: number;
  sales: number;
  lastSale: string;
  plan: string;
  cluster: string;
  tariff: number;
}

const Dashboard = () => {
  const [bots, setBots] = useState<BotData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<any>(null);
  const [userFullData, setUserFullData] = useState<any>(null);
  const [restartingBots, setRestartingBots] = useState<Set<string>>(new Set());
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { urlToken } = useParams();

  useEffect(() => {
    const initializeAuth = async () => {
      // Pegar tokens da URL (vindo do login)
      const sessionToken = searchParams.get('token');
      const jwtToken = searchParams.get('jwt');

      console.log('Dashboard useEffect - sessionToken da URL:', sessionToken);
      console.log('Dashboard useEffect - jwtToken da URL:', jwtToken ? 'JWT_PRESENT' : 'NO_JWT');
      console.log('Dashboard useEffect - urlToken da URL:', urlToken);

      if (sessionToken || jwtToken) {
        // Usuário logado via OAuth - salvar tokens
        const tokens = {
          sessionToken: sessionToken || undefined,
          jwtToken: jwtToken || undefined,
          urlToken: urlToken || undefined
        };

        saveTokens(tokens);
        console.log('Tokens salvos no localStorage');

        // Pular verificação de refresh já que acabamos de receber tokens válidos do servidor
        try {
          await loadUserInfo();

          // Limpar tokens da URL após salvar, mantendo urlToken se existir
          if (urlToken) {
            navigate(`/dashboard/${urlToken}`, { replace: true });
          } else {
            // Se não temos urlToken na URL, buscar dos dados do usuário
            await redirectToCorrectUrl();
          }
        } catch (error) {
          console.error('Erro ao carregar dados do usuário:', error);
          navigate('/login');
        }
      } else {
        // Tentar usar tokens salvos
        const savedTokens = getTokens();
        console.log('Tokens do localStorage:', {
          hasJWT: !!savedTokens.jwtToken,
          hasSession: !!savedTokens.sessionToken,
          hasUrl: !!savedTokens.urlToken
        });

        if (savedTokens.jwtToken || savedTokens.sessionToken) {
          // Verificar e renovar autenticação se necessário
          const authValid = await checkAndRefreshAuth();

          if (authValid) {
            // Se temos urlToken salvo mas não estamos na URL correta, redirecionar
            if (savedTokens.urlToken && !urlToken) {
              navigate(`/dashboard/${savedTokens.urlToken}`, { replace: true });
              return;
            }

            // Carregar dados do usuário
            await loadUserInfo();
          } else {
            console.log('Autenticação inválida, redirecionando para login');
            navigate('/login');
          }
        } else {
          // Nenhum token encontrado
          console.log('Nenhum token encontrado, redirecionando para login');
          navigate('/login');
        }
      }
    };

    initializeAuth();
  }, [searchParams, navigate, urlToken]);

  const redirectToCorrectUrl = async () => {
    try {
      const response = await authenticatedFetch('/api/user-full');
      if (response.ok) {
        const fullUserData = await response.json();
        if (fullUserData.urlToken) {
          saveTokens({ urlToken: fullUserData.urlToken });
          navigate(`/dashboard/${fullUserData.urlToken}`, { replace: true });
          return;
        }
      }
    } catch (error) {
      console.error('Erro ao buscar dados completos:', error);
    }
  };

  const loadUserInfo = async () => {
    try {
      console.log('Carregando informações do usuário...');
      const response = await authenticatedFetch('/api/user-info');
      console.log('Resposta da API user-info:', response.status, response.statusText);

      if (response.ok) {
        const userData = await response.json();
        console.log('Dados do usuário recebidos:', userData);
        setUser(userData);

        // Buscar dados completos do usuário (incluindo urlToken)
        const fullResponse = await authenticatedFetch('/api/user-full');
        if (fullResponse.ok) {
          const fullUserData = await fullResponse.json();
          setUserFullData(fullUserData);

          // Salvar urlToken se não estiver salvo
          if (fullUserData.urlToken) {
            const tokens = getTokens();
            if (!tokens.urlToken) {
              saveTokens({ ...tokens, urlToken: fullUserData.urlToken });
            }
          }
        }

        loadUserBots(userData.id);
      } else {
        // Token inválido, fazer logout
        console.log('Token inválido, fazendo logout');
        logout();
      }
    } catch (error) {
      console.error('Erro ao verificar autenticação:', error);
      logout();
    }
  };

  const loadUserBots = async (userId: string) => {
    try {
      console.log('Carregando bots para usuário:', userId);
      const response = await fetch(`/api/user-bots/${userId}`);
      console.log('Resposta da API user-bots:', response.status, response.statusText);

      if (response.ok) {
        const data = await response.json();
        console.log('Dados dos bots recebidos:', data);
        setBots(data.bots || []);
      } else {
        console.error('Erro ao carregar bots - resposta não OK:', response.status);
      }
    } catch (error) {
      console.error('Erro ao carregar bots:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBotAction = async (botId: string, action: 'start' | 'stop' | 'restart' | 'delete') => {
    const actionMessages = {
      start: { loading: 'Iniciando bot...', success: 'Bot iniciado com sucesso!', error: 'Erro ao iniciar bot' },
      stop: { loading: 'Parando bot...', success: 'Bot parado com sucesso!', error: 'Erro ao parar bot' },
      restart: { loading: 'Reiniciando bot...', success: 'Bot reiniciado com sucesso!', error: 'Erro ao reiniciar bot' },
      delete: { loading: 'Removendo bot...', success: 'Bot removido com sucesso!', error: 'Erro ao remover bot' }
    };

    const messages = actionMessages[action];

    // Atualizar status imediatamente para feedback visual
    setBots(prevBots =>
      prevBots.map(bot =>
        bot.id === botId
          ? {
              ...bot,
              status: action === 'start' ? 'starting' :
                     action === 'stop' ? 'stopping' :
                     action === 'restart' ? 'restarting' : bot.status
            }
          : bot
      )
    );

    // Adicionar animação para restart
    if (action === 'restart') {
      setRestartingBots(prev => new Set([...prev, botId]));
    }

    // Mostrar toast de loading
    const loadingToast = toast.loading(messages.loading);

    try {
      const response = await fetch(`/api/bot-action`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ botId, action })
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success(messages.success, { id: loadingToast });

        // Remover animação após sucesso
        if (action === 'restart') {
          setTimeout(() => {
            setRestartingBots(prev => {
              const newSet = new Set(prev);
              newSet.delete(botId);
              return newSet;
            });
          }, 2000);
        }

        // Recarregar lista de bots após um pequeno delay para dar tempo da ação ser processada
        setTimeout(() => {
          if (user) {
            loadUserBots(user.id);
          }
        }, action === 'restart' ? 3000 : 1500); // Mais tempo para restart
      } else {
        toast.error(result.message || messages.error, { id: loadingToast });
        // Reverter status em caso de erro
        if (user) {
          loadUserBots(user.id);
        }
        // Remover animação em caso de erro
        if (action === 'restart') {
          setRestartingBots(prev => {
            const newSet = new Set(prev);
            newSet.delete(botId);
            return newSet;
          });
        }
      }
    } catch (error) {
      console.error('Erro ao executar ação:', error);
      toast.error(messages.error, { id: loadingToast });
      // Reverter status em caso de erro
      if (user) {
        loadUserBots(user.id);
      }
      // Remover animação em caso de erro
      if (action === 'restart') {
        setRestartingBots(prev => {
          const newSet = new Set(prev);
          newSet.delete(botId);
          return newSet;
        });
      }
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'text-green-400';
      case 'offline': return 'text-gray-400';
      case 'error': return 'text-red-400';
      case 'starting': return 'text-yellow-400';
      case 'stopping': return 'text-orange-400';
      case 'restarting': return 'text-blue-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusDot = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-500';
      case 'offline': return 'bg-gray-500';
      case 'error': return 'bg-red-500';
      case 'starting': return 'bg-yellow-500 animate-pulse';
      case 'stopping': return 'bg-orange-500 animate-pulse';
      case 'restarting': return 'bg-blue-500 animate-pulse';
      default: return 'bg-gray-500';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'online': return 'Online';
      case 'offline': return 'Offline';
      case 'error': return 'Erro';
      case 'starting': return 'Iniciando...';
      case 'stopping': return 'Parando...';
      case 'restarting': return 'Reiniciando...';
      default: return 'Desconhecido';
    }
  };

  if (isLoading) {
    return (
      <div className="dark min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <Bot className="w-12 h-12 text-primary mx-auto mb-4 animate-pulse" />
          <p className="text-muted-foreground">Carregando dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="dark min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>
            <p className="text-muted-foreground">
              Bem-vindo, <strong>{user?.username || 'Usuário'}</strong>! Gerencie seus bots de vendas
            </p>
          </div>
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => {
                // Navegar para página de extrato com token de segurança
                if (userFullData?.urlToken) {
                  navigate(`/extrato/${userFullData.urlToken}`);
                } else {
                  navigate('/extrato');
                }
              }}
              className="border-green-600 text-green-600 hover:bg-green-600 hover:text-white transition-all duration-200"
            >
              <CreditCard className="mr-2 h-4 w-4" />
              Extrato Nodex Pay
            </Button>
            <Button
              variant="ghost"
              onClick={() => {
                // Limpar todos os tokens e redirecionar
                setUser(null);
                setUserFullData(null);
                setBots([]);
                logout();
              }}
              className="text-gray-400 hover:text-red-400 hover:bg-transparent transition-colors duration-200"
            >
              <LogOut className="w-4 h-4" />
            </Button>
            <Button
              onClick={() => {
                const configUrl = userFullData?.urlToken
                  ? `/configure?username=${user?.username}&userId=${user?.id}&urlToken=${userFullData.urlToken}`
                  : `/configure?username=${user?.username}&userId=${user?.id}`;
                navigate(configUrl);
              }}
              className="bg-[#5865F2] hover:bg-[#4752C4]"
            >
              <Plus className="mr-2 h-4 w-4" />
              Criar Novo Bot
            </Button>
          </div>
        </div>

        {/* Estatísticas gerais */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="border-border bg-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total de Bots</p>
                  <p className="text-2xl font-bold text-foreground">{bots.length}</p>
                </div>
                <Bot className="w-8 h-8 text-primary" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-border bg-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Bots Online</p>
                  <p className="text-2xl font-bold text-green-400">
                    {bots.filter(bot => bot.status === 'online').length}
                  </p>
                </div>
                <Play className="w-8 h-8 text-green-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-border bg-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Servidores</p>
                  <p className="text-2xl font-bold text-blue-400">
                    {bots.reduce((total, bot) => total + bot.servers, 0)}
                  </p>
                </div>
                <Users className="w-8 h-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-border bg-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Vendas</p>
                  <p className="text-2xl font-bold text-yellow-400">
                    {bots.reduce((total, bot) => total + bot.sales, 0)}
                  </p>
                </div>
                <ShoppingCart className="w-8 h-8 text-yellow-400" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Lista de Bots */}
        <Card className="border-border bg-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bot className="w-5 h-5" />
              Aplicações de {user?.username || 'usuário'}
            </CardTitle>
            <CardDescription>
              Gerencie todos os seus bots de vendas em um só lugar
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {bots.length === 0 ? (
              <div className="text-center py-12">
                <Bot className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium text-foreground mb-2">Nenhuma aplicação configurada</h3>
                <p className="text-muted-foreground mb-4">
                  Crie sua primeira aplicação de vendas para começar
                </p>
                <Button
                  onClick={() => {
                    const configUrl = userFullData?.urlToken
                      ? `/configure?username=${user?.username}&userId=${user?.id}&urlToken=${userFullData.urlToken}`
                      : `/configure?username=${user?.username}&userId=${user?.id}`;
                    navigate(configUrl);
                  }}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Cadastrar uma nova aplicação
                </Button>
              </div>
            ) : (
              bots.map((bot) => (
                <div key={bot.id} className="bg-gray-800 border border-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center">
                        <Bot className="w-6 h-6 text-gray-400" />
                      </div>
                      <div>
                        <h4 className="font-medium text-white">{bot.name}</h4>
                        <div className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${getStatusDot(bot.status)}`}></div>
                          <span className={`text-sm ${getStatusColor(bot.status)}`}>
                            {getStatusText(bot.status)}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {/* Botão Ligar/Desligar */}
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            onClick={() => handleBotAction(bot.id, bot.status === 'online' ? 'stop' : 'start')}
                            className={bot.status === 'online'
                              ? "bg-red-600 hover:bg-red-700 text-white transition-all duration-200"
                              : "bg-green-600 hover:bg-green-700 text-white transition-all duration-200"
                            }
                          >
                            {bot.status === 'online' ? (
                              <Pause className="w-4 h-4" />
                            ) : (
                              <Play className="w-4 h-4" />
                            )}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          {bot.status === 'online' ? 'Parar bot' : 'Iniciar bot'}
                        </TooltipContent>
                      </Tooltip>

                      {/* Botão Reiniciar */}
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            onClick={() => handleBotAction(bot.id, 'restart')}
                            className="bg-blue-600 hover:bg-blue-700 text-white transition-all duration-200"
                          >
                            <RotateCcw className={`w-4 h-4 ${restartingBots.has(bot.id) ? 'animate-spin-slow' : ''}`} />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          Reiniciar bot
                        </TooltipContent>
                      </Tooltip>

                      {/* Botão Convidar */}
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            onClick={() => window.open(`https://discord.com/api/oauth2/authorize?client_id=${bot.clientId}&permissions=2048&scope=bot`, '_blank')}
                            className="bg-gray-600 hover:bg-gray-700 text-white transition-all duration-200"
                          >
                            <ExternalLink className="w-4 h-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          Convidar para servidor
                        </TooltipContent>
                      </Tooltip>

                      {/* Botão Apagar */}
                      <AlertDialog>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <AlertDialogTrigger asChild>
                              <Button
                                size="sm"
                                className="bg-red-600 hover:bg-red-700 text-white transition-all duration-200"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </AlertDialogTrigger>
                          </TooltipTrigger>
                          <TooltipContent>
                            Remover aplicação
                          </TooltipContent>
                        </Tooltip>
                        <AlertDialogContent className="bg-gray-900 border-gray-700">
                          <AlertDialogHeader>
                            <AlertDialogTitle className="flex items-center gap-2 text-red-400">
                              <AlertTriangle className="w-5 h-5" />
                              Confirmar Exclusão da Aplicação
                            </AlertDialogTitle>
                            <AlertDialogDescription className="text-gray-300 space-y-2">
                              <p>
                                Você está prestes a <strong className="text-red-400">excluir permanentemente</strong> a aplicação <strong className="text-white">"{bot.name}"</strong>.
                              </p>
                              <p className="text-sm text-gray-400">
                                Esta ação irá:
                              </p>
                              <ul className="text-sm text-gray-400 list-disc list-inside space-y-1 ml-4">
                                <li>Desconectar o bot de todos os servidores</li>
                                <li>Remover todas as configurações e dados</li>
                                <li>Cancelar todas as vendas em andamento</li>
                                <li>Apagar o histórico de transações</li>
                              </ul>
                              <p className="text-sm font-medium text-yellow-400 mt-3">
                                ⚠️ Esta ação não pode ser desfeita.
                              </p>
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter className="gap-2">
                            <AlertDialogCancel className="bg-gray-700 hover:bg-gray-600 text-white border-gray-600">
                              Cancelar
                            </AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleBotAction(bot.id, 'delete')}
                              className="bg-red-600 hover:bg-red-700 text-white"
                            >
                              <Trash2 className="w-4 h-4 mr-2" />
                              Excluir Aplicação
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm text-gray-300">
                    <div>
                      <span className="text-gray-400">ID:</span> {bot.clientId.slice(0, 8)}...
                    </div>
                    <div>
                      <span className="text-gray-400">Plano:</span> <span className="text-green-400">{bot.plan}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Servidores:</span> <span className="text-blue-400">{bot.servers}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Vendas:</span> <span className="text-yellow-400">{bot.sales}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Última venda:</span> {bot.lastSale}
                    </div>
                  </div>
                </div>
              ))
            )}
          </CardContent>
        </Card>
        </div>
      </div>
    </TooltipProvider>
  );
};

export default Dashboard;
