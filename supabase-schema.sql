-- ============================================
-- SCHEMA PARA SISTEMA DE LICENÇAS - SUPABASE
-- ============================================

-- 1. <PERSON>bela de Licenças
CREATE TABLE IF NOT EXISTS licenses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  key VARCHAR(50) UNIQUE NOT NULL,
  customer_email VARCHAR(255) NOT NULL,
  plan VARCHAR(50) NOT NULL CHECK (plan IN ('basic', 'pro')),
  plan_name VARCHAR(100) NOT NULL,
  stripe_session_id VARCHAR(255),
  stripe_customer_id VARCHAR(255),
  activated_at TIMESTAMP WITH TIME ZONE,
  activated_by <PERSON><PERSON><PERSON><PERSON>(255),
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. <PERSON><PERSON>a de Usuários (migração do users.json)
CREATE TABLE IF NOT EXISTS users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  discord_id VARCHAR(50) UNIQUE NOT NULL,
  license_key VARCHAR(50) REFERENCES licenses(key),
  activated_at TIMESTAMP WITH TIME ZONE,
  last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Tabela de Configurações do Bot (migração de config.json)
CREATE TABLE IF NOT EXISTS bot_configs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  discord_id VARCHAR(50) NOT NULL,
  config_type VARCHAR(100) NOT NULL, -- 'mercado_pago', 'channels', 'roles', etc.
  config_data JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(discord_id, config_type)
);

-- 4. Índices para Performance
CREATE INDEX IF NOT EXISTS idx_licenses_key ON licenses(key);
CREATE INDEX IF NOT EXISTS idx_licenses_email ON licenses(customer_email);
CREATE INDEX IF NOT EXISTS idx_licenses_plan ON licenses(plan);
CREATE INDEX IF NOT EXISTS idx_licenses_activated ON licenses(activated_at);
CREATE INDEX IF NOT EXISTS idx_users_discord_id ON users(discord_id);
CREATE INDEX IF NOT EXISTS idx_users_license_key ON users(license_key);
CREATE INDEX IF NOT EXISTS idx_bot_configs_discord_id ON bot_configs(discord_id);
CREATE INDEX IF NOT EXISTS idx_bot_configs_type ON bot_configs(config_type);

-- 5. Trigger para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_licenses_updated_at BEFORE UPDATE ON licenses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bot_configs_updated_at BEFORE UPDATE ON bot_configs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 6. RLS (Row Level Security) - Opcional para segurança
ALTER TABLE licenses ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE bot_configs ENABLE ROW LEVEL SECURITY;

-- Política para permitir acesso total via service key (para o bot)
CREATE POLICY "Allow full access via service key" ON licenses
    FOR ALL USING (true);

CREATE POLICY "Allow full access via service key" ON users
    FOR ALL USING (true);

CREATE POLICY "Allow full access via service key" ON bot_configs
    FOR ALL USING (true);

-- 7. Inserir dados de teste (opcional)
INSERT INTO licenses (key, customer_email, plan, plan_name, expires_at) VALUES
('NODEX-TEST-1234-5678-9012', '<EMAIL>', 'basic', 'Plano Basic (Teste)', NOW() + INTERVAL '30 days'),
('NODEX-DEMO-ABCD-EFGH-IJKL', '<EMAIL>', 'pro', 'Plano Pro (Teste)', NOW() + INTERVAL '30 days')
ON CONFLICT (key) DO NOTHING;

-- ============================================
-- VIEWS ÚTEIS PARA CONSULTAS
-- ============================================

-- View para estatísticas de licenças
CREATE OR REPLACE VIEW license_stats AS
SELECT 
    COUNT(*) as total_licenses,
    COUNT(CASE WHEN activated_at IS NOT NULL THEN 1 END) as activated_licenses,
    COUNT(CASE WHEN activated_at IS NULL THEN 1 END) as pending_licenses,
    COUNT(CASE WHEN plan = 'basic' THEN 1 END) as basic_licenses,
    COUNT(CASE WHEN plan = 'pro' THEN 1 END) as pro_licenses,
    COUNT(CASE WHEN expires_at > NOW() OR expires_at IS NULL THEN 1 END) as active_licenses,
    COUNT(CASE WHEN expires_at <= NOW() THEN 1 END) as expired_licenses
FROM licenses;

-- View para licenças com informações do usuário
CREATE OR REPLACE VIEW licenses_with_users AS
SELECT 
    l.*,
    u.discord_id,
    u.last_seen as user_last_seen
FROM licenses l
LEFT JOIN users u ON l.key = u.license_key;

-- ============================================
-- FUNÇÕES ÚTEIS
-- ============================================

-- Função para gerar chave de licença
CREATE OR REPLACE FUNCTION generate_license_key()
RETURNS VARCHAR(50) AS $$
DECLARE
    key_part1 VARCHAR(4);
    key_part2 VARCHAR(4);
    key_part3 VARCHAR(4);
    key_part4 VARCHAR(4);
    full_key VARCHAR(50);
BEGIN
    -- Gerar 4 partes de 4 caracteres cada
    key_part1 := UPPER(substring(md5(random()::text) from 1 for 4));
    key_part2 := UPPER(substring(md5(random()::text) from 1 for 4));
    key_part3 := UPPER(substring(md5(random()::text) from 1 for 4));
    key_part4 := UPPER(substring(md5(random()::text) from 1 for 4));
    
    full_key := 'NODEX-' || key_part1 || '-' || key_part2 || '-' || key_part3 || '-' || key_part4;
    
    RETURN full_key;
END;
$$ LANGUAGE plpgsql;

-- Função para validar licença
CREATE OR REPLACE FUNCTION validate_license(license_key VARCHAR(50))
RETURNS TABLE(
    valid BOOLEAN,
    license_data JSONB,
    error_message TEXT
) AS $$
DECLARE
    license_record licenses%ROWTYPE;
BEGIN
    -- Buscar licença
    SELECT * INTO license_record FROM licenses WHERE key = license_key;
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, NULL::JSONB, 'Licença não encontrada';
        RETURN;
    END IF;
    
    -- Verificar se expirou
    IF license_record.expires_at IS NOT NULL AND license_record.expires_at <= NOW() THEN
        RETURN QUERY SELECT false, 
            jsonb_build_object(
                'key', license_record.key,
                'plan', license_record.plan,
                'expired', true,
                'expires_at', license_record.expires_at
            ), 
            'Licença expirada';
        RETURN;
    END IF;
    
    -- Licença válida
    RETURN QUERY SELECT true,
        jsonb_build_object(
            'key', license_record.key,
            'plan', license_record.plan,
            'plan_name', license_record.plan_name,
            'customer_email', license_record.customer_email,
            'activated_at', license_record.activated_at,
            'activated_by', license_record.activated_by,
            'expires_at', license_record.expires_at,
            'created_at', license_record.created_at
        ),
        NULL;
END;
$$ LANGUAGE plpgsql;
