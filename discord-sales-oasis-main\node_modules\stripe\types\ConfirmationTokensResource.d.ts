// File generated from our OpenAPI spec

declare module 'stripe' {
  namespace Stripe {
    interface ConfirmationTokenRetrieveParams {
      /**
       * Specifies which fields in the response should be expanded.
       */
      expand?: Array<string>;
    }

    class ConfirmationTokensResource {
      /**
       * Retrieves an existing ConfirmationToken object
       */
      retrieve(
        id: string,
        params?: ConfirmationTokenRetrieveParams,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.ConfirmationToken>>;
      retrieve(
        id: string,
        options?: RequestOptions
      ): Promise<Stripe.Response<Stripe.ConfirmationToken>>;
    }
  }
}
