import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';

const AuthCallback = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processando login...');

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const error = searchParams.get('error');
        const token = searchParams.get('token'); // Token do servidor
        const urlToken = searchParams.get('urlToken'); // URL Token do servidor

        console.log('🔍 AuthCallback iniciado:', { error, token: token ? '[PRESENTE]' : null, urlToken });

        if (error) {
          throw new Error(`Erro de autorização: ${error}`);
        }

        setMessage('Processando autenticação...');

        let authData;

        // Se já temos os tokens da URL (vindo do servidor), usar eles
        if (token && urlToken) {
          authData = {
            sessionToken: token,
            urlToken: urlToken
          };

          // Limpar tokens antigos e salvar os novos
          localStorage.removeItem('sessionToken');
          localStorage.removeItem('jwtToken');
          localStorage.removeItem('urlToken');

          localStorage.setItem('sessionToken', token);
          localStorage.setItem('urlToken', urlToken);
        } else {
          // Se não temos tokens, algo deu errado
          throw new Error('Tokens de autenticação não encontrados');
        }

        setStatus('success');

        // Verificar se há um plano selecionado no localStorage
        const selectedPlan = localStorage.getItem('selectedPlan');
        console.log('📦 Plano selecionado:', selectedPlan);

        if (selectedPlan) {
          setMessage('Login realizado! Processando plano...');
        } else {
          setMessage('Login realizado com sucesso!');
        }

        if (selectedPlan) {
          setMessage('Processando plano selecionado...');

          // Limpar o plano do localStorage
          localStorage.removeItem('selectedPlan');

          if (selectedPlan === 'basic') {
            // Redirecionar para checkout Stripe para Basic
            try {
              const checkoutResponse = await fetch('/api/stripe/create-checkout-session', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'x-session-token': authData.sessionToken,
                },
                body: JSON.stringify({
                  priceId: 'price_1RdYzeRIzvHRKrKIhNsADY81', // Basic plan
                  plan: 'basic',
                  successUrl: `${window.location.origin}/success`,
                  cancelUrl: `${window.location.origin}/pricing`,
                }),
              });

              if (checkoutResponse.ok) {
                const { sessionId } = await checkoutResponse.json();

                // Redirecionar para Stripe Checkout
                const stripe = await import('@stripe/stripe-js').then(m =>
                  m.loadStripe('pk_test_51RdYpaRIzvHRKrKI5lpipw0j0NqSDi7VP4yn30XKkFt9yOICAeEeWCuHhwm3d8qb5mWmE0J4fn6pBOPAzNNOmu4U00lLhT8skQ')
                );

                if (stripe) {
                  await stripe.redirectToCheckout({ sessionId });
                  return;
                }
              }
            } catch (error) {
              console.error('Erro ao processar pagamento Basic:', error);
            }
          } else if (selectedPlan === 'pro') {
            // Redirecionar para checkout Stripe para Pro
            try {
              const checkoutResponse = await fetch('/api/stripe/create-checkout-session', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'x-session-token': authData.sessionToken,
                },
                body: JSON.stringify({
                  priceId: 'price_1RdZ0ORIzvHRKrKIt3O227mH', // Pro plan
                  plan: 'pro',
                  successUrl: `${window.location.origin}/success`,
                  cancelUrl: `${window.location.origin}/pricing`,
                }),
              });

              if (checkoutResponse.ok) {
                const { sessionId } = await checkoutResponse.json();

                // Redirecionar para Stripe Checkout
                const stripe = await import('@stripe/stripe-js').then(m =>
                  m.loadStripe('pk_test_51RdYpaRIzvHRKrKI5lpipw0j0NqSDi7VP4yn30XKkFt9yOICAeEeWCuHhwm3d8qb5mWmE0J4fn6pBOPAzNNOmu4U00lLhT8skQ')
                );

                if (stripe) {
                  await stripe.redirectToCheckout({ sessionId });
                  return;
                }
              }
            } catch (error) {
              console.error('Erro ao processar pagamento Pro:', error);
            }
          }
        } else {
          // Se não há plano selecionado, é login normal pelo header - ir para dashboard
          setMessage('Redirecionando para o dashboard...');
          console.log('🔄 Redirecionando para dashboard com urlToken:', authData.urlToken);
          setTimeout(() => {
            if (authData.urlToken) {
              console.log('✅ Navegando para:', `/dashboard/${authData.urlToken}`);
              navigate(`/dashboard/${authData.urlToken}`);
            } else {
              console.log('⚠️ Navegando para dashboard sem urlToken');
              navigate('/dashboard');
            }
          }, 1500);
          return;
        }

      } catch (error) {
        console.error('Erro no callback:', error);
        setStatus('error');
        setMessage(error instanceof Error ? error.message : 'Erro desconhecido');

        // Redirecionar para home após 3 segundos em caso de erro
        setTimeout(() => {
          navigate('/');
        }, 3000);
      }
    };

    handleCallback();
  }, [searchParams, navigate]);

  return (
    <div className="dark min-h-screen bg-background flex items-center justify-center">
      <div className="text-center space-y-4">
        {status === 'loading' && (
          <>
            <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
            <h2 className="text-xl font-semibold text-foreground">Processando login</h2>
            <p className="text-muted-foreground">Aguarde enquanto validamos sua conta Discord...</p>
          </>
        )}

        {status === 'success' && (
          <>
            <div className="h-8 w-8 mx-auto bg-green-600 rounded-full flex items-center justify-center">
              <svg className="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-foreground">Login realizado com sucesso!</h2>
            <p className="text-muted-foreground">{message}</p>
          </>
        )}

        {status === 'error' && (
          <>
            <div className="h-8 w-8 mx-auto bg-red-600 rounded-full flex items-center justify-center">
              <svg className="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-foreground">Erro no login</h2>
            <p className="text-muted-foreground">{message}</p>
            <p className="text-sm text-muted-foreground">Redirecionando para a página inicial...</p>
          </>
        )}
      </div>
    </div>
  );
};

export default AuthCallback;
